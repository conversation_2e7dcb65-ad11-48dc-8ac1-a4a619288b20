{"version": 3, "sources": ["src/pages/team/components/TeamListContent.tsx", "src/pages/team/index.tsx"], "sourcesContent": ["/**\n * 团队列表内容组件\n */\n\nimport {\n  CheckCircleOutlined,\n  CrownOutlined,\n  EyeOutlined,\n  SearchOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { history } from '@umijs/max';\nimport {\n  Avatar,\n  Button,\n  Empty,\n  Input,\n  List,\n  message,\n  Space,\n  Tag,\n  Typography,\n} from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { AuthService, TeamService } from '@/services';\nimport type { TeamDetailResponse } from '@/types/api';\nimport { getTeamIdFromCurrentToken, hasTeamInCurrentToken, getUserIdFromCurrentToken } from '@/utils/tokenUtils';\nimport { recordTeamSelection } from '@/utils/teamSelectionUtils';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\n\n\n\nconst TeamListContent: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\n  const [filteredTeams, setFilteredTeams] = useState<TeamDetailResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [currentTeamId, setCurrentTeamId] = useState<number | null>(null);\n\n  useEffect(() => {\n    fetchTeams();\n    // 获取当前团队ID\n    const teamId = getTeamIdFromCurrentToken();\n    setCurrentTeamId(teamId);\n  }, []);\n\n  useEffect(() => {\n    // 过滤团队列表\n    const filtered = teams.filter(\n      (team) =>\n        team.name.toLowerCase().includes(searchText.toLowerCase()) ||\n        (team.description &&\n          team.description.toLowerCase().includes(searchText.toLowerCase())),\n    );\n    setFilteredTeams(filtered);\n  }, [teams, searchText]);\n\n  const fetchTeams = async () => {\n    try {\n      setLoading(true);\n      const teamList = await TeamService.getUserTeams();\n      setTeams(teamList);\n    } catch (error) {\n      console.error('获取团队列表失败:', error);\n      message.error('获取团队列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateTeam = () => {\n    history.push('/personal-center');\n  };\n\n  const handleViewTeam = async (team: TeamDetailResponse) => {\n    try {\n      // 切换到该团队\n      const response = await AuthService.teamLogin({ teamId: team.id });\n\n      // 检查后端返回的团队选择成功标识\n      if (\n        response.teamSelectionSuccess &&\n        response.team &&\n        response.team.id === team.id\n      ) {\n        message.success(`已切换到团队：${team.name}`);\n        // 更新当前团队ID\n        setCurrentTeamId(team.id);\n        // 不需要刷新页面，让应用自然响应状态变化\n      } else {\n        console.error('团队切换响应异常，未返回正确的团队信息');\n        message.error('团队切换失败，请重试');\n      }\n    } catch (error: any) {\n      console.error('切换团队失败:', error);\n\n      // 响应拦截器已经处理了错误消息显示，这里只需要记录日志\n      // 如果是网络错误或其他非业务错误，才显示通用错误消息\n      if (!error.message || error.message === 'Failed to fetch') {\n        message.error('切换团队失败，请检查网络连接');\n      }\n    }\n  };\n\n  const handleSwitchTeam = async (team: TeamDetailResponse) => {\n    try {\n      const response = await AuthService.teamLogin({ teamId: team.id });\n\n      // 检查后端返回的团队选择成功标识\n      if (\n        response.teamSelectionSuccess &&\n        response.team &&\n        response.team.id === team.id\n      ) {\n        message.success(`已切换到团队：${team.name}`);\n\n        // 记录用户选择了这个团队\n        const currentUserId = getUserIdFromCurrentToken();\n        if (currentUserId) {\n          recordTeamSelection(currentUserId, team.id);\n        }\n\n        // 更新当前团队ID\n        setCurrentTeamId(team.id);\n\n        // 等待一段时间确保 Token 更新完成后再跳转\n        setTimeout(() => {\n          history.push('/');\n        }, 200);\n      } else {\n        console.error('团队切换响应异常，未返回正确的团队信息');\n        message.error('团队切换失败，请重试');\n      }\n    } catch (error: any) {\n      console.error('切换团队失败:', error);\n\n      // 响应拦截器已经处理了错误消息显示，这里只需要记录日志\n      // 如果是网络错误或其他非业务错误，才显示通用错误消息\n      if (!error.message || error.message === 'Failed to fetch') {\n        message.error('切换团队失败，请检查网络连接');\n      }\n    }\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: 16 }}>\n        <Search\n          placeholder=\"搜索团队名称或描述\"\n          allowClear\n          value={searchText}\n          onChange={(e) => setSearchText(e.target.value)}\n          style={{ width: 300 }}\n        />\n      </div>\n\n      {filteredTeams.length === 0 && !loading ? (\n        <Empty\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\n          description={\n            searchText ? '没有找到匹配的团队' : '您还没有加入任何团队'\n          }\n        >\n          {!searchText && (\n            <Button type=\"primary\" onClick={handleCreateTeam}>\n              创建第一个团队\n            </Button>\n          )}\n        </Empty>\n      ) : (\n        <List\n          loading={loading}\n          itemLayout=\"horizontal\"\n          dataSource={filteredTeams}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个团队`,\n          }}\n          renderItem={(team) => {\n            const isCurrentTeam = currentTeamId === team.id;\n            return (\n              <List.Item\n                style={{\n                  backgroundColor: isCurrentTeam ? '#f6ffed' : undefined,\n                  border: isCurrentTeam ? '1px solid #b7eb8f' : undefined,\n                  borderRadius: isCurrentTeam ? '8px' : undefined,\n                  padding: isCurrentTeam ? '16px' : undefined,\n                }}\n                actions={[\n                  <Button\n                    key=\"view\"\n                    type=\"text\"\n                    icon={<EyeOutlined />}\n                    onClick={() => handleViewTeam(team)}\n                  >\n                    查看详情\n                  </Button>,\n                  isCurrentTeam ? (\n                    <Tag\n                      key=\"current\"\n                      color=\"green\"\n                      icon={<CheckCircleOutlined />}\n                    >\n                      当前团队\n                    </Tag>\n                  ) : (\n                    <Button\n                      key=\"switch\"\n                      type=\"primary\"\n                      size=\"small\"\n                      onClick={() => handleSwitchTeam(team)}\n                    >\n                      进入团队\n                    </Button>\n                  ),\n                ]}\n              >\n                <List.Item.Meta\n                  avatar={\n                    <Avatar\n                      size={64}\n                      icon={<TeamOutlined />}\n                      style={{\n                        backgroundColor: isCurrentTeam ? '#52c41a' : '#1890ff',\n                        border: isCurrentTeam ? '2px solid #389e0d' : undefined,\n                      }}\n                    />\n                  }\n                  title={\n                    <Space>\n                      <Title level={4} style={{ margin: 0 }}>\n                        {team.name}\n                      </Title>\n                      {isCurrentTeam && (\n                        <Tag color=\"green\" icon={<CheckCircleOutlined />}>\n                          当前团队\n                        </Tag>\n                      )}\n                      {team.isCreator && (\n                        <Tag color=\"gold\" icon={<CrownOutlined />}>\n                          创建者\n                        </Tag>\n                      )}\n                    </Space>\n                  }\n                  description={\n                    <Space direction=\"vertical\" size=\"small\">\n                      {team.description && (\n                        <Text type=\"secondary\">{team.description}</Text>\n                      )}\n                      <Space>\n                        <Space size=\"small\">\n                          <UserOutlined />\n                          <Text type=\"secondary\">{team.memberCount} 名成员</Text>\n                        </Space>\n                        {team.assignedAt && (\n                          <Text type=\"secondary\">\n                            加入于 {new Date(team.assignedAt).toLocaleDateString()}\n                          </Text>\n                        )}\n                        <Text type=\"secondary\">\n                          创建于 {new Date(team.createdAt).toLocaleDateString()}\n                        </Text>\n                      </Space>\n                      <Space>\n                        <Text type=\"secondary\">状态：</Text>\n                        <Tag color={team.isActive ? 'green' : 'red'}>\n                          {team.isActive ? '启用' : '停用'}\n                        </Tag>\n                        {team.lastAccessTime && (\n                          <Text type=\"secondary\">\n                            最后访问：{new Date(team.lastAccessTime).toLocaleDateString()}\n                          </Text>\n                        )}\n                      </Space>\n                    </Space>\n                  }\n                />\n              </List.Item>\n            );\n          }}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default TeamListContent;\n", "/**\n * 我的团队页面 - 简化版，只保留团队列表和切换功能\n */\n\nimport { PageContainer } from '@ant-design/pro-components';\nimport React from 'react';\n\n// 导入团队列表组件\nimport TeamListContent from './components/TeamListContent';\n\nconst MyTeamsPage: React.FC = () => {\n  return (\n    <PageContainer title=\"我的团队\">\n      <TeamListContent />\n    </PageContainer>\n  );\n};\n\nexport default MyTeamsPage;\n"], "names": [], "mappings": "sxBA8BA,GAAM,CAAE,MAAA,CAAK,CAAE,KAAA,CAAI,CAAE,CAAG,SAAU,CAC5B,CAAE,OAAA,CAAM,CAAE,CAAG,SAAK,CAIlB,EAA4B,KAChC,GAAM,CAAC,EAAS,EAAW,CAAG,GAAA,UAAQ,EAAC,CAAA,GACjC,CAAC,EAAO,EAAS,CAAG,GAAA,UAAQ,EAAuB,EAAE,EACrD,CAAC,EAAe,EAAiB,CAAG,GAAA,UAAQ,EAAuB,EAAE,EACrE,CAAC,EAAY,EAAc,CAAG,GAAA,UAAQ,EAAC,IACvC,CAAC,EAAe,EAAiB,CAAG,GAAA,UAAQ,EAAgB,MAElE,GAAA,WAAS,EAAC,KACR,IAGA,EADe,GAAA,2BAAyB,KAE1C,EAAG,EAAE,EAEL,GAAA,WAAS,EAAC,KAQR,EANiB,EAAM,MAAM,CAC3B,AAAC,GACC,EAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,EAAW,WAAW,KACtD,EAAK,WAAW,EACf,EAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,EAAW,WAAW,MAGtE,EAAG,CAAC,EAAO,EAAW,EAEtB,IAAM,EAAa,UACjB,GAAI,CACF,EAAW,CAAA,GACX,IAAM,EAAW,MAAM,aAAW,CAAC,YAAY,GAC/C,EAAS,GACX,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,oDAAa,GAC3B,SAAO,CAAC,KAAK,CAAC,oDAChB,QAAU,CACR,EAAW,CAAA,GACb,CACF,EAMM,EAAiB,MAAO,IAC5B,GAAI,CAEF,IAAM,EAAW,MAAM,aAAW,CAAC,SAAS,CAAC,CAAE,OAAQ,EAAK,EAAE,AAAC,GAI7D,EAAS,oBAAoB,EAC7B,EAAS,IAAI,EACb,EAAS,IAAI,CAAC,EAAE,GAAK,EAAK,EAAE,EAE5B,SAAO,CAAC,OAAO,CAAC,CAAC,wDAAO,EAAE,EAAK,IAAI,CAAC,CAAC,EAErC,EAAiB,EAAK,EAAE,IAGxB,QAAQ,KAAK,CAAC,sHACd,SAAO,CAAC,KAAK,CAAC,iEAElB,CAAE,MAAO,EAAY,CACnB,QAAQ,KAAK,CAAC,wCAAW,GAIpB,EAAM,OAAO,EAAI,AAAkB,oBAAlB,EAAM,OAAO,EACjC,SAAO,CAAC,KAAK,CAAC,wFAElB,CACF,EAEM,EAAmB,MAAO,IAC9B,GAAI,CACF,IAAM,EAAW,MAAM,aAAW,CAAC,SAAS,CAAC,CAAE,OAAQ,EAAK,EAAE,AAAC,GAG/D,GACE,EAAS,oBAAoB,EAC7B,EAAS,IAAI,EACb,EAAS,IAAI,CAAC,EAAE,GAAK,EAAK,EAAE,CAC5B,CACA,SAAO,CAAC,OAAO,CAAC,CAAC,wDAAO,EAAE,EAAK,IAAI,CAAC,CAAC,EAGrC,IAAM,EAAgB,GAAA,2BAAyB,IAC3C,GACF,GAAA,qBAAmB,EAAC,EAAe,EAAK,EAAE,EAI5C,EAAiB,EAAK,EAAE,EAGxB,WAAW,KACT,SAAO,CAAC,IAAI,CAAC,KACf,EAAG,KACL,MACE,QAAQ,KAAK,CAAC,sHACd,SAAO,CAAC,KAAK,CAAC,gEAElB,CAAE,MAAO,EAAY,CACnB,QAAQ,KAAK,CAAC,wCAAW,GAIpB,EAAM,OAAO,EAAI,AAAkB,oBAAlB,EAAM,OAAO,EACjC,SAAO,CAAC,KAAK,CAAC,wFAElB,CACF,EAEA,MACE,WAAC,iBACC,UAAC,OAAI,MAAO,CAAE,aAAc,EAAG,WAC7B,UAAC,GACC,YAAY,yDACZ,UAAU,IACV,MAAO,EACP,SAAU,AAAC,GAAM,EAAc,EAAE,MAAM,CAAC,KAAK,EAC7C,MAAO,CAAE,MAAO,GAAI,MAIvB,AAAyB,IAAzB,EAAc,MAAM,EAAW,EAc9B,UAAC,SAAI,EACH,QAAS,EACT,WAAW,aACX,WAAY,EACZ,WAAY,CACV,gBAAiB,CAAA,EACjB,gBAAiB,CAAA,EACjB,UAAW,AAAC,GAAU,CAAC,SAAE,EAAE,EAAM,yBAAI,CAAC,AACxC,EACA,WAAY,AAAC,IACX,IAAM,EAAgB,IAAkB,EAAK,EAAE,CAC/C,MACE,UAAC,SAAI,CAAC,IAAI,EACR,MAAO,CACL,gBAAiB,EAAgB,UAAY,KAAA,EAC7C,OAAQ,EAAgB,oBAAsB,KAAA,EAC9C,aAAc,EAAgB,MAAQ,KAAA,EACtC,QAAS,EAAgB,OAAS,KAAA,CACpC,EACA,QAAS,CACP,UAAC,SAAM,EAEL,KAAK,OACL,KAAM,UAAC,SAAW,KAClB,QAAS,IAAM,EAAe,YAC/B,4BAJK,QAON,EACE,UAAC,SAAG,EAEF,MAAM,QACN,KAAM,UAAC,SAAmB,cAC3B,4BAHK,WAON,UAAC,SAAM,EAEL,KAAK,UACL,KAAK,QACL,QAAS,IAAM,EAAiB,YACjC,4BAJK,UAQT,UAED,UAAC,SAAI,CAAC,IAAI,CAAC,IAAI,EACb,OACE,UAAC,SAAM,EACL,KAAM,GACN,KAAM,UAAC,SAAY,KACnB,MAAO,CACL,gBAAiB,EAAgB,UAAY,UAC7C,OAAQ,EAAgB,oBAAsB,KAAA,CAChD,IAGJ,MACE,WAAC,SAAK,YACJ,UAAC,GAAM,MAAO,EAAG,MAAO,CAAE,OAAQ,CAAE,WACjC,EAAK,IAAI,GAEX,GACC,UAAC,SAAG,EAAC,MAAM,QAAQ,KAAM,UAAC,SAAmB,cAAK,6BAInD,EAAK,SAAS,EACb,UAAC,SAAG,EAAC,MAAM,OAAO,KAAM,UAAC,SAAa,cAAK,0BAMjD,YACE,WAAC,SAAK,EAAC,UAAU,WAAW,KAAK,kBAC9B,EAAK,WAAW,EACf,UAAC,GAAK,KAAK,qBAAa,EAAK,WAAW,GAE1C,WAAC,SAAK,YACJ,WAAC,SAAK,EAAC,KAAK,kBACV,UAAC,SAAY,KACb,WAAC,GAAK,KAAK,sBAAa,EAAK,WAAW,CAAC,4BAE1C,EAAK,UAAU,EACd,WAAC,GAAK,KAAK,sBAAY,sBAChB,IAAI,KAAK,EAAK,UAAU,EAAE,kBAAkB,MAGrD,WAAC,GAAK,KAAK,sBAAY,sBAChB,IAAI,KAAK,EAAK,SAAS,EAAE,kBAAkB,SAGpD,WAAC,SAAK,YACJ,UAAC,GAAK,KAAK,qBAAY,uBACvB,UAAC,SAAG,EAAC,MAAO,EAAK,QAAQ,CAAG,QAAU,eACnC,EAAK,QAAQ,CAAG,eAAO,iBAEzB,EAAK,cAAc,EAClB,WAAC,GAAK,KAAK,sBAAY,iCACf,IAAI,KAAK,EAAK,cAAc,EAAE,kBAAkB,gBASxE,IA5HF,UAAC,SAAK,EACJ,MAAO,SAAK,CAAC,sBAAsB,CACnC,YACE,EAAa,yDAAc,wEAG5B,CAAC,GACA,UAAC,SAAM,EAAC,KAAK,UAAU,QA9FR,KACvB,SAAO,CAAC,IAAI,CAAC,oBACf,WA4F4D,oDA0H9D,ECvRM,EAAwB,IAE1B,UAAC,eAAa,EAAC,MAAM,oCACnB,UAAC"}