{"version": 3, "sources": ["src/pages/Dashboard/index.tsx"], "sourcesContent": ["/**\n * 仪表板页面\n */\n\nimport { PageContainer } from '@ant-design/pro-components';\nimport React from 'react';\n\nconst Dashboard: React.FC = () => {\n  return (\n    <PageContainer title=\"仪表板\">\n      <div style={{\n        height: '400px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        color: '#999',\n        fontSize: '16px'\n      }}>\n        {/* 空白页面 */}\n      </div>\n    </PageContainer>\n  );\n};\n\nexport default Dashboard;\n"], "names": [], "mappings": "uQAwBA,+CAAA,0CAAA,EAjB4B,IAExB,UAAC,eAAa,EAAC,MAAM,8BACnB,UAAC,OAAI,MAAO,CACV,OAAQ,QACR,QAAS,OACT,WAAY,SACZ,eAAgB,SAChB,MAAO,OACP,SAAU,MACZ"}