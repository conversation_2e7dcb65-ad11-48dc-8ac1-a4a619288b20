(("undefined"!=typeof globalThis?globalThis:self)["makoChunk_teamauth-frontend"]=("undefined"!=typeof globalThis?globalThis:self)["makoChunk_teamauth-frontend"]||[]).push([["d9021e09"],{"2a5bef08":function(e,t,l){l.d(t,"__esModule",{value:!0}),l.e(t,{default:function(){return e4;}});var a=l("777fffbe"),r=l("852bbaa9"),n=l("87723398"),i=l("8cf722f6"),s=l("359d4dc3"),o=a._(s),d=l("c9c5d7af"),c=a._(d),u=l("f9353a87"),f=a._(u),m=l("2d45ae60"),x=r._(m),h=l("b79185ff"),g=l("3ad4ab70"),j=a._(g),p={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"},y=l("38eb1919"),b=a._(y),v=x.forwardRef(function(e,t){return x.createElement(b.default,(0,j.default)({},e,{ref:t,icon:p}));}),S=l("0f39226e"),C=a._(S),w=l("7f249ca9"),_=a._(w),k=l("5b8fb427"),T=a._(k),E=l("9fed9c3c"),z=a._(E),O=l("2ee0e178"),$=a._(O),M=l("8c3b80b4"),A=a._(M),R=l("e25ade57"),I=a._(R),D=l("0ba0ded5"),L=a._(D),N=l("83b006f8"),P=a._(N),B=l("3a3f20d4"),H=l("cb04eb22"),F=a._(H),q=l("a59ccbc3"),G=a._(q),J=l("4e1013a7"),K=a._(J),Q=l("a668ac1b"),V=a._(Q),W=l("f91ce2f8"),Y=a._(W),Z=l("ab3d3880"),U=a._(Z),X=l("4bf9a7fe"),ee=a._(X),et=l("b075e6df"),el=a._(et),ea=l("570fcdf0"),er=a._(ea),en=l("736360ad"),ei=a._(en),es=l("895d7523"),eo=a._(es),ed=l("46b7ac20"),ec=a._(ed),eu=l("3028ea74"),ef=a._(eu);l("20ade671");var em=l("b697552d"),ex=l("33732578"),eh=a._(ex),eg=l("2b9403f5"),ej=l("a838006a"),ep=a._(ej),ey=l("72511d4e"),eb=a._(ey),ev=l("311adbb5"),eS=l("b28bd1ac"),eC=a._(eS);let ew=e=>{let t;let{value:l,formatter:a,precision:r,decimalSeparator:n,groupSeparator:i="",prefixCls:s}=e;if("function"==typeof a)t=a(l);else{let e=String(l),a=e.match(/^(-?)(\d*)(\.(\d+))?$/);if(a&&"-"!==e){let e=a[1],l=a[2]||"0",o=a[4]||"";l=l.replace(/\B(?=(\d{3})+(?!\d))/g,i),"number"==typeof r&&(o=o.padEnd(r,"0").slice(0,r>0?r:0)),o&&(o=`${n}${o}`),t=[x.createElement("span",{key:"int",className:`${s}-content-value-int`},e,l),o&&x.createElement("span",{key:"decimal",className:`${s}-content-value-decimal`},o)];}else t=e;}return x.createElement("span",{className:`${s}-content-value`},t);};var e_=l("8cdc778b"),ek=l("1a2a1fdd"),eT=l("4469bd89");let eE=e=>{let{componentCls:t,marginXXS:l,padding:a,colorTextDescription:r,titleFontSize:n,colorTextHeading:i,contentFontSize:s,fontFamily:o}=e;return{[t]:Object.assign(Object.assign({},(0,e_.resetComponent)(e)),{[`${t}-title`]:{marginBottom:l,color:r,fontSize:n},[`${t}-skeleton`]:{paddingTop:a},[`${t}-content`]:{color:i,fontSize:s,fontFamily:o,[`${t}-content-value`]:{display:"inline-block",direction:"ltr"},[`${t}-content-prefix, ${t}-content-suffix`]:{display:"inline-block"},[`${t}-content-prefix`]:{marginInlineEnd:l},[`${t}-content-suffix`]:{marginInlineStart:l}}})};};var ez=(0,ek.genStyleHooks)("Statistic",e=>[eE((0,eT.mergeToken)(e,{}))],e=>{let{fontSizeHeading3:t,fontSize:l}=e;return{titleFontSize:l,contentFontSize:t};}),eO=this&&this.__rest||function(e,t){var l={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(l[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,a=Object.getOwnPropertySymbols(e);r<a.length;r++)0>t.indexOf(a[r])&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(l[a[r]]=e[a[r]]);return l;};let e$=x.forwardRef((e,t)=>{let{prefixCls:l,className:a,rootClassName:r,style:n,valueStyle:i,value:s=0,title:o,valueRender:d,prefix:c,suffix:u,loading:f=!1,formatter:m,precision:h,decimalSeparator:g=".",groupSeparator:j=",",onMouseEnter:p,onMouseLeave:y}=e,b=eO(e,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:v,direction:S,className:C,style:w}=(0,ev.useComponentConfig)("statistic"),_=v("statistic",l),[k,T,E]=ez(_),z=x.createElement(ew,{decimalSeparator:g,groupSeparator:j,prefixCls:_,formatter:m,precision:h,value:s}),O=(0,ep.default)(_,{[`${_}-rtl`]:"rtl"===S},C,a,r,T,E),$=x.useRef(null);x.useImperativeHandle(t,()=>({nativeElement:$.current}));let M=(0,eb.default)(b,{aria:!0,data:!0});return k(x.createElement("div",Object.assign({},M,{ref:$,className:O,style:Object.assign(Object.assign({},w),n),onMouseEnter:p,onMouseLeave:y}),o&&x.createElement("div",{className:`${_}-title`},o),x.createElement(eC.default,{paragraph:!1,loading:f,className:`${_}-skeleton`},x.createElement("div",{style:i,className:`${_}-content`},c&&x.createElement("span",{className:`${_}-content-prefix`},c),d?d(z):z,u&&x.createElement("span",{className:`${_}-content-suffix`},u)))));}),eM=[["Y",31536e6],["M",2592e6],["D",864e5],["H",36e5],["m",6e4],["s",1e3],["S",1]];var eA=this&&this.__rest||function(e,t){var l={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(l[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,a=Object.getOwnPropertySymbols(e);r<a.length;r++)0>t.indexOf(a[r])&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(l[a[r]]=e[a[r]]);return l;};let eR=e=>{let{value:t,format:l="HH:mm:ss",onChange:a,onFinish:r,type:n}=e,i=eA(e,["value","format","onChange","onFinish","type"]),s="countdown"===n,[o,d]=x.useState(null),c=(0,em.useEvent)(()=>{let e=Date.now(),l=new Date(t).getTime();return d({}),null==a||a(s?l-e:e-l),!s||!(l<e)||(null==r||r(),!1);});return x.useEffect(()=>{let e;let t=()=>{e=(0,eh.default)(()=>{c()&&t();});};return t(),()=>eh.default.cancel(e);},[t,s]),x.useEffect(()=>{d({});},[]),x.createElement(e$,Object.assign({},i,{value:t,valueRender:e=>(0,eg.cloneElement)(e,{title:void 0}),formatter:(e,t)=>o?function(e,t,l){let{format:a=""}=t,r=new Date(e).getTime(),n=Date.now();return function(e,t){let l=e,a=/\[[^\]]*]/g,r=(t.match(a)||[]).map(e=>e.slice(1,-1)),n=t.replace(a,"[]"),i=eM.reduce((e,[t,a])=>{if(e.includes(t)){let r=Math.floor(l/a);return l-=r*a,e.replace(RegExp(`${t}+`,"g"),e=>{let t=e.length;return r.toString().padStart(t,"0");});}return e;},n),s=0;return i.replace(a,()=>{let e=r[s];return s+=1,e;});}(l?Math.max(r-n,0):Math.max(n-r,0),a);}(e,Object.assign(Object.assign({},t),{format:l}),s):"-"}));};var eI=x.memo(e=>x.createElement(eR,Object.assign({},e,{type:"countdown"})));e$.Timer=eR,e$.Countdown=eI;var eD=l("cb8ff74d"),eL=a._(eD),eN={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M511.5 65C719.99 65 889 234.01 889 442.5S719.99 820 511.5 820 134 650.99 134 442.5 303.01 65 511.5 65m0 64C338.36 129 198 269.36 198 442.5S338.36 756 511.5 756 825 615.64 825 442.5 684.64 129 511.5 129M745 889v72H278v-72z"}}]},name:"harmony-o-s",theme:"outlined"},eP=x.forwardRef(function(e,t){return x.createElement(b.default,(0,j.default)({},e,{ref:t,icon:eN}));}),eB=l("51d9ac43"),eH=a._(eB),eF=l("fbc24883"),eq=a._(eF),eG=l("a40346af"),eJ=a._(eG),eK=l("2a4858ed"),eQ=a._(eK),eV=l("d54c4f8e");function eW(e){return void 0!==e.role?e.role===eV.TeamRole.TEAM_CREATOR:e.isCreator;}eV.TeamRole.TEAM_CREATOR,eV.TeamRole.TEAM_MEMBER;let{Text:eY}=f.default,{Option:eZ}=eq.default,eU=({teamId:e,isCreator:t,onMemberChange:l})=>{let[a,r]=(0,x.useState)(!0),[i,s]=(0,x.useState)([]),[d,c]=(0,x.useState)(""),[u,f]=(0,x.useState)([]),[m,g]=(0,x.useState)([]),[j,p]=(0,x.useState)("all");(0,x.useEffect)(()=>{y();},[e]),(0,x.useEffect)(()=>{if(!i||!Array.isArray(i)){f([]);return;}f(i.filter(e=>{if(!e||!e.name||!e.email)return!1;let t=!d||e.name.toLowerCase().includes(d.toLowerCase())||e.email.toLowerCase().includes(d.toLowerCase()),l="all"===j||"active"===j&&e.isActive||"inactive"===j&&!e.isActive||"creator"===j&&e.isCreator||"member"===j&&!e.isCreator;return t&&l;}));},[i,d,j]);let y=async()=>{try{r(!0);let e=await h.TeamService.getTeamMembers({current:1,pageSize:1e3});s((null==e?void 0:e.list)||[]);}catch(e){console.error("\u83B7\u53D6\u56E2\u961F\u6210\u5458\u5931\u8D25:",e),o.default.error("\u83B7\u53D6\u56E2\u961F\u6210\u5458\u5931\u8D25"),s([]);}finally{r(!1);}},b=e=>{if(e.isCreator){o.default.warning("\u4E0D\u80FD\u79FB\u9664\u56E2\u961F\u521B\u5EFA\u8005");return;}ei.default.confirm({title:"\u786E\u8BA4\u79FB\u9664\u6210\u5458",content:`\u{786E}\u{5B9A}\u{8981}\u{79FB}\u{9664}\u{6210}\u{5458} "${e.name}" \u{5417}\u{FF1F}`,okText:"\u786E\u8BA4",cancelText:"\u53D6\u6D88",onOk:async()=>{try{await h.TeamService.removeMember(e.id),o.default.success("\u6210\u5458\u79FB\u9664\u6210\u529F"),y(),null==l||l();}catch(e){console.error("\u79FB\u9664\u6210\u5458\u5931\u8D25:",e);}}});},v=[{title:"\u6210\u5458",dataIndex:"name",key:"name",render:(e,t)=>(0,n.jsxs)(ef.default,{children:[(0,n.jsx)(F.default,{size:"small",icon:(0,n.jsx)(P.default,{})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{children:e}),(0,n.jsx)("div",{style:{fontSize:12,color:"#999"},children:t.email})]})]})},{title:"\u89D2\u8272",key:"role",width:100,render:(e,t)=>{let l=eW(t);return(0,n.jsx)(eL.default,{color:eW(t)?"gold":"blue",icon:l?(0,n.jsx)(T.default,{}):(0,n.jsx)(P.default,{}),children:eW(t)?"\u56E2\u961F\u521B\u5EFA\u8005":"\u56E2\u961F\u6210\u5458"});}},{title:"\u72B6\u6001",dataIndex:"isActive",key:"status",width:80,render:e=>(0,n.jsx)(eL.default,{color:e?"green":"red",children:e?"\u6D3B\u8DC3":"\u505C\u7528"})},{title:"\u52A0\u5165\u65F6\u95F4",dataIndex:"assignedAt",key:"assignedAt",width:150,render:e=>new Date(e).toLocaleDateString()},{title:"\u6700\u540E\u8BBF\u95EE",dataIndex:"lastAccessTime",key:"lastAccessTime",width:150,render:e=>{let t=new Date(e),l=Math.floor((new Date().getTime()-t.getTime())/864e5),a="green";return l>7&&(a="orange"),l>30&&(a="red"),(0,n.jsx)(eQ.default,{title:t.toLocaleString(),children:(0,n.jsx)(eL.default,{color:a,children:0===l?"\u4ECA\u5929":`${l}\u{5929}\u{524D}`})});}},{title:"\u64CD\u4F5C",key:"action",width:120,render:(e,l)=>{if(!t||l.isCreator)return(0,n.jsx)(eY,{type:"secondary",children:"-"});let a=[{key:"remove",label:"\u79FB\u9664\u6210\u5458",icon:(0,n.jsx)(z.default,{}),danger:!0,onClick:()=>b(l)}];return(0,n.jsxs)(ef.default,{size:"small",children:[(0,n.jsx)(K.default,{type:"text",danger:!0,size:"small",icon:(0,n.jsx)(z.default,{}),onClick:()=>b(l),children:"\u79FB\u9664"}),(0,n.jsx)(U.default,{menu:{items:a},trigger:["click"],children:(0,n.jsx)(K.default,{type:"text",size:"small",icon:(0,n.jsx)(eP,{})})})]});}}];return(0,n.jsxs)(V.default,{title:(0,n.jsxs)(ef.default,{children:[(0,n.jsx)(eY,{strong:!0,children:"\u56E2\u961F\u6210\u5458"}),(0,n.jsx)(G.default,{count:u.length,showZero:!0})]}),extra:(0,n.jsxs)(ef.default,{children:[(0,n.jsxs)(eq.default,{value:j,onChange:p,style:{width:120},size:"small",children:[(0,n.jsx)(eZ,{value:"all",children:"\u5168\u90E8"}),(0,n.jsx)(eZ,{value:"active",children:"\u6D3B\u8DC3"}),(0,n.jsx)(eZ,{value:"inactive",children:"\u505C\u7528"}),(0,n.jsx)(eZ,{value:"creator",children:"\u521B\u5EFA\u8005"}),(0,n.jsx)(eZ,{value:"member",children:"\u6210\u5458"})]}),(0,n.jsx)(er.default,{placeholder:"\u641C\u7D22\u6210\u5458",prefix:(0,n.jsx)(eH.default,{}),value:d,onChange:e=>c(e.target.value),style:{width:200},size:"small"})]}),children:[m.length>0&&t&&(0,n.jsx)("div",{style:{marginBottom:16,padding:12,background:"#f5f5f5",borderRadius:6},children:(0,n.jsxs)(ef.default,{children:[(0,n.jsxs)(eY,{children:["\u5DF2\u9009\u62E9 ",m.length," \u540D\u6210\u5458"]}),(0,n.jsx)(K.default,{size:"small",danger:!0,icon:(0,n.jsx)(z.default,{}),onClick:()=>{let e=(i||[]).filter(e=>m.includes(e.id)&&!e.isCreator);if(0===e.length){o.default.warning("\u8BF7\u9009\u62E9\u8981\u79FB\u9664\u7684\u6210\u5458");return;}ei.default.confirm({title:"\u6279\u91CF\u79FB\u9664\u6210\u5458",content:`\u{786E}\u{5B9A}\u{8981}\u{79FB}\u{9664}\u{9009}\u{4E2D}\u{7684} ${e.length} \u{540D}\u{6210}\u{5458}\u{5417}\u{FF1F}`,okText:"\u786E\u8BA4",cancelText:"\u53D6\u6D88",onOk:async()=>{try{await Promise.all(e.map(e=>h.TeamService.removeMember(e.id))),o.default.success(`\u{6210}\u{529F}\u{79FB}\u{9664} ${e.length} \u{540D}\u{6210}\u{5458}`),g([]),y(),null==l||l();}catch(e){console.error("\u6279\u91CF\u79FB\u9664\u6210\u5458\u5931\u8D25:",e),o.default.error("\u6279\u91CF\u79FB\u9664\u5931\u8D25");}}});},children:"\u6279\u91CF\u79FB\u9664"}),(0,n.jsx)(K.default,{size:"small",onClick:()=>g([]),children:"\u53D6\u6D88\u9009\u62E9"})]})}),(0,n.jsx)(eJ.default,{columns:v,dataSource:u,rowKey:"id",loading:a,rowSelection:t?{selectedRowKeys:m,onChange:e=>{g(e);},getCheckboxProps:e=>({disabled:e.isCreator})}:void 0,pagination:{showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`\u{5171} ${e} \u{540D}\u{6210}\u{5458}`,pageSize:10}})]});},{Title:eX,Text:e0,Paragraph:e5}=f.default,{TextArea:e1}=er.default,e2=({teamDetail:e,loading:t,onRefresh:l,showBackButton:a=!1,onBack:r})=>{let[i,s]=(0,x.useState)(!1),[d,u]=(0,x.useState)(!1),[f,m]=(0,x.useState)(!1),[g]=el.default.useForm(),{setInitialState:j}=(0,B.useModel)("@@initialState"),p=e=>new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"}),y=()=>{if(!e)return"#1890ff";let t=e.memberCount;return t>=10?"#52c41a":t>=5?"#faad14":"#1890ff";},b=async t=>{if(e)try{u(!0),await h.TeamService.updateCurrentTeam(t),o.default.success("\u56E2\u961F\u4FE1\u606F\u66F4\u65B0\u6210\u529F"),s(!1),l();}catch(e){console.error("\u66F4\u65B0\u56E2\u961F\u5931\u8D25:",e),o.default.error("\u66F4\u65B0\u56E2\u961F\u5931\u8D25");}finally{u(!1);}};return t?(0,n.jsx)("div",{style:{textAlign:"center",padding:"50px 0"},children:(0,n.jsx)(c.default,{size:"large"})}):e?(0,n.jsxs)("div",{style:{padding:"0 24px"},children:[(0,n.jsx)(V.default,{style:{marginBottom:24,background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",border:"none",borderRadius:16},styles:{body:{padding:"32px"}},children:(0,n.jsxs)(ec.default,{align:"middle",justify:"space-between",children:[a&&(0,n.jsx)(Y.default,{children:(0,n.jsx)(K.default,{type:"text",icon:(0,n.jsx)(v,{}),onClick:()=>{r?r():B.history.push("/user/team-select");},style:{color:"rgba(255, 255, 255, 0.8)",fontSize:16,padding:"4px 8px"},children:"\u8FD4\u56DE"})}),(0,n.jsx)(Y.default,{flex:"auto",style:{display:"flex",justifyContent:"center",maxWidth:"60%"},children:(0,n.jsxs)(ef.default,{size:"large",align:"center",children:[(0,n.jsx)(F.default,{size:65,icon:(0,n.jsx)(L.default,{}),style:{backgroundColor:"rgba(255, 255, 255, 0.2)",color:"white",fontSize:28}}),(0,n.jsxs)("div",{children:[(0,n.jsxs)(ef.default,{align:"center",style:{marginBottom:8},children:[(0,n.jsx)(eX,{level:2,style:{color:"white",margin:0},children:e.name}),e.isCreator&&(0,n.jsx)(eL.default,{icon:(0,n.jsx)(T.default,{}),color:"gold",style:{fontSize:12},children:"\u7BA1\u7406\u5458"}),(0,n.jsx)(G.default,{color:y(),text:(0,n.jsx)(e0,{style:{color:"rgba(255, 255, 255, 0.8)"},children:(()=>{if(!e)return"\u5C0F\u578B\u56E2\u961F";let t=e.memberCount;return t>=10?"\u6D3B\u8DC3\u56E2\u961F":t>=5?"\u6B63\u5E38\u56E2\u961F":"\u5C0F\u578B\u56E2\u961F";})()})})]}),(0,n.jsx)(e5,{style:{color:"rgba(255, 255, 255, 0.8)",margin:0,textAlign:"center"},ellipsis:{rows:2},children:e.description||"\u8FD9\u4E2A\u56E2\u961F\u8FD8\u6CA1\u6709\u63CF\u8FF0"})]})]})}),(0,n.jsx)(Y.default,{children:e.isCreator&&(0,n.jsx)(U.default,{menu:{items:[{key:"edit",icon:(0,n.jsx)($.default,{}),label:"\u7F16\u8F91\u56E2\u961F",onClick:()=>{e&&(g.setFieldsValue({name:e.name,description:e.description||""}),s(!0));}},{key:"delete",icon:(0,n.jsx)(z.default,{}),label:"\u5220\u9664\u56E2\u961F",danger:!0,onClick:()=>{e&&ei.default.confirm({title:"\u786E\u8BA4\u5220\u9664\u56E2\u961F",content:`\u{786E}\u{5B9A}\u{8981}\u{5220}\u{9664}\u{56E2}\u{961F} "${e.name}" \u{5417}\u{FF1F}\u{6B64}\u{64CD}\u{4F5C}\u{4E0D}\u{53EF}\u{6062}\u{590D}\u{3002}`,icon:(0,n.jsx)(A.default,{}),okText:"\u786E\u8BA4\u5220\u9664",cancelText:"\u53D6\u6D88",okType:"danger",onOk:async()=>{try{m(!0),await h.TeamService.deleteCurrentTeam(),o.default.success("\u56E2\u961F\u5220\u9664\u6210\u529F"),j(e=>({...e,currentTeam:void 0})),B.history.push("/user/team-select");}catch(e){console.error("\u5220\u9664\u56E2\u961F\u5931\u8D25:",e),o.default.error("\u5220\u9664\u56E2\u961F\u5931\u8D25");}finally{m(!1);}}});}}]},trigger:["click"],placement:"bottomRight",children:(0,n.jsx)(K.default,{type:"text",icon:(0,n.jsx)(I.default,{}),style:{color:"rgba(255, 255, 255, 0.8)",fontSize:20,width:50,height:50}})})})]})}),(0,n.jsxs)(ec.default,{gutter:[16,16],style:{marginBottom:24},children:[(0,n.jsx)(Y.default,{xs:24,sm:12,md:6,children:(0,n.jsx)(V.default,{children:(0,n.jsx)(e$,{title:"\u56E2\u961F\u6210\u5458",value:e.memberCount,suffix:"\u4EBA",prefix:(0,n.jsx)(P.default,{style:{color:"#1890ff"}}),valueStyle:{color:"#1890ff"}})})}),(0,n.jsx)(Y.default,{xs:24,sm:12,md:6,children:(0,n.jsx)(V.default,{children:(0,n.jsx)(e$,{title:"\u521B\u5EFA\u65F6\u95F4",value:p(e.createdAt),prefix:(0,n.jsx)(C.default,{style:{color:"#52c41a"}}),valueStyle:{color:"#52c41a",fontSize:16}})})}),(0,n.jsx)(Y.default,{xs:24,sm:12,md:6,children:(0,n.jsx)(V.default,{children:(0,n.jsx)(e$,{title:"\u6700\u540E\u6D3B\u52A8",value:p(e.updatedAt),prefix:(0,n.jsx)(_.default,{style:{color:"#faad14"}}),valueStyle:{color:"#faad14",fontSize:16}})})}),(0,n.jsx)(Y.default,{xs:24,sm:12,md:6,children:(0,n.jsx)(V.default,{children:(0,n.jsxs)("div",{style:{textAlign:"center"},children:[(0,n.jsx)(e0,{type:"secondary",style:{fontSize:14},children:"\u56E2\u961F\u6D3B\u8DC3\u5EA6"}),(0,n.jsx)("div",{style:{marginTop:8},children:(0,n.jsx)(eo.default,{type:"circle",size:60,percent:Math.min(10*e.memberCount,100),strokeColor:y(),format:()=>(0,n.jsx)(e0,{style:{fontSize:12,color:y()},children:e.memberCount>=10?"\u9AD8":e.memberCount>=5?"\u4E2D":"\u4F4E"})})})]})})})]}),(0,n.jsx)(eU,{teamId:e.id,isCreator:e.isCreator,onMemberChange:l}),(0,n.jsx)(ei.default,{title:"\u7F16\u8F91\u56E2\u961F\u4FE1\u606F",open:i,onCancel:()=>s(!1),footer:null,width:600,children:(0,n.jsxs)(el.default,{form:g,layout:"vertical",onFinish:b,children:[(0,n.jsx)(el.default.Item,{label:"\u56E2\u961F\u540D\u79F0",name:"name",rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u56E2\u961F\u540D\u79F0"},{max:50,message:"\u56E2\u961F\u540D\u79F0\u4E0D\u80FD\u8D85\u8FC750\u4E2A\u5B57\u7B26"}],children:(0,n.jsx)(er.default,{placeholder:"\u8BF7\u8F93\u5165\u56E2\u961F\u540D\u79F0"})}),(0,n.jsx)(el.default.Item,{label:"\u56E2\u961F\u63CF\u8FF0",name:"description",rules:[{max:200,message:"\u56E2\u961F\u63CF\u8FF0\u4E0D\u80FD\u8D85\u8FC7200\u4E2A\u5B57\u7B26"}],children:(0,n.jsx)(e1,{rows:4,placeholder:"\u8BF7\u8F93\u5165\u56E2\u961F\u63CF\u8FF0\uFF08\u53EF\u9009\uFF09",showCount:!0,maxLength:200})}),(0,n.jsx)(el.default.Item,{style:{marginBottom:0,textAlign:"right"},children:(0,n.jsxs)(ef.default,{children:[(0,n.jsx)(K.default,{onClick:()=>s(!1),children:"\u53D6\u6D88"}),(0,n.jsx)(K.default,{type:"primary",htmlType:"submit",loading:d,children:"\u4FDD\u5B58"})]})})]})})]}):(0,n.jsx)(ee.default,{image:ee.default.PRESENTED_IMAGE_SIMPLE,description:"\u8BF7\u5148\u9009\u62E9\u4E00\u4E2A\u56E2\u961F"});},{Text:e8}=f.default,e4=()=>{let[e,t]=(0,x.useState)(!0),[l,a]=(0,x.useState)(null);(0,x.useEffect)(()=>{r();},[]);let r=async()=>{try{t(!0);let e=await h.TeamService.getCurrentTeamDetail();a(e);}catch(e){console.error("\u83B7\u53D6\u56E2\u961F\u8BE6\u60C5\u5931\u8D25:",e),o.default.error("\u83B7\u53D6\u56E2\u961F\u8BE6\u60C5\u5931\u8D25");}finally{t(!1);}};return e?(0,n.jsx)(i.PageContainer,{children:(0,n.jsx)("div",{style:{textAlign:"center",padding:"50px 0"},children:(0,n.jsx)(c.default,{size:"large"})})}):l?(0,n.jsx)(i.PageContainer,{style:{background:"#f5f5f5"},children:(0,n.jsx)(e2,{teamDetail:l,loading:e,onRefresh:r,showBackButton:!0})}):(0,n.jsx)(i.PageContainer,{children:(0,n.jsx)("div",{style:{textAlign:"center",padding:"50px 0"},children:(0,n.jsx)(e8,{type:"secondary",children:"\u56E2\u961F\u4FE1\u606F\u52A0\u8F7D\u5931\u8D25"})})});};}}]);
//# sourceMappingURL=d9021e09-async.f8b00de4.js.map