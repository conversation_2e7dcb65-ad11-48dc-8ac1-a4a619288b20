{"version": 3, "sources": ["src/utils/teamSelectionUtils.ts", "config/defaultSettings.ts", "src/components/InvitationStatus.tsx"], "sourcesContent": ["/**\n * 团队选择状态管理工具函数\n * 用于跟踪用户是否已经主动选择过团队，以区分初始登录状态和主动选择状态\n */\n\n// 团队选择历史的本地存储键\nconst TEAM_SELECTION_KEY = 'user_team_selection_history';\n\n/**\n * 获取用户的团队选择历史\n * @param userId 用户ID\n * @returns 用户选择过的团队ID集合\n */\nexport const getUserTeamSelectionHistory = (userId: number): Set<number> => {\n  try {\n    const history = localStorage.getItem(`${TEAM_SELECTION_KEY}_${userId}`);\n    if (history) {\n      return new Set(JSON.parse(history));\n    }\n  } catch (error) {\n    console.error('获取团队选择历史失败:', error);\n  }\n  return new Set();\n};\n\n/**\n * 记录用户选择了某个团队\n * @param userId 用户ID\n * @param teamId 团队ID\n */\nexport const recordTeamSelection = (userId: number, teamId: number): void => {\n  try {\n    const history = getUserTeamSelectionHistory(userId);\n    history.add(teamId);\n    localStorage.setItem(`${TEAM_SELECTION_KEY}_${userId}`, JSON.stringify([...history]));\n    console.log(`记录团队选择: 用户${userId}选择了团队${teamId}`);\n  } catch (error) {\n    console.error('记录团队选择历史失败:', error);\n  }\n};\n\n/**\n * 检查用户是否曾经选择过某个团队\n * @param userId 用户ID\n * @param teamId 团队ID\n * @returns 是否曾经选择过该团队\n */\nexport const hasUserSelectedTeam = (userId: number, teamId: number): boolean => {\n  const history = getUserTeamSelectionHistory(userId);\n  return history.has(teamId);\n};\n\n/**\n * 清除用户的团队选择历史（用于注销等场景）\n * @param userId 用户ID\n */\nexport const clearUserTeamSelectionHistory = (userId: number): void => {\n  try {\n    localStorage.removeItem(`${TEAM_SELECTION_KEY}_${userId}`);\n    console.log(`清除用户${userId}的团队选择历史`);\n  } catch (error) {\n    console.error('清除团队选择历史失败:', error);\n  }\n};\n\n/**\n * 获取所有用户的团队选择历史键（用于调试）\n * @returns 所有相关的localStorage键\n */\nexport const getAllTeamSelectionKeys = (): string[] => {\n  const keys: string[] = [];\n  for (let i = 0; i < localStorage.length; i++) {\n    const key = localStorage.key(i);\n    if (key && key.startsWith(TEAM_SELECTION_KEY)) {\n      keys.push(key);\n    }\n  }\n  return keys;\n};\n", "import type { ProLayoutProps } from '@ant-design/pro-components';\n\nconst Settings: ProLayoutProps & {\n  pwa?: boolean;\n  logo?: string;\n} = {\n  navTheme: 'light',\n  colorPrimary: '#1890ff',\n  layout: 'side',\n  contentWidth: 'Fluid',\n  fixedHeader: false,\n  fixSiderbar: true,\n  colorWeak: false,\n  title: '团队协作管理系统',\n  pwa: false,\n  logo: '/logo.svg',\n  iconfontUrl: '',\n  token: {},\n};\n\nexport default Settings;\n", "/**\n * 邀请状态显示组件\n */\n\nimport React from 'react';\nimport { Tag } from 'antd';\nimport {\n  ClockCircleOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  ExclamationCircleOutlined,\n  StopOutlined,\n} from '@ant-design/icons';\nimport { InvitationStatus } from '@/types/api';\n\ninterface InvitationStatusProps {\n  status: InvitationStatus;\n  isExpired?: boolean;\n}\n\n/**\n * 邀请状态组件\n */\nconst InvitationStatusComponent: React.FC<InvitationStatusProps> = ({ \n  status, \n  isExpired = false \n}) => {\n  // 如果已过期，优先显示过期状态\n  if (isExpired && status === InvitationStatus.PENDING) {\n    return (\n      <Tag icon={<ExclamationCircleOutlined />} color=\"orange\">\n        已过期\n      </Tag>\n    );\n  }\n\n  switch (status) {\n    case InvitationStatus.PENDING:\n      return (\n        <Tag icon={<ClockCircleOutlined />} color=\"blue\">\n          待确认\n        </Tag>\n      );\n    case InvitationStatus.ACCEPTED:\n      return (\n        <Tag icon={<CheckCircleOutlined />} color=\"green\">\n          已接受\n        </Tag>\n      );\n    case InvitationStatus.REJECTED:\n      return (\n        <Tag icon={<CloseCircleOutlined />} color=\"red\">\n          已拒绝\n        </Tag>\n      );\n    case InvitationStatus.EXPIRED:\n      return (\n        <Tag icon={<ExclamationCircleOutlined />} color=\"orange\">\n          已过期\n        </Tag>\n      );\n    case InvitationStatus.CANCELLED:\n      return (\n        <Tag icon={<StopOutlined />} color=\"default\">\n          已取消\n        </Tag>\n      );\n    default:\n      return (\n        <Tag color=\"default\">\n          未知状态\n        </Tag>\n      );\n  }\n};\n\nexport default InvitationStatusComponent;\n"], "names": [], "mappings": "sQAaa,2BAA2B,mBAA3B,IAkCA,mBAAmB,mBAAnB,IAjBA,mBAAmB,mBAAnB,MAxBb,IAAM,EAAqB,8BAOd,EAA8B,AAAC,IAC1C,GAAI,CACF,IAAM,EAAU,aAAa,OAAO,CAAC,CAAC,EAAE,EAAmB,CAAC,EAAE,EAAO,CAAC,EACtE,GAAI,EACF,OAAO,IAAI,IAAI,KAAK,KAAK,CAAC,IAE9B,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,gEAAe,GAC/B,CACA,OAAO,IAAI,IACb,EAOa,EAAsB,CAAC,EAAgB,KAClD,GAAI,CACF,IAAM,EAAU,EAA4B,GAC5C,EAAQ,GAAG,CAAC,GACZ,aAAa,OAAO,CAAC,CAAC,EAAE,EAAmB,CAAC,EAAE,EAAO,CAAC,CAAE,KAAK,SAAS,CAAC,IAAI,EAAQ,GACnF,QAAQ,GAAG,CAAC,CAAC,kEAAU,EAAE,EAAO,wCAAK,EAAE,EAAO,CAAC,EACjD,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,gEAAe,GAC/B,CACF,EAQa,EAAsB,CAAC,EAAgB,IAE3C,AADS,EAA4B,GAC7B,GAAG,CAAC,kFC7BrB,+CAAA,UAAA,EAfI,CACF,SAAU,QACV,aAAc,UACd,OAAQ,OACR,aAAc,QACd,YAAa,CAAA,EACb,YAAa,CAAA,EACb,UAAW,CAAA,EACX,MAAO,mDACP,IAAK,CAAA,EACL,KAAM,YACN,YAAa,GACb,MAAO,CAAC,CACV,+EC0DA,+CAAA,4KA/DiC,YA+DjC,EArDmE,CAAC,CAClE,OAAA,CAAM,CACN,UAAA,EAAY,CAAA,CAAK,CAClB,IAEC,GAAI,GAAa,IAAW,kBAAgB,CAAC,OAAO,CAClD,MACE,UAAC,SAAG,EAAC,KAAM,UAAC,SAAyB,KAAK,MAAM,kBAAS,uBAM7D,OAAQ,GACN,KAAK,kBAAgB,CAAC,OAAO,CAC3B,MACE,UAAC,SAAG,EAAC,KAAM,UAAC,SAAmB,KAAK,MAAM,gBAAO,uBAIrD,KAAK,kBAAgB,CAAC,QAAQ,CAC5B,MACE,UAAC,SAAG,EAAC,KAAM,UAAC,SAAmB,KAAK,MAAM,iBAAQ,uBAItD,KAAK,kBAAgB,CAAC,QAAQ,CAC5B,MACE,UAAC,SAAG,EAAC,KAAM,UAAC,SAAmB,KAAK,MAAM,eAAM,uBAIpD,KAAK,kBAAgB,CAAC,OAAO,CAC3B,MACE,UAAC,SAAG,EAAC,KAAM,UAAC,SAAyB,KAAK,MAAM,kBAAS,uBAI7D,KAAK,kBAAgB,CAAC,SAAS,CAC7B,MACE,UAAC,SAAG,EAAC,KAAM,UAAC,SAAY,KAAK,MAAM,mBAAU,uBAIjD,QACE,MACE,UAAC,SAAG,EAAC,MAAM,mBAAU,6BAI3B,CACF"}