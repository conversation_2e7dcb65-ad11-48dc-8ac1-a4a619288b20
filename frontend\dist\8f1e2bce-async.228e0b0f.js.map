{"version": 3, "sources": ["node_modules/@ant-design/icons-svg/es/asn/InfoCircleOutlined.js", "node_modules/@ant-design/icons/es/icons/InfoCircleOutlined.js", "node_modules/antd/es/popconfirm/style/index.js", "node_modules/antd/es/popconfirm/PurePanel.js", "node_modules/antd/es/popconfirm/index.js", "node_modules/@ant-design/icons-svg/es/asn/UserAddOutlined.js", "node_modules/@ant-design/icons/es/icons/UserAddOutlined.js", "src/pages/team-management/components/TeamMemberManagement.tsx", "src/pages/team-management/index.tsx"], "sourcesContent": ["// This icon file is generated automatically.\nvar InfoCircleOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M464 336a48 48 0 1096 0 48 48 0 10-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z\" } }] }, \"name\": \"info-circle\", \"theme\": \"outlined\" };\nexport default InfoCircleOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport InfoCircleOutlinedSvg from \"@ant-design/icons-svg/es/asn/InfoCircleOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar InfoCircleOutlined = function InfoCircleOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: InfoCircleOutlinedSvg\n  }));\n};\n\n/**![info-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnoiIC8+PHBhdGggZD0iTTQ2NCAzMzZhNDggNDggMCAxMDk2IDAgNDggNDggMCAxMC05NiAwem03MiAxMTJoLTQ4Yy00LjQgMC04IDMuNi04IDh2MjcyYzAgNC40IDMuNiA4IDggOGg0OGM0LjQgMCA4LTMuNiA4LThWNDU2YzAtNC40LTMuNi04LTgtOHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(InfoCircleOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'InfoCircleOutlined';\n}\nexport default RefIcon;", "import { genStyleHooks } from '../../theme/internal';\n// =============================== Base ===============================\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    antCls,\n    zIndexPopup,\n    colorText,\n    colorWarning,\n    marginXXS,\n    marginXS,\n    fontSize,\n    fontWeightStrong,\n    colorTextHeading\n  } = token;\n  return {\n    [componentCls]: {\n      zIndex: zIndexPopup,\n      [`&${antCls}-popover`]: {\n        fontSize\n      },\n      [`${componentCls}-message`]: {\n        marginBottom: marginXS,\n        display: 'flex',\n        flexWrap: 'nowrap',\n        alignItems: 'start',\n        [`> ${componentCls}-message-icon ${iconCls}`]: {\n          color: colorWarning,\n          fontSize,\n          lineHeight: 1,\n          marginInlineEnd: marginXS\n        },\n        [`${componentCls}-title`]: {\n          fontWeight: fontWeightStrong,\n          color: colorTextHeading,\n          '&:only-child': {\n            fontWeight: 'normal'\n          }\n        },\n        [`${componentCls}-description`]: {\n          marginTop: marginXXS,\n          color: colorText\n        }\n      },\n      [`${componentCls}-buttons`]: {\n        textAlign: 'end',\n        whiteSpace: 'nowrap',\n        button: {\n          marginInlineStart: marginXS\n        }\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => {\n  const {\n    zIndexPopupBase\n  } = token;\n  return {\n    zIndexPopup: zIndexPopupBase + 60\n  };\n};\nexport default genStyleHooks('Popconfirm', token => genBaseStyle(token), prepareComponentToken, {\n  resetStyle: false\n});", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport ExclamationCircleFilled from \"@ant-design/icons/es/icons/ExclamationCircleFilled\";\nimport classNames from 'classnames';\nimport ActionButton from '../_util/ActionButton';\nimport { getRenderPropValue } from '../_util/getRenderPropValue';\nimport Button from '../button';\nimport { convertLegacyProps } from '../button/buttonHelpers';\nimport { ConfigContext } from '../config-provider';\nimport { useLocale } from '../locale';\nimport defaultLocale from '../locale/en_US';\nimport PopoverPurePanel from '../popover/PurePanel';\nimport useStyle from './style';\nexport const Overlay = props => {\n  const {\n    prefixCls,\n    okButtonProps,\n    cancelButtonProps,\n    title,\n    description,\n    cancelText,\n    okText,\n    okType = 'primary',\n    icon = /*#__PURE__*/React.createElement(ExclamationCircleFilled, null),\n    showCancel = true,\n    close,\n    onConfirm,\n    onCancel,\n    onPopupClick\n  } = props;\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const [contextLocale] = useLocale('Popconfirm', defaultLocale.Popconfirm);\n  const titleNode = getRenderPropValue(title);\n  const descriptionNode = getRenderPropValue(description);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-inner-content`,\n    onClick: onPopupClick\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-message`\n  }, icon && /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-message-icon`\n  }, icon), /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-message-text`\n  }, titleNode && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-title`\n  }, titleNode), descriptionNode && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-description`\n  }, descriptionNode))), /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-buttons`\n  }, showCancel && (/*#__PURE__*/React.createElement(Button, Object.assign({\n    onClick: onCancel,\n    size: \"small\"\n  }, cancelButtonProps), cancelText || (contextLocale === null || contextLocale === void 0 ? void 0 : contextLocale.cancelText))), /*#__PURE__*/React.createElement(ActionButton, {\n    buttonProps: Object.assign(Object.assign({\n      size: 'small'\n    }, convertLegacyProps(okType)), okButtonProps),\n    actionFn: onConfirm,\n    close: close,\n    prefixCls: getPrefixCls('btn'),\n    quitOnNullishReturnValue: true,\n    emitEvent: true\n  }, okText || (contextLocale === null || contextLocale === void 0 ? void 0 : contextLocale.okText))));\n};\nconst PurePanel = props => {\n  const {\n      prefixCls: customizePrefixCls,\n      placement,\n      className,\n      style\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"placement\", \"className\", \"style\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('popconfirm', customizePrefixCls);\n  const [wrapCSSVar] = useStyle(prefixCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(PopoverPurePanel, {\n    placement: placement,\n    className: classNames(prefixCls, className),\n    style: style,\n    content: /*#__PURE__*/React.createElement(Overlay, Object.assign({\n      prefixCls: prefixCls\n    }, restProps))\n  }));\n};\nexport default PurePanel;", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport ExclamationCircleFilled from \"@ant-design/icons/es/icons/ExclamationCircleFilled\";\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport omit from \"rc-util/es/omit\";\nimport { useComponentConfig } from '../config-provider/context';\nimport Popover from '../popover';\nimport PurePanel, { Overlay } from './PurePanel';\nimport useStyle from './style';\nconst InternalPopconfirm = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a, _b;\n  const {\n      prefixCls: customizePrefixCls,\n      placement = 'top',\n      trigger = 'click',\n      okType = 'primary',\n      icon = /*#__PURE__*/React.createElement(ExclamationCircleFilled, null),\n      children,\n      overlayClassName,\n      onOpenChange,\n      onVisibleChange,\n      overlayStyle,\n      styles,\n      classNames: popconfirmClassNames\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"placement\", \"trigger\", \"okType\", \"icon\", \"children\", \"overlayClassName\", \"onOpenChange\", \"onVisibleChange\", \"overlayStyle\", \"styles\", \"classNames\"]);\n  const {\n    getPrefixCls,\n    className: contextClassName,\n    style: contextStyle,\n    classNames: contextClassNames,\n    styles: contextStyles\n  } = useComponentConfig('popconfirm');\n  const [open, setOpen] = useMergedState(false, {\n    value: (_a = props.open) !== null && _a !== void 0 ? _a : props.visible,\n    defaultValue: (_b = props.defaultOpen) !== null && _b !== void 0 ? _b : props.defaultVisible\n  });\n  const settingOpen = (value, e) => {\n    setOpen(value, true);\n    onVisibleChange === null || onVisibleChange === void 0 ? void 0 : onVisibleChange(value);\n    onOpenChange === null || onOpenChange === void 0 ? void 0 : onOpenChange(value, e);\n  };\n  const close = e => {\n    settingOpen(false, e);\n  };\n  const onConfirm = e => {\n    var _a;\n    return (_a = props.onConfirm) === null || _a === void 0 ? void 0 : _a.call(this, e);\n  };\n  const onCancel = e => {\n    var _a;\n    settingOpen(false, e);\n    (_a = props.onCancel) === null || _a === void 0 ? void 0 : _a.call(this, e);\n  };\n  const onInternalOpenChange = (value, e) => {\n    const {\n      disabled = false\n    } = props;\n    if (disabled) {\n      return;\n    }\n    settingOpen(value, e);\n  };\n  const prefixCls = getPrefixCls('popconfirm', customizePrefixCls);\n  const rootClassNames = classNames(prefixCls, contextClassName, overlayClassName, contextClassNames.root, popconfirmClassNames === null || popconfirmClassNames === void 0 ? void 0 : popconfirmClassNames.root);\n  const bodyClassNames = classNames(contextClassNames.body, popconfirmClassNames === null || popconfirmClassNames === void 0 ? void 0 : popconfirmClassNames.body);\n  const [wrapCSSVar] = useStyle(prefixCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(Popover, Object.assign({}, omit(restProps, ['title']), {\n    trigger: trigger,\n    placement: placement,\n    onOpenChange: onInternalOpenChange,\n    open: open,\n    ref: ref,\n    classNames: {\n      root: rootClassNames,\n      body: bodyClassNames\n    },\n    styles: {\n      root: Object.assign(Object.assign(Object.assign(Object.assign({}, contextStyles.root), contextStyle), overlayStyle), styles === null || styles === void 0 ? void 0 : styles.root),\n      body: Object.assign(Object.assign({}, contextStyles.body), styles === null || styles === void 0 ? void 0 : styles.body)\n    },\n    content: /*#__PURE__*/React.createElement(Overlay, Object.assign({\n      okType: okType,\n      icon: icon\n    }, props, {\n      prefixCls: prefixCls,\n      close: close,\n      onConfirm: onConfirm,\n      onCancel: onCancel\n    })),\n    \"data-popover-inject\": true\n  }), children));\n});\nconst Popconfirm = InternalPopconfirm;\n// We don't care debug panel\n/* istanbul ignore next */\nPopconfirm._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nif (process.env.NODE_ENV !== 'production') {\n  Popconfirm.displayName = 'Popconfirm';\n}\nexport default Popconfirm;", "// This icon file is generated automatically.\nvar UserAddOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M678.3 642.4c24.2-13 51.9-20.4 81.4-20.4h.1c3 0 4.4-3.6 2.2-5.6a371.67 371.67 0 00-103.7-65.8c-.4-.2-.8-.3-1.2-.5C719.2 505 759.6 431.7 759.6 349c0-137-110.8-248-247.5-248S264.7 212 264.7 349c0 82.7 40.4 156 102.6 201.1-.4.2-.8.3-1.2.5-44.7 18.9-84.8 46-119.3 80.6a373.42 373.42 0 00-80.4 119.5A373.6 373.6 0 00137 888.8a8 8 0 008 8.2h59.9c4.3 0 7.9-3.5 8-7.8 2-77.2 32.9-149.5 87.6-204.3C357 628.2 432.2 597 512.2 597c56.7 0 111.1 15.7 158 45.1a8.1 8.1 0 008.1.3zM512.2 521c-45.8 0-88.9-17.9-121.4-50.4A171.2 171.2 0 01340.5 349c0-45.9 17.9-89.1 50.3-121.6S466.3 177 512.2 177s88.9 17.9 121.4 50.4A171.2 171.2 0 01683.9 349c0 45.9-17.9 89.1-50.3 121.6C601.1 503.1 558 521 512.2 521zM880 759h-84v-84c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v84h-84c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h84v84c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-84h84c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z\" } }] }, \"name\": \"user-add\", \"theme\": \"outlined\" };\nexport default UserAddOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport UserAddOutlinedSvg from \"@ant-design/icons-svg/es/asn/UserAddOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar UserAddOutlined = function UserAddOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: UserAddOutlinedSvg\n  }));\n};\n\n/**![user-add](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY3OC4zIDY0Mi40YzI0LjItMTMgNTEuOS0yMC40IDgxLjQtMjAuNGguMWMzIDAgNC40LTMuNiAyLjItNS42YTM3MS42NyAzNzEuNjcgMCAwMC0xMDMuNy02NS44Yy0uNC0uMi0uOC0uMy0xLjItLjVDNzE5LjIgNTA1IDc1OS42IDQzMS43IDc1OS42IDM0OWMwLTEzNy0xMTAuOC0yNDgtMjQ3LjUtMjQ4UzI2NC43IDIxMiAyNjQuNyAzNDljMCA4Mi43IDQwLjQgMTU2IDEwMi42IDIwMS4xLS40LjItLjguMy0xLjIuNS00NC43IDE4LjktODQuOCA0Ni0xMTkuMyA4MC42YTM3My40MiAzNzMuNDIgMCAwMC04MC40IDExOS41QTM3My42IDM3My42IDAgMDAxMzcgODg4LjhhOCA4IDAgMDA4IDguMmg1OS45YzQuMyAwIDcuOS0zLjUgOC03LjggMi03Ny4yIDMyLjktMTQ5LjUgODcuNi0yMDQuM0MzNTcgNjI4LjIgNDMyLjIgNTk3IDUxMi4yIDU5N2M1Ni43IDAgMTExLjEgMTUuNyAxNTggNDUuMWE4LjEgOC4xIDAgMDA4LjEuM3pNNTEyLjIgNTIxYy00NS44IDAtODguOS0xNy45LTEyMS40LTUwLjRBMTcxLjIgMTcxLjIgMCAwMTM0MC41IDM0OWMwLTQ1LjkgMTcuOS04OS4xIDUwLjMtMTIxLjZTNDY2LjMgMTc3IDUxMi4yIDE3N3M4OC45IDE3LjkgMTIxLjQgNTAuNEExNzEuMiAxNzEuMiAwIDAxNjgzLjkgMzQ5YzAgNDUuOS0xNy45IDg5LjEtNTAuMyAxMjEuNkM2MDEuMSA1MDMuMSA1NTggNTIxIDUxMi4yIDUyMXpNODgwIDc1OWgtODR2LTg0YzAtNC40LTMuNi04LTgtOGgtNTZjLTQuNCAwLTggMy42LTggOHY4NGgtODRjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoODR2ODRjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOHYtODRoODRjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(UserAddOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'UserAddOutlined';\n}\nexport default RefIcon;", "/**\n * 团队成员与邀请管理组件\n *\n * 功能特性：\n * - 统一页面显示团队成员列表和邀请记录\n * - 查看团队成员列表及详细信息\n * - 查看团队邀请列表及状态管理\n * - 添加新成员（通过邮箱邀请）\n * - 移除团队现有成员\n * - 取消待处理的邀请\n * - 批量操作支持\n * - 成员和邀请搜索筛选\n *\n * 权限控制：\n * - 只有团队创建者可以进行成员管理操作\n * - 创建者不能移除自己\n * - 提供详细的操作确认\n *\n * 界面设计：\n * - 移除标签页导航，采用统一页面布局\n * - 团队成员和邀请记录垂直排列\n * - 提升用户体验和操作效率\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Modal,\n  Form,\n  message,\n  Tag,\n  Typography,\n  Popconfirm,\n  Select,\n  Tooltip\n} from 'antd';\nimport {\n  UserAddOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  MailOutlined,\n  UserOutlined,\n  CrownOutlined,\n  ReloadOutlined\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport dayjs from 'dayjs';\n\n// 导入服务和类型\nimport { TeamService } from '@/services/team';\nimport { InvitationService } from '@/services/invitation';\nimport type { TeamDetailResponse, TeamMemberResponse, TeamInvitationResponse } from '@/types/api';\nimport { InvitationStatus } from '@/types/api';\nimport InvitationStatusComponent from '@/components/InvitationStatus';\n\nconst { Text } = Typography;\nconst { TextArea } = Input;\n\ninterface TeamMemberManagementProps {\n  teamDetail: TeamDetailResponse;\n  onRefresh: () => void;\n}\n\nconst TeamMemberManagement: React.FC<TeamMemberManagementProps> = ({\n  teamDetail,\n  onRefresh\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [members, setMembers] = useState<TeamMemberResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [inviteModalVisible, setInviteModalVisible] = useState(false);\n  const [inviteForm] = Form.useForm();\n\n  // 邀请管理相关状态\n  const [invitations, setInvitations] = useState<TeamInvitationResponse[]>([]);\n  const [invitationLoading, setInvitationLoading] = useState(false);\n  const [invitationSearchText, setInvitationSearchText] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n\n  useEffect(() => {\n    fetchMembers();\n    fetchInvitations();\n  }, []);\n\n  const fetchMembers = async () => {\n    try {\n      setLoading(true);\n      const memberList = await TeamService.getCurrentTeamMembers();\n      setMembers(memberList || []);\n    } catch (error) {\n      console.error('获取团队成员失败:', error);\n      message.error('获取团队成员失败');\n      setMembers([]); // 确保在错误时设置为空数组\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取邀请列表\n  const fetchInvitations = async () => {\n    try {\n      setInvitationLoading(true);\n      const invitationList = await InvitationService.getCurrentTeamInvitations();\n      setInvitations(invitationList || []);\n    } catch (error) {\n      console.error('获取邀请列表失败:', error);\n      message.error('获取邀请列表失败');\n      setInvitations([]);\n    } finally {\n      setInvitationLoading(false);\n    }\n  };\n\n  // 邀请新成员\n  const handleInviteMembers = async (values: { emails: string; message?: string }) => {\n    try {\n      const emailList = values.emails\n        .split('\\n')\n        .map(email => email.trim())\n        .filter(email => email);\n\n      // 使用新的邀请链接功能\n      const response = await InvitationService.sendInvitations({\n        emails: emailList,\n        message: values.message\n      });\n\n      // 显示详细的发送结果\n      if (response.successCount > 0) {\n        message.success(`成功发送 ${response.successCount} 个邀请`);\n\n        // 显示邀请链接（可选：在开发环境中显示）\n        if (process.env.NODE_ENV === 'development') {\n          console.log('邀请链接:', response.invitations.map(inv => ({\n            email: inv.email,\n            link: inv.invitationLink\n          })));\n        }\n      }\n\n      if (response.failureCount > 0) {\n        message.warning(`${response.failureCount} 个邀请发送失败`);\n      }\n\n      setInviteModalVisible(false);\n      inviteForm.resetFields();\n      fetchMembers();\n      fetchInvitations(); // 刷新邀请列表\n      onRefresh(); // 刷新团队详情\n    } catch (error) {\n      console.error('邀请成员失败:', error);\n      message.error('邀请成员失败');\n    }\n  };\n\n  // 取消邀请\n  const handleCancelInvitation = async (invitationId: number) => {\n    try {\n      await InvitationService.cancelInvitation(invitationId);\n      message.success('邀请取消成功');\n      fetchInvitations();\n      onRefresh();\n    } catch (error) {\n      console.error('取消邀请失败:', error);\n      message.error('取消邀请失败');\n    }\n  };\n\n  // 移除单个成员\n  const handleRemoveMember = async (member: TeamMemberResponse) => {\n    try {\n      await TeamService.removeMember(member.id);\n      message.success(`已移除成员：${member.name}`);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('移除成员失败:', error);\n      message.error('移除成员失败');\n    }\n  };\n\n  // 批量移除成员\n  const handleBatchRemove = async () => {\n    try {\n      const memberIds = selectedRowKeys as number[];\n      for (const memberId of memberIds) {\n        await TeamService.removeMember(memberId);\n      }\n      message.success(`已移除 ${memberIds.length} 名成员`);\n      setSelectedRowKeys([]);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('批量移除成员失败:', error);\n      message.error('批量移除成员失败');\n    }\n  };\n\n  // 筛选成员\n  const filteredMembers = (members || []).filter(member =>\n    member.name.toLowerCase().includes(searchText.toLowerCase()) ||\n    member.email.toLowerCase().includes(searchText.toLowerCase())\n  );\n\n  // 停用/启用成员\n  const handleToggleMemberStatus = async (member: TeamMemberResponse, isActive: boolean) => {\n    try {\n      await TeamService.updateMemberStatus(member.id, isActive);\n      message.success(`已${isActive ? '启用' : '停用'}成员：${member.name}`);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('更新成员状态失败:', error);\n      message.error('更新成员状态失败');\n    }\n  };\n\n  // 表格列配置\n  const columns: ColumnsType<TeamMemberResponse> = [\n    {\n      title: '姓名',\n      dataIndex: 'name',\n      key: 'name',\n      render: (name: string, record) => (\n        <Space>\n          <Text strong>{name}</Text>\n          {record.isCreator && (\n            <Tag icon={<CrownOutlined />} color=\"gold\">创建者</Tag>\n          )}\n        </Space>\n      ),\n    },\n    {\n      title: '邮箱',\n      dataIndex: 'email',\n      key: 'email',\n      render: (email: string) => (\n        <Text type=\"secondary\">{email}</Text>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'isActive',\n      key: 'status',\n      width: 100,\n      render: (isActive: boolean) => (\n        <Tag color={isActive ? 'green' : 'red'}>\n          {isActive ? '启用' : '停用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '加入时间',\n      dataIndex: 'assignedAt',\n      key: 'assignedAt',\n      width: 150,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '最后访问',\n      dataIndex: 'lastAccessTime',\n      key: 'lastAccessTime',\n      width: 150,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      render: (_, record) => {\n        if (record.isCreator) {\n          return <Text type=\"secondary\">-</Text>;\n        }\n\n        return (\n          <Space>\n            {record.isActive ? (\n              <Button\n                type=\"text\"\n                size=\"small\"\n                onClick={() => handleToggleMemberStatus(record, false)}\n              >\n                停用\n              </Button>\n            ) : (\n              <Button\n                type=\"text\"\n                size=\"small\"\n                onClick={() => handleToggleMemberStatus(record, true)}\n              >\n                启用\n              </Button>\n            )}\n            <Popconfirm\n              title=\"确认移除成员\"\n              description={`确定要移除成员 ${record.name} 吗？此操作不可恢复。`}\n              onConfirm={() => handleRemoveMember(record)}\n              okText=\"确认\"\n              cancelText=\"取消\"\n              okType=\"danger\"\n            >\n              <Button\n                type=\"text\"\n                danger\n                size=\"small\"\n                icon={<DeleteOutlined />}\n              >\n                移除\n              </Button>\n            </Popconfirm>\n          </Space>\n        );\n      },\n    },\n  ];\n\n  // 行选择配置\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: setSelectedRowKeys,\n    getCheckboxProps: (record: TeamMemberResponse) => ({\n      disabled: record.isCreator, // 创建者不能被选择\n    }),\n  };\n\n  // 邀请表格列定义\n  const invitationColumns: ColumnsType<TeamInvitationResponse> = [\n    {\n      title: '被邀请人',\n      key: 'invitee',\n      render: (_, record) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text strong>{record.inviteeName || '未注册用户'}</Text>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            <MailOutlined /> {record.inviteeEmail}\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '邀请状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status, record) => (\n        <InvitationStatusComponent\n          status={status}\n          isExpired={record.isExpired}\n        />\n      ),\n      filters: [\n        { text: '待确认', value: InvitationStatus.PENDING },\n        { text: '已接受', value: InvitationStatus.ACCEPTED },\n        { text: '已拒绝', value: InvitationStatus.REJECTED },\n        { text: '已过期', value: InvitationStatus.EXPIRED },\n        { text: '已取消', value: InvitationStatus.CANCELLED },\n      ],\n      onFilter: (value, record) => record.status === value,\n    },\n    {\n      title: '邀请时间',\n      dataIndex: 'invitedAt',\n      key: 'invitedAt',\n      render: (time) => (\n        <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>\n          {dayjs(time).format('MM-DD HH:mm')}\n        </Tooltip>\n      ),\n      sorter: (a, b) => dayjs(a.invitedAt).unix() - dayjs(b.invitedAt).unix(),\n    },\n    {\n      title: '过期时间',\n      dataIndex: 'expiresAt',\n      key: 'expiresAt',\n      render: (time, record) => {\n        const isExpired = record.isExpired;\n        return (\n          <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>\n            <Text type={isExpired ? 'danger' : 'secondary'}>\n              {dayjs(time).format('MM-DD HH:mm')}\n            </Text>\n          </Tooltip>\n        );\n      },\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => {\n        if (record.status === InvitationStatus.PENDING && !record.isExpired) {\n          return (\n            <Popconfirm\n              title=\"确定要取消这个邀请吗？\"\n              onConfirm={() => handleCancelInvitation(record.id)}\n              okText=\"确定\"\n              cancelText=\"取消\"\n            >\n              <Button size=\"small\" danger icon={<DeleteOutlined />}>\n                取消邀请\n              </Button>\n            </Popconfirm>\n          );\n        }\n        return <Text type=\"secondary\">-</Text>;\n      },\n    },\n  ];\n\n  // 过滤邀请列表\n  const filteredInvitations = invitations.filter(invitation => {\n    const matchesSearch = !invitationSearchText ||\n      invitation.inviteeEmail.toLowerCase().includes(invitationSearchText.toLowerCase()) ||\n      (invitation.inviteeName && invitation.inviteeName.toLowerCase().includes(invitationSearchText.toLowerCase()));\n\n    const matchesStatus = !statusFilter || invitation.status === statusFilter;\n\n    return matchesSearch && matchesStatus;\n  });\n\n  return (\n    <div>\n      {/* 团队成员管理区域 */}\n      <Card\n        title={\n          <Space>\n            <UserOutlined />\n            <span>团队成员 ({(members || []).length})</span>\n          </Space>\n        }\n        style={{ marginBottom: 24 }}\n      >\n        {/* 成员操作栏 */}\n        <div style={{ marginBottom: 16 }}>\n          <Space style={{ width: '100%', justifyContent: 'space-between' }}>\n            <Space>\n              <Input\n                placeholder=\"搜索成员姓名或邮箱\"\n                prefix={<SearchOutlined />}\n                value={searchText}\n                onChange={(e) => setSearchText(e.target.value)}\n                style={{ width: 250 }}\n              />\n              {selectedRowKeys.length > 0 && (\n                <Popconfirm\n                  title={`确定要移除选中的 ${selectedRowKeys.length} 名成员吗？`}\n                  onConfirm={handleBatchRemove}\n                  okText=\"确定\"\n                  cancelText=\"取消\"\n                >\n                  <Button danger icon={<DeleteOutlined />}>\n                    批量移除 ({selectedRowKeys.length})\n                  </Button>\n                </Popconfirm>\n              )}\n            </Space>\n            <Button\n              type=\"primary\"\n              icon={<UserAddOutlined />}\n              onClick={() => setInviteModalVisible(true)}\n            >\n              邀请成员\n            </Button>\n          </Space>\n        </div>\n\n        {/* 成员列表 */}\n        <Table\n          columns={columns}\n          dataSource={filteredMembers}\n          rowKey=\"id\"\n          loading={loading}\n          rowSelection={rowSelection}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 名成员`,\n            pageSize: 10,\n          }}\n        />\n      </Card>\n\n      {/* 邀请记录管理区域 */}\n      <Card\n        title={\n          <Space>\n            <MailOutlined />\n            <span>邀请记录 ({(invitations || []).length})</span>\n          </Space>\n        }\n      >\n        {/* 邀请操作栏 */}\n        <div style={{ marginBottom: 16 }}>\n          <Space style={{ width: '100%', justifyContent: 'space-between' }}>\n            <Space>\n              <Input\n                placeholder=\"搜索邮箱或姓名\"\n                prefix={<SearchOutlined />}\n                value={invitationSearchText}\n                onChange={(e) => setInvitationSearchText(e.target.value)}\n                style={{ width: 200 }}\n              />\n              <Select\n                placeholder=\"筛选状态\"\n                value={statusFilter}\n                onChange={setStatusFilter}\n                style={{ width: 120 }}\n                allowClear\n              >\n                <Select.Option value={InvitationStatus.PENDING}>待确认</Select.Option>\n                <Select.Option value={InvitationStatus.ACCEPTED}>已接受</Select.Option>\n                <Select.Option value={InvitationStatus.REJECTED}>已拒绝</Select.Option>\n                <Select.Option value={InvitationStatus.EXPIRED}>已过期</Select.Option>\n                <Select.Option value={InvitationStatus.CANCELLED}>已取消</Select.Option>\n              </Select>\n            </Space>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={fetchInvitations}\n              loading={invitationLoading}\n            >\n              刷新\n            </Button>\n          </Space>\n        </div>\n\n        {/* 邀请列表 */}\n        <Table\n          columns={invitationColumns}\n          dataSource={filteredInvitations}\n          rowKey=\"id\"\n          loading={invitationLoading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条邀请记录`,\n            pageSize: 10,\n          }}\n        />\n      </Card>\n\n      {/* 邀请成员弹窗 */}\n\n      {/* 邀请成员弹窗 */}\n      <Modal\n        title=\"邀请新成员\"\n        open={inviteModalVisible}\n        onCancel={() => {\n          setInviteModalVisible(false);\n          inviteForm.resetFields();\n        }}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={inviteForm}\n          layout=\"vertical\"\n          onFinish={handleInviteMembers}\n        >\n          <Form.Item\n            name=\"emails\"\n            label=\"邮箱地址\"\n            rules={[\n              { required: true, message: '请输入邮箱地址' },\n            ]}\n            extra=\"每行一个邮箱地址，支持批量邀请\"\n          >\n            <TextArea\n              rows={6}\n              placeholder=\"请输入邮箱地址，每行一个&#10;例如：&#10;<EMAIL>&#10;<EMAIL>\"\n            />\n          </Form.Item>\n          <Form.Item\n            name=\"message\"\n            label=\"邀请消息（可选）\"\n            extra=\"您可以添加一些邀请消息，让被邀请人更好地了解邀请意图\"\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"欢迎加入我们的团队！我们期待与您一起工作...\"\n              maxLength={500}\n              showCount\n            />\n          </Form.Item>\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\" icon={<MailOutlined />}>\n                发送邀请\n              </Button>\n              <Button onClick={() => setInviteModalVisible(false)}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default TeamMemberManagement;\n", "/**\n * 集成团队管理页面\n *\n * 功能特性：\n * - 统一的团队管理界面，包含多个功能模块\n * - 团队名称卡片显示，提升用户体验\n * - 合并的成员与邀请管理功能\n * - 团队设置功能\n * - 权限控制，只有团队创建者可以访问管理功能\n *\n * 模块组织：\n * - 团队成员与邀请管理：查看、添加、移除团队成员，管理邀请\n * - 团队设置：编辑团队信息、删除团队\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport {\n  Card,\n  Alert,\n  Spin,\n  Typography,\n  Space,\n  Tag,\n  Avatar,\n  Button,\n  Dropdown,\n  Modal,\n  Form,\n  Input,\n  message\n} from 'antd';\nimport {\n  TeamOutlined,\n  InfoCircleOutlined,\n  CrownOutlined,\n  MoreOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  SaveOutlined,\n  ExclamationCircleOutlined\n} from '@ant-design/icons';\nimport { history } from '@umijs/max';\n\n// 导入子组件\nimport TeamMemberManagement from './components/TeamMemberManagement';\n\n// 导入服务和类型\nimport { TeamService } from '@/services/team';\nimport type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\n\nconst TeamManagementPage: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);\n\n  // 团队编辑相关状态\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [updating, setUpdating] = useState(false);\n  const [form] = Form.useForm();\n\n  // 团队删除相关状态\n  const [deleteModalVisible, setDeleteModalVisible] = useState(false);\n  const [deleteConfirmText, setDeleteConfirmText] = useState('');\n  const [deleting, setDeleting] = useState(false);\n\n  useEffect(() => {\n    fetchTeamDetail();\n  }, []);\n\n  const fetchTeamDetail = async () => {\n    try {\n      setLoading(true);\n      const detail = await TeamService.getCurrentTeamDetail();\n      setTeamDetail(detail);\n    } catch (error) {\n      console.error('获取团队详情失败:', error);\n      // 如果获取失败，可能是没有选择团队，跳转到个人中心页面\n      history.push('/personal-center');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 处理编辑团队\n  const handleEditTeam = () => {\n    if (!teamDetail) return;\n    form.setFieldsValue({\n      name: teamDetail.name,\n      description: teamDetail.description || '',\n    });\n    setEditModalVisible(true);\n  };\n\n  // 保存团队信息\n  const handleSaveTeam = async (values: UpdateTeamRequest) => {\n    try {\n      setUpdating(true);\n      await TeamService.updateCurrentTeam(values);\n      message.success('团队信息更新成功');\n      setEditModalVisible(false);\n      fetchTeamDetail();\n    } catch (error) {\n      console.error('更新团队失败:', error);\n      message.error('更新团队失败');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  // 删除团队\n  const handleDeleteTeam = async () => {\n    if (deleteConfirmText !== teamDetail?.name) {\n      message.error('请输入正确的团队名称');\n      return;\n    }\n\n    try {\n      setDeleting(true);\n      await TeamService.deleteCurrentTeam();\n      message.success('团队已删除');\n      setDeleteModalVisible(false);\n      // 删除成功后跳转到团队选择页面\n      history.push('/user/team-select');\n    } catch (error) {\n      console.error('删除团队失败:', error);\n      message.error('删除团队失败');\n    } finally {\n      setDeleting(false);\n    }\n  };\n\n  // 权限检查：只有团队创建者可以访问管理功能\n  const hasManagePermission = teamDetail?.isCreator || false;\n\n  if (loading) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Spin size=\"large\" />\n          <div style={{ marginTop: 16 }}>\n            <Text type=\"secondary\">正在加载团队信息...</Text>\n          </div>\n        </div>\n      </PageContainer>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <PageContainer>\n        <Card>\n          <div style={{ textAlign: 'center', padding: '50px 0' }}>\n            <InfoCircleOutlined style={{ fontSize: 48, color: '#faad14', marginBottom: 16 }} />\n            <Title level={4}>未找到团队信息</Title>\n            <Text type=\"secondary\">请先选择一个团队</Text>\n            <div style={{ marginTop: 16 }}>\n              <Button type=\"primary\" onClick={() => history.push('/personal-center')}>\n                返回个人中心\n              </Button>\n            </div>\n          </div>\n        </Card>\n      </PageContainer>\n    );\n  }\n\n  // 权限不足提示\n  if (!hasManagePermission) {\n    return (\n      <PageContainer>\n        <Card>\n          <Alert\n            message=\"权限不足\"\n            description=\"只有团队创建者可以访问团队管理功能。如果您需要管理权限，请联系团队创建者。\"\n            type=\"warning\"\n            showIcon\n            action={\n              <Button size=\"small\" onClick={() => history.push('/dashboard')}>\n                返回首页\n              </Button>\n            }\n          />\n        </Card>\n      </PageContainer>\n    );\n  }\n\n\n\n  // 操作菜单项\n  const menuItems = [\n    {\n      key: 'edit',\n      label: '编辑团队',\n      icon: <EditOutlined />,\n      onClick: handleEditTeam,\n    },\n    {\n      key: 'delete',\n      label: '删除团队',\n      icon: <DeleteOutlined />,\n      danger: true,\n      onClick: () => setDeleteModalVisible(true),\n    },\n  ];\n\n  return (\n    <PageContainer>\n      {/* 团队名称卡片 */}\n      <Card\n        style={{ marginBottom: 16 }}\n        extra={\n          hasManagePermission && (\n            <Dropdown\n              menu={{ items: menuItems }}\n              placement=\"bottomRight\"\n              trigger={['click']}\n            >\n              <Button\n                type=\"text\"\n                icon={<MoreOutlined />}\n                style={{ fontSize: 16 }}\n              />\n            </Dropdown>\n          )\n        }\n      >\n        <Space size=\"large\" align=\"center\">\n          <Avatar\n            size={48}\n            icon={<TeamOutlined />}\n            style={{\n              backgroundColor: '#1890ff',\n              fontSize: 20,\n            }}\n          />\n          <div>\n            <Title level={3} style={{ margin: 0, marginBottom: 4 }}>\n              {teamDetail.name}\n            </Title>\n            <Space>\n              {teamDetail.isCreator && (\n                <Tag\n                  icon={<CrownOutlined />}\n                  color=\"gold\"\n                >\n                  管理员\n                </Tag>\n              )}\n              <Text type=\"secondary\">\n                {teamDetail.description || '这个团队还没有描述'}\n              </Text>\n            </Space>\n          </div>\n        </Space>\n      </Card>\n\n      {/* 团队成员与邀请管理 */}\n      <TeamMemberManagement\n        teamDetail={teamDetail}\n        onRefresh={fetchTeamDetail}\n      />\n\n      {/* 编辑团队模态框 */}\n      <Modal\n        title=\"编辑团队信息\"\n        open={editModalVisible}\n        onCancel={() => {\n          setEditModalVisible(false);\n          form.resetFields();\n        }}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSaveTeam}\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"团队名称\"\n            rules={[\n              { required: true, message: '请输入团队名称' },\n              { min: 2, max: 50, message: '团队名称长度应在2-50个字符之间' },\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" />\n          </Form.Item>\n          <Form.Item\n            name=\"description\"\n            label=\"团队描述\"\n            rules={[\n              { max: 200, message: '团队描述不能超过200个字符' },\n            ]}\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入团队描述（可选）\"\n              showCount\n              maxLength={200}\n            />\n          </Form.Item>\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => {\n                setEditModalVisible(false);\n                form.resetFields();\n              }}>\n                取消\n              </Button>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={updating}\n                icon={<SaveOutlined />}\n              >\n                保存\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 删除团队确认模态框 */}\n      <Modal\n        title={\n          <Space>\n            <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />\n            <Text type=\"danger\">删除团队确认</Text>\n          </Space>\n        }\n        open={deleteModalVisible}\n        onCancel={() => {\n          setDeleteModalVisible(false);\n          setDeleteConfirmText('');\n        }}\n        footer={null}\n        width={600}\n      >\n        <Alert\n          message=\"警告：此操作不可恢复\"\n          description={\n            <div>\n              <p>删除团队将会：</p>\n              <ul>\n                <li>永久删除团队及所有相关数据</li>\n                <li>移除所有团队成员</li>\n                <li>清除团队设置和配置</li>\n                <li>无法恢复任何数据</li>\n              </ul>\n            </div>\n          }\n          type=\"error\"\n          showIcon\n          style={{ marginBottom: 24 }}\n        />\n\n        <div style={{ marginBottom: 16 }}>\n          <Text strong>\n            请输入团队名称 \"<Text code>{teamDetail?.name}</Text>\" 来确认删除：\n          </Text>\n        </div>\n\n        <Input\n          placeholder={`请输入：${teamDetail?.name}`}\n          value={deleteConfirmText}\n          onChange={(e) => setDeleteConfirmText(e.target.value)}\n          style={{ marginBottom: 24 }}\n        />\n\n        <div style={{ textAlign: 'right' }}>\n          <Space>\n            <Button\n              onClick={() => {\n                setDeleteModalVisible(false);\n                setDeleteConfirmText('');\n              }}\n            >\n              取消\n            </Button>\n            <Button\n              type=\"primary\"\n              danger\n              loading={deleting}\n              disabled={deleteConfirmText !== teamDetail?.name}\n              onClick={handleDeleteTeam}\n              icon={<DeleteOutlined />}\n            >\n              确认删除团队\n            </Button>\n          </Space>\n        </div>\n      </Modal>\n    </PageContainer>\n  );\n};\n\nexport default TeamManagementPage;\n"], "names": [], "mappings": "gvBACI,EAAqB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,OAAQ,EAAG,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,+KAAgL,CAAE,EAAG,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,kIAAmI,CAAE,EAAE,AAAC,EAAG,KAAQ,cAAe,MAAS,UAAW,2BCcxiB,EAAuB,EAAM,UAAU,CARlB,SAA4B,CAAK,CAAE,CAAG,EAC7D,OAAoB,EAAM,aAAa,CAAC,SAAQ,CAAE,GAAA,SAAQ,EAAC,CAAC,EAAG,EAAO,CACpE,IAAK,EACL,KAAM,CACR,IACF,ugBCVA,IAAM,GAAe,IACnB,GAAM,CACJ,aAAA,CAAY,CACZ,QAAA,CAAO,CACP,OAAA,CAAM,CACN,YAAA,CAAW,CACX,UAAA,CAAS,CACT,aAAA,CAAY,CACZ,UAAA,CAAS,CACT,SAAA,CAAQ,CACR,SAAA,CAAQ,CACR,iBAAA,CAAgB,CAChB,iBAAA,CAAgB,CACjB,CAAG,EACJ,MAAO,CACL,CAAC,EAAa,CAAE,CACd,OAAQ,EACR,CAAC,CAAC,CAAC,EAAE,EAAO,QAAQ,CAAC,CAAC,CAAE,CACtB,SAAA,CACF,EACA,CAAC,CAAC,EAAE,EAAa,QAAQ,CAAC,CAAC,CAAE,CAC3B,aAAc,EACd,QAAS,OACT,SAAU,SACV,WAAY,QACZ,CAAC,CAAC,EAAE,EAAE,EAAa,cAAc,EAAE,EAAQ,CAAC,CAAC,CAAE,CAC7C,MAAO,EACP,SAAA,EACA,WAAY,EACZ,gBAAiB,CACnB,EACA,CAAC,CAAC,EAAE,EAAa,MAAM,CAAC,CAAC,CAAE,CACzB,WAAY,EACZ,MAAO,EACP,eAAgB,CACd,WAAY,QACd,CACF,EACA,CAAC,CAAC,EAAE,EAAa,YAAY,CAAC,CAAC,CAAE,CAC/B,UAAW,EACX,MAAO,CACT,CACF,EACA,CAAC,CAAC,EAAE,EAAa,QAAQ,CAAC,CAAC,CAAE,CAC3B,UAAW,MACX,WAAY,SACZ,OAAQ,CACN,kBAAmB,CACrB,CACF,CACF,CACF,EACF,MAUA,GAAe,GAAA,gBAAa,EAAC,aAAc,GAAS,GAAa,GAR5B,IACnC,GAAM,CACJ,gBAAA,CAAe,CAChB,CAAG,EACJ,MAAO,CACL,YAAa,EAAkB,EACjC,EACF,EACgG,CAC9F,WAAY,CAAA,CACd,GChEI,GAAS,IAAI,EAAI,IAAI,CAAC,MAAM,EAAI,SAAU,CAAC,CAAE,CAAC,EAChD,IAAI,EAAI,CAAC,EACT,IAAK,IAAI,KAAK,EAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAG,IAAM,AAAe,EAAf,EAAE,OAAO,CAAC,IAAQ,CAAA,CAAC,CAAC,EAAE,CAAG,CAAC,CAAC,EAAE,AAAD,EAC/F,GAAI,AAAK,MAAL,GAAa,AAAwC,YAAxC,OAAO,OAAO,qBAAqB,CAAiB,IAAK,IAAI,EAAI,EAAG,EAAI,OAAO,qBAAqB,CAAC,GAAI,EAAI,EAAE,MAAM,CAAE,IAChH,EAAlB,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAS,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAG,CAAC,CAAC,EAAE,GAAG,CAAA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,AAAD,EAElG,OAAO,EACT,EAaO,IAAM,GAAU,IACrB,GAAM,CACJ,UAAA,CAAS,CACT,cAAA,CAAa,CACb,kBAAA,CAAiB,CACjB,MAAA,CAAK,CACL,YAAA,CAAW,CACX,WAAA,CAAU,CACV,OAAA,CAAM,CACN,OAAA,EAAS,SAAS,CAClB,KAAA,EAAoB,EAAM,aAAa,CAAC,UAAuB,CAAE,KAAK,CACtE,WAAA,EAAa,CAAA,CAAI,CACjB,MAAA,CAAK,CACL,UAAA,CAAS,CACT,SAAA,CAAQ,CACR,aAAA,CAAY,CACb,CAAG,EACE,CACJ,aAAA,CAAY,CACb,CAAG,EAAM,UAAU,CAAC,gBAAa,EAC5B,CAAC,EAAc,CAAG,GAAA,UAAS,EAAC,aAAc,UAAa,CAAC,UAAU,EAClE,EAAY,GAAA,qBAAkB,EAAC,GAC/B,EAAkB,GAAA,qBAAkB,EAAC,GAC3C,OAAoB,EAAM,aAAa,CAAC,MAAO,CAC7C,UAAW,CAAC,EAAE,EAAU,cAAc,CAAC,CACvC,QAAS,CACX,EAAgB,EAAM,aAAa,CAAC,MAAO,CACzC,UAAW,CAAC,EAAE,EAAU,QAAQ,CAAC,AACnC,EAAG,GAAqB,EAAM,aAAa,CAAC,OAAQ,CAClD,UAAW,CAAC,EAAE,EAAU,aAAa,CAAC,AACxC,EAAG,GAAoB,EAAM,aAAa,CAAC,MAAO,CAChD,UAAW,CAAC,EAAE,EAAU,aAAa,CAAC,AACxC,EAAG,GAA0B,EAAM,aAAa,CAAC,MAAO,CACtD,UAAW,CAAC,EAAE,EAAU,MAAM,CAAC,AACjC,EAAG,GAAY,GAAgC,EAAM,aAAa,CAAC,MAAO,CACxE,UAAW,CAAC,EAAE,EAAU,YAAY,CAAC,AACvC,EAAG,KAAiC,EAAM,aAAa,CAAC,MAAO,CAC7D,UAAW,CAAC,EAAE,EAAU,QAAQ,CAAC,AACnC,EAAG,GAA4B,EAAM,aAAa,CAAC,SAAM,CAAE,OAAO,MAAM,CAAC,CACvE,QAAS,EACT,KAAM,OACR,EAAG,GAAoB,GAAe,CAAA,MAAA,EAAqD,KAAK,EAAI,EAAc,UAAU,AAAD,GAAmB,EAAM,aAAa,CAAC,UAAY,CAAE,CAC9K,YAAa,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CACvC,KAAM,OACR,EAAG,GAAA,qBAAkB,EAAC,IAAU,GAChC,SAAU,EACV,MAAO,EACP,UAAW,EAAa,OACxB,yBAA0B,CAAA,EAC1B,UAAW,CAAA,CACb,EAAG,GAAW,CAAA,MAAA,EAAqD,KAAK,EAAI,EAAc,MAAM,AAAD,KACjG,ECvEA,IAAI,GAAS,IAAI,EAAI,IAAI,CAAC,MAAM,EAAI,SAAU,CAAC,CAAE,CAAC,EAChD,IAAI,EAAI,CAAC,EACT,IAAK,IAAI,KAAK,EAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAG,IAAM,AAAe,EAAf,EAAE,OAAO,CAAC,IAAQ,CAAA,CAAC,CAAC,EAAE,CAAG,CAAC,CAAC,EAAE,AAAD,EAC/F,GAAI,AAAK,MAAL,GAAa,AAAwC,YAAxC,OAAO,OAAO,qBAAqB,CAAiB,IAAK,IAAI,EAAI,EAAG,EAAI,OAAO,qBAAqB,CAAC,GAAI,EAAI,EAAE,MAAM,CAAE,IAChH,EAAlB,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAS,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAG,CAAC,CAAC,EAAE,GAAG,CAAA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,AAAD,EAElG,OAAO,EACT,EAUA,IAAM,GAAkC,EAAM,UAAU,CAAC,CAAC,EAAO,KAC/D,IAAI,EAAI,EACR,GAAM,CACF,UAAW,CAAkB,CAC7B,UAAA,EAAY,KAAK,CACjB,QAAA,EAAU,OAAO,CACjB,OAAA,EAAS,SAAS,CAClB,KAAA,EAAoB,EAAM,aAAa,CAAC,UAAuB,CAAE,KAAK,CACtE,SAAA,CAAQ,CACR,iBAAA,CAAgB,CAChB,aAAA,CAAY,CACZ,gBAAA,CAAe,CACf,aAAA,CAAY,CACZ,OAAA,CAAM,CACN,WAAY,CAAoB,CACjC,CAAG,EACJ,EAAY,GAAO,EAAO,CAAC,YAAa,YAAa,UAAW,SAAU,OAAQ,WAAY,mBAAoB,eAAgB,kBAAmB,eAAgB,SAAU,aAAa,EACxL,CACJ,aAAA,CAAY,CACZ,UAAW,CAAgB,CAC3B,MAAO,CAAY,CACnB,WAAY,CAAiB,CAC7B,OAAQ,CAAa,CACtB,CAAG,GAAA,qBAAkB,EAAC,cACjB,CAAC,EAAM,EAAQ,CAAG,GAAA,UAAc,EAAC,CAAA,EAAO,CAC5C,MAAO,AAAsB,OAArB,CAAA,EAAK,EAAM,IAAI,AAAD,GAAe,AAAO,KAAK,IAAZ,EAAgB,EAAK,EAAM,OAAO,CACvE,aAAc,AAA6B,OAA5B,CAAA,EAAK,EAAM,WAAW,AAAD,GAAe,AAAO,KAAK,IAAZ,EAAgB,EAAK,EAAM,cAAc,AAC9F,GACM,EAAc,CAAC,EAAO,KAC1B,EAAQ,EAAO,CAAA,GACf,MAAA,GAAkE,EAAgB,GAClF,MAAA,GAA4D,EAAa,EAAO,GAClF,EAsBM,EAAY,EAAa,aAAc,GACvC,EAAiB,GAAA,UAAU,EAAC,EAAW,EAAkB,EAAkB,EAAkB,IAAI,CAAE,MAAA,EAAmE,KAAK,EAAI,EAAqB,IAAI,EACxM,EAAiB,GAAA,UAAU,EAAC,EAAkB,IAAI,CAAE,MAAA,EAAmE,KAAK,EAAI,EAAqB,IAAI,EACzJ,CAAC,EAAW,CAAG,GAAS,GAC9B,OAAO,EAAwB,EAAM,aAAa,CAAC,UAAO,CAAE,OAAO,MAAM,CAAC,CAAC,EAAG,GAAA,UAAI,EAAC,EAAW,CAAC,QAAQ,EAAG,CACxG,QAAS,EACT,UAAW,EACX,aAhB2B,CAAC,EAAO,KACnC,GAAM,CACJ,SAAA,EAAW,CAAA,CAAK,CACjB,CAAG,EACA,GAGJ,EAAY,EAAO,GACrB,EASE,KAAM,EACN,IAAK,EACL,WAAY,CACV,KAAM,EACN,KAAM,CACR,EACA,OAAQ,CACN,KAAM,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,EAAG,EAAc,IAAI,EAAG,GAAe,GAAe,MAAA,EAAuC,KAAK,EAAI,EAAO,IAAI,EAChL,KAAM,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,EAAG,EAAc,IAAI,EAAG,MAAA,EAAuC,KAAK,EAAI,EAAO,IAAI,CACxH,EACA,QAAsB,EAAM,aAAa,CAAC,GAAS,OAAO,MAAM,CAAC,CAC/D,OAAQ,EACR,KAAM,CACR,EAAG,EAAO,CACR,UAAW,EACX,MA5CU,IACZ,EAAY,CAAA,EAAO,GACrB,EA2CI,UA1Cc,IAChB,IAAI,EACJ,OAAO,AAA2B,OAA1B,CAAA,EAAK,EAAM,SAAS,AAAD,GAAe,AAAO,KAAK,IAAZ,EAAgB,KAAK,EAAI,EAAG,IAAI,CAAC,IAAI,CAAE,GACnF,EAwCI,SAvCa,IACf,IAAI,EACJ,EAAY,CAAA,EAAO,GACnB,AAA0B,OAAzB,CAAA,EAAK,EAAM,QAAQ,AAAD,GAAe,AAAO,KAAK,IAAZ,GAAyB,EAAG,IAAI,CAAC,IAAI,CAAE,GAC3E,CAoCE,IACA,sBAAuB,CAAA,CACzB,GAAI,IACN,GAIA,AAHmB,GAGR,sCAAsC,CDhC/B,IAChB,GAAM,CACF,UAAW,CAAkB,CAC7B,UAAA,CAAS,CACT,UAAA,CAAS,CACT,MAAA,CAAK,CACN,CAAG,EACJ,EAAY,GAAO,EAAO,CAAC,YAAa,YAAa,YAAa,QAAQ,EACtE,CACJ,aAAA,CAAY,CACb,CAAG,EAAM,UAAU,CAAC,gBAAa,EAC5B,EAAY,EAAa,aAAc,GACvC,CAAC,EAAW,CAAG,GAAS,GAC9B,OAAO,EAAwB,EAAM,aAAa,CAAC,UAAgB,CAAE,CACnE,UAAW,EACX,UAAW,GAAA,UAAU,EAAC,EAAW,GACjC,MAAO,EACP,QAAsB,EAAM,aAAa,CAAC,GAAS,OAAO,MAAM,CAAC,CAC/D,UAAW,CACb,EAAG,GACL,IACF,8DE9FI,GAAkB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,OAAQ,EAAG,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,k2BAAm2B,CAAE,EAAE,AAAC,EAAG,KAAQ,WAAY,MAAS,UAAW,ECc5iC,GAAuB,EAAM,UAAU,CARrB,SAAyB,CAAK,CAAE,CAAG,EACvD,OAAoB,EAAM,aAAa,CAAC,SAAQ,CAAE,GAAA,SAAQ,EAAC,CAAC,EAAG,EAAO,CACpE,IAAK,EACL,KAAM,EACR,IACF,8NC+CA,GAAM,CAAE,KAAA,EAAI,CAAE,CAAG,SAAU,CACrB,CAAE,SAAA,EAAQ,CAAE,CAAG,SAAK,CAOpB,GAA4D,CAAC,CACjE,WAAA,CAAU,CACV,UAAA,CAAS,CACV,IACC,GAAM,CAAC,EAAS,EAAW,CAAG,GAAA,UAAQ,EAAC,CAAA,GACjC,CAAC,EAAS,EAAW,CAAG,GAAA,UAAQ,EAAuB,EAAE,EACzD,CAAC,EAAY,EAAc,CAAG,GAAA,UAAQ,EAAC,IACvC,CAAC,EAAiB,EAAmB,CAAG,GAAA,UAAQ,EAAc,EAAE,EAChE,CAAC,EAAoB,EAAsB,CAAG,GAAA,UAAQ,EAAC,CAAA,GACvD,CAAC,EAAW,CAAG,SAAI,CAAC,OAAO,GAG3B,CAAC,EAAa,EAAe,CAAG,GAAA,UAAQ,EAA2B,EAAE,EACrE,CAAC,EAAmB,EAAqB,CAAG,GAAA,UAAQ,EAAC,CAAA,GACrD,CAAC,EAAsB,EAAwB,CAAG,GAAA,UAAQ,EAAC,IAC3D,CAAC,EAAc,EAAgB,CAAG,GAAA,UAAQ,EAAS,IAEzD,GAAA,WAAS,EAAC,KACR,IACA,IACF,EAAG,EAAE,EAEL,IAAM,EAAe,UACnB,GAAI,CACF,EAAW,CAAA,GACX,IAAM,EAAa,MAAM,cAAW,CAAC,qBAAqB,GAC1D,EAAW,GAAc,EAAE,EAC7B,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,oDAAa,GAC3B,SAAO,CAAC,KAAK,CAAC,oDACd,EAAW,EAAE,EACf,QAAU,CACR,EAAW,CAAA,GACb,CACF,EAGM,EAAmB,UACvB,GAAI,CACF,EAAqB,CAAA,GACrB,IAAM,EAAiB,MAAM,oBAAiB,CAAC,yBAAyB,GACxE,EAAe,GAAkB,EAAE,EACrC,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,oDAAa,GAC3B,SAAO,CAAC,KAAK,CAAC,oDACd,EAAe,EAAE,EACnB,QAAU,CACR,EAAqB,CAAA,GACvB,CACF,EAGM,EAAsB,MAAO,IACjC,GAAI,CACF,IAAM,EAAY,EAAO,MAAM,CAC5B,KAAK,CAAC,MACN,GAAG,CAAC,GAAS,EAAM,IAAI,IACvB,MAAM,CAAC,GAAS,GAGb,EAAW,MAAM,oBAAiB,CAAC,eAAe,CAAC,CACvD,OAAQ,EACR,QAAS,EAAO,OAAO,AACzB,GAGI,EAAS,YAAY,CAAG,GAC1B,SAAO,CAAC,OAAO,CAAC,CAAC,iCAAK,EAAE,EAAS,YAAY,CAAC,yBAAI,CAAC,EAWjD,EAAS,YAAY,CAAG,GAC1B,SAAO,CAAC,OAAO,CAAC,CAAC,EAAE,EAAS,YAAY,CAAC,yDAAQ,CAAC,EAGpD,EAAsB,CAAA,GACtB,EAAW,WAAW,GACtB,IACA,IACA,IACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wCAAW,GACzB,SAAO,CAAC,KAAK,CAAC,wCAChB,CACF,EAGM,EAAyB,MAAO,IACpC,GAAI,CACF,MAAM,oBAAiB,CAAC,gBAAgB,CAAC,GACzC,SAAO,CAAC,OAAO,CAAC,wCAChB,IACA,IACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wCAAW,GACzB,SAAO,CAAC,KAAK,CAAC,wCAChB,CACF,EAGM,EAAqB,MAAO,IAChC,GAAI,CACF,MAAM,cAAW,CAAC,YAAY,CAAC,EAAO,EAAE,EACxC,SAAO,CAAC,OAAO,CAAC,CAAC,gDAAM,EAAE,EAAO,IAAI,CAAC,CAAC,EACtC,IACA,IACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wCAAW,GACzB,SAAO,CAAC,KAAK,CAAC,wCAChB,CACF,EAGM,EAAoB,UACxB,GAAI,CAEF,IAAK,IAAM,KADO,EAEhB,MAAM,cAAW,CAAC,YAAY,CAAC,GAEjC,SAAO,CAAC,OAAO,CAAC,CAAC,yBAAI,EAAE,AAJL,EAIe,MAAM,CAAC,yBAAI,CAAC,EAC7C,EAAmB,EAAE,EACrB,IACA,IACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,oDAAa,GAC3B,SAAO,CAAC,KAAK,CAAC,oDAChB,CACF,EAGM,EAAkB,AAAC,CAAA,GAAW,EAAE,AAAD,EAAG,MAAM,CAAC,GAC7C,EAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,EAAW,WAAW,KACzD,EAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,EAAW,WAAW,KAItD,EAA2B,MAAO,EAA4B,KAClE,GAAI,CACF,MAAM,cAAW,CAAC,kBAAkB,CAAC,EAAO,EAAE,CAAE,GAChD,SAAO,CAAC,OAAO,CAAC,CAAC,QAAC,EAAE,EAAW,eAAO,eAAK,wBAAG,EAAE,EAAO,IAAI,CAAC,CAAC,EAC7D,IACA,IACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,oDAAa,GAC3B,SAAO,CAAC,KAAK,CAAC,oDAChB,CACF,EAGM,EAA2C,CAC/C,CACE,MAAO,eACP,UAAW,OACX,IAAK,OACL,OAAQ,CAAC,EAAc,IACrB,WAAC,SAAK,YACJ,UAAC,IAAK,MAAM,aAAE,IACb,EAAO,SAAS,EACf,UAAC,SAAG,EAAC,KAAM,UAAC,SAAa,KAAK,MAAM,gBAAO,yBAInD,EACA,CACE,MAAO,eACP,UAAW,QACX,IAAK,QACL,OAAQ,AAAC,GACP,UAAC,IAAK,KAAK,qBAAa,GAE5B,EACA,CACE,MAAO,eACP,UAAW,WACX,IAAK,SACL,MAAO,IACP,OAAQ,AAAC,GACP,UAAC,SAAG,EAAC,MAAO,EAAW,QAAU,eAC9B,EAAW,eAAO,gBAGzB,EACA,CACE,MAAO,2BACP,UAAW,aACX,IAAK,aACL,MAAO,IACP,OAAQ,AAAC,GAAiB,IAAI,KAAK,GAAM,kBAAkB,EAC7D,EACA,CACE,MAAO,2BACP,UAAW,iBACX,IAAK,iBACL,MAAO,IACP,OAAQ,AAAC,GAAiB,IAAI,KAAK,GAAM,kBAAkB,EAC7D,EACA,CACE,MAAO,eACP,IAAK,SACL,MAAO,IACP,OAAQ,CAAC,EAAG,IACV,AAAI,EAAO,SAAS,CACX,UAAC,IAAK,KAAK,qBAAY,MAI9B,WAAC,SAAK,YACH,EAAO,QAAQ,CACd,UAAC,SAAM,EACL,KAAK,OACL,KAAK,QACL,QAAS,IAAM,EAAyB,EAAQ,CAAA,YACjD,iBAID,UAAC,SAAM,EACL,KAAK,OACL,KAAK,QACL,QAAS,IAAM,EAAyB,EAAQ,CAAA,YACjD,iBAIH,UHnMO,IGoML,MAAM,uCACN,YAAa,CAAC,yDAAQ,EAAE,EAAO,IAAI,CAAC,iFAAW,CAAC,CAChD,UAAW,IAAM,EAAmB,GACpC,OAAO,eACP,WAAW,eACX,OAAO,kBAEP,UAAC,SAAM,EACL,KAAK,OACL,MAAM,IACN,KAAK,QACL,KAAM,UAAC,SAAc,cACtB,qBAOX,EACD,CAYK,EAAyD,CAC7D,CACE,MAAO,2BACP,IAAK,UACL,OAAQ,CAAC,EAAG,IACV,WAAC,SAAK,EAAC,UAAU,WAAW,KAAM,YAChC,UAAC,IAAK,MAAM,aAAE,EAAO,WAAW,EAAI,mCACpC,WAAC,IAAK,KAAK,YAAY,MAAO,CAAE,SAAU,MAAO,YAC/C,UAAC,UAAY,KAAG,IAAE,EAAO,YAAY,MAI7C,EACA,CACE,MAAO,2BACP,UAAW,SACX,IAAK,SACL,OAAQ,CAAC,EAAQ,IACf,UAAC,UAAyB,EACxB,OAAQ,EACR,UAAW,EAAO,SAAS,GAG/B,QAAS,CACP,CAAE,KAAM,qBAAO,MAAO,mBAAgB,CAAC,OAAO,AAAC,EAC/C,CAAE,KAAM,qBAAO,MAAO,mBAAgB,CAAC,QAAQ,AAAC,EAChD,CAAE,KAAM,qBAAO,MAAO,mBAAgB,CAAC,QAAQ,AAAC,EAChD,CAAE,KAAM,qBAAO,MAAO,mBAAgB,CAAC,OAAO,AAAC,EAC/C,CAAE,KAAM,qBAAO,MAAO,mBAAgB,CAAC,SAAS,AAAC,EAClD,CACD,SAAU,CAAC,EAAO,IAAW,EAAO,MAAM,GAAK,CACjD,EACA,CACE,MAAO,2BACP,UAAW,YACX,IAAK,YACL,OAAQ,AAAC,GACP,UAAC,UAAO,EAAC,MAAO,GAAA,UAAK,EAAC,GAAM,MAAM,CAAC,gCAChC,GAAA,UAAK,EAAC,GAAM,MAAM,CAAC,iBAGxB,OAAQ,CAAC,EAAG,IAAM,GAAA,UAAK,EAAC,EAAE,SAAS,EAAE,IAAI,GAAK,GAAA,UAAK,EAAC,EAAE,SAAS,EAAE,IAAI,EACvE,EACA,CACE,MAAO,2BACP,UAAW,YACX,IAAK,YACL,OAAQ,CAAC,EAAM,KACb,IAAM,EAAY,EAAO,SAAS,CAClC,MACE,UAAC,UAAO,EAAC,MAAO,GAAA,UAAK,EAAC,GAAM,MAAM,CAAC,gCACjC,UAAC,IAAK,KAAM,EAAY,SAAW,qBAChC,GAAA,UAAK,EAAC,GAAM,MAAM,CAAC,mBAI5B,CACF,EACA,CACE,MAAO,eACP,IAAK,SACL,OAAQ,CAAC,EAAG,IACV,AAAI,EAAO,MAAM,GAAK,mBAAgB,CAAC,OAAO,EAAK,EAAO,SAAS,CAc5D,UAAC,IAAK,KAAK,qBAAY,MAZ1B,UHpSO,IGqSL,MAAM,qEACN,UAAW,IAAM,EAAuB,EAAO,EAAE,EACjD,OAAO,eACP,WAAW,wBAEX,UAAC,SAAM,EAAC,KAAK,QAAQ,MAAM,IAAC,KAAM,UAAC,SAAc,cAAK,8BAQhE,EACD,CAGK,EAAsB,EAAY,MAAM,CAAC,IAC7C,IAAM,EAAgB,CAAC,GACrB,EAAW,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,EAAqB,WAAW,KAC9E,EAAW,WAAW,EAAI,EAAW,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,EAAqB,WAAW,IAErG,EAAgB,CAAC,GAAgB,EAAW,MAAM,GAAK,EAE7D,OAAO,GAAiB,EAC1B,GAEA,MACE,WAAC,iBAEC,WAAC,SAAI,EACH,MACE,WAAC,SAAK,YACJ,UAAC,UAAY,KACb,WAAC,kBAAK,6BAAO,AAAC,CAAA,GAAW,EAAE,AAAD,EAAG,MAAM,CAAC,UAGxC,MAAO,CAAE,aAAc,EAAG,YAG1B,UAAC,OAAI,MAAO,CAAE,aAAc,EAAG,WAC7B,WAAC,SAAK,EAAC,MAAO,CAAE,MAAO,OAAQ,eAAgB,eAAgB,YAC7D,WAAC,SAAK,YACJ,UAAC,SAAK,EACJ,YAAY,yDACZ,OAAQ,UAAC,UAAc,KACvB,MAAO,EACP,SAAU,AAAC,GAAM,EAAc,EAAE,MAAM,CAAC,KAAK,EAC7C,MAAO,CAAE,MAAO,GAAI,IAErB,EAAgB,MAAM,CAAG,GACxB,UHxVG,IGyVD,MAAO,CAAC,iEAAS,EAAE,EAAgB,MAAM,CAAC,yCAAM,CAAC,CACjD,UAAW,EACX,OAAO,eACP,WAAW,wBAEX,WAAC,SAAM,EAAC,MAAM,IAAC,KAAM,UAAC,SAAc,eAAK,6BAChC,EAAgB,MAAM,CAAC,YAKtC,UAAC,SAAM,EACL,KAAK,UACL,KAAM,UAAC,OACP,QAAS,IAAM,EAAsB,CAAA,YACtC,kCAOL,UAAC,UAAK,EACJ,QAAS,EACT,WAAY,EACZ,OAAO,KACP,QAAS,EACT,aAzJa,CACnB,gBAAA,EACA,SAAU,EACV,iBAAkB,AAAC,GAAgC,CAAA,CACjD,SAAU,EAAO,SAAS,AAC5B,CAAA,CACF,EAoJQ,WAAY,CACV,gBAAiB,CAAA,EACjB,gBAAiB,CAAA,EACjB,UAAW,AAAC,GAAU,CAAC,SAAE,EAAE,EAAM,yBAAI,CAAC,CACtC,SAAU,EACZ,OAKJ,WAAC,SAAI,EACH,MACE,WAAC,SAAK,YACJ,UAAC,UAAY,KACb,WAAC,kBAAK,6BAAO,AAAC,CAAA,GAAe,EAAE,AAAD,EAAG,MAAM,CAAC,oBAK5C,UAAC,OAAI,MAAO,CAAE,aAAc,EAAG,WAC7B,WAAC,SAAK,EAAC,MAAO,CAAE,MAAO,OAAQ,eAAgB,eAAgB,YAC7D,WAAC,SAAK,YACJ,UAAC,SAAK,EACJ,YAAY,6CACZ,OAAQ,UAAC,UAAc,KACvB,MAAO,EACP,SAAU,AAAC,GAAM,EAAwB,EAAE,MAAM,CAAC,KAAK,EACvD,MAAO,CAAE,MAAO,GAAI,IAEtB,WAAC,UAAM,EACL,YAAY,2BACZ,MAAO,EACP,SAAU,EACV,MAAO,CAAE,MAAO,GAAI,EACpB,UAAU,cAEV,UAAC,UAAM,CAAC,MAAM,EAAC,MAAO,mBAAgB,CAAC,OAAO,UAAE,uBAChD,UAAC,UAAM,CAAC,MAAM,EAAC,MAAO,mBAAgB,CAAC,QAAQ,UAAE,uBACjD,UAAC,UAAM,CAAC,MAAM,EAAC,MAAO,mBAAgB,CAAC,QAAQ,UAAE,uBACjD,UAAC,UAAM,CAAC,MAAM,EAAC,MAAO,mBAAgB,CAAC,OAAO,UAAE,uBAChD,UAAC,UAAM,CAAC,MAAM,EAAC,MAAO,mBAAgB,CAAC,SAAS,UAAE,6BAGtD,UAAC,SAAM,EACL,KAAM,UAAC,UAAc,KACrB,QAAS,EACT,QAAS,WACV,sBAOL,UAAC,UAAK,EACJ,QAAS,EACT,WAAY,EACZ,OAAO,KACP,QAAS,EACT,WAAY,CACV,gBAAiB,CAAA,EACjB,gBAAiB,CAAA,EACjB,UAAW,AAAC,GAAU,CAAC,SAAE,EAAE,EAAM,yCAAM,CAAC,CACxC,SAAU,EACZ,OAOJ,UAAC,SAAK,EACJ,MAAM,iCACN,KAAM,EACN,SAAU,KACR,EAAsB,CAAA,GACtB,EAAW,WAAW,GACxB,EACA,OAAQ,KACR,MAAO,aAEP,WAAC,SAAI,EACH,KAAM,EACN,OAAO,WACP,SAAU,YAEV,UAAC,SAAI,CAAC,IAAI,EACR,KAAK,SACL,MAAM,2BACN,MAAO,CACL,CAAE,SAAU,CAAA,EAAM,QAAS,4CAAU,EACtC,CACD,MAAM,sGAEN,UAAC,IACC,KAAM,EACN,YAAY,sIAGhB,UAAC,SAAI,CAAC,IAAI,EACR,KAAK,UACL,MAAM,mDACN,MAAM,wKAEN,UAAC,IACC,KAAM,EACN,YAAY,8HACZ,UAAW,IACX,SAAS,QAGb,UAAC,SAAI,CAAC,IAAI,WACR,WAAC,SAAK,YACJ,UAAC,SAAM,EAAC,KAAK,UAAU,SAAS,SAAS,KAAM,UAAC,UAAY,cAAK,6BAGjE,UAAC,SAAM,EAAC,QAAS,IAAM,EAAsB,CAAA,YAAQ,8BASnE,ECtiBM,CAAE,MAAA,EAAK,CAAE,KAAA,EAAI,CAAE,UAAA,EAAS,CAAE,CAAG,SAAU,CACvC,CAAE,SAAA,EAAQ,CAAE,CAAG,SAAK,CAEpB,GAA+B,KACnC,GAAM,CAAC,EAAS,EAAW,CAAG,GAAA,UAAQ,EAAC,CAAA,GACjC,CAAC,EAAY,EAAc,CAAG,GAAA,UAAQ,EAA4B,MAGlE,CAAC,EAAkB,EAAoB,CAAG,GAAA,UAAQ,EAAC,CAAA,GACnD,CAAC,EAAU,EAAY,CAAG,GAAA,UAAQ,EAAC,CAAA,GACnC,CAAC,EAAK,CAAG,SAAI,CAAC,OAAO,GAGrB,CAAC,EAAoB,EAAsB,CAAG,GAAA,UAAQ,EAAC,CAAA,GACvD,CAAC,EAAmB,EAAqB,CAAG,GAAA,UAAQ,EAAC,IACrD,CAAC,EAAU,EAAY,CAAG,GAAA,UAAQ,EAAC,CAAA,GAEzC,GAAA,WAAS,EAAC,KACR,IACF,EAAG,EAAE,EAEL,IAAM,EAAkB,UACtB,GAAI,CACF,EAAW,CAAA,GACX,IAAM,EAAS,MAAM,cAAW,CAAC,oBAAoB,GACrD,EAAc,GAChB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,oDAAa,GAE3B,UAAO,CAAC,IAAI,CAAC,oBACf,QAAU,CACR,EAAW,CAAA,GACb,CACF,EAaM,EAAiB,MAAO,IAC5B,GAAI,CACF,EAAY,CAAA,GACZ,MAAM,cAAW,CAAC,iBAAiB,CAAC,GACpC,SAAO,CAAC,OAAO,CAAC,oDAChB,EAAoB,CAAA,GACpB,IACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wCAAW,GACzB,SAAO,CAAC,KAAK,CAAC,wCAChB,QAAU,CACR,EAAY,CAAA,GACd,CACF,EAGM,EAAmB,UACvB,GAAI,WAAsB,SAAA,EAAY,IAAI,EAAE,CAC1C,SAAO,CAAC,KAAK,CAAC,gEACd,OACF,CAEA,GAAI,CACF,EAAY,CAAA,GACZ,MAAM,cAAW,CAAC,iBAAiB,GACnC,SAAO,CAAC,OAAO,CAAC,kCAChB,EAAsB,CAAA,GAEtB,UAAO,CAAC,IAAI,CAAC,qBACf,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wCAAW,GACzB,SAAO,CAAC,KAAK,CAAC,wCAChB,QAAU,CACR,EAAY,CAAA,GACd,CACF,EAGM,EAAsB,OAAA,SAAA,EAAY,SAAS,GAAI,CAAA,EAErD,GAAI,EACF,MACE,UAAC,eAAa,WACZ,WAAC,OAAI,MAAO,CAAE,UAAW,SAAU,QAAS,QAAS,YACnD,UAAC,SAAI,EAAC,KAAK,UACX,UAAC,OAAI,MAAO,CAAE,UAAW,EAAG,WAC1B,UAAC,IAAK,KAAK,qBAAY,+DAOjC,GAAI,CAAC,EACH,MACE,UAAC,eAAa,WACZ,UAAC,SAAI,WACH,WAAC,OAAI,MAAO,CAAE,UAAW,SAAU,QAAS,QAAS,YACnD,UAAC,GAAmB,MAAO,CAAE,SAAU,GAAI,MAAO,UAAW,aAAc,EAAG,IAC9E,UAAC,IAAM,MAAO,WAAG,+CACjB,UAAC,IAAK,KAAK,qBAAY,qDACvB,UAAC,OAAI,MAAO,CAAE,UAAW,EAAG,WAC1B,UAAC,SAAM,EAAC,KAAK,UAAU,QAAS,IAAM,UAAO,CAAC,IAAI,CAAC,6BAAqB,kDAWpF,GAAI,CAAC,EACH,MACE,UAAC,eAAa,WACZ,UAAC,SAAI,WACH,UAAC,SAAK,EACJ,QAAQ,2BACR,YAAY,iOACZ,KAAK,UACL,QAAQ,IACR,OACE,UAAC,SAAM,EAAC,KAAK,QAAQ,QAAS,IAAM,UAAO,CAAC,IAAI,CAAC,uBAAe,mCAa5E,IAAM,EAAY,CAChB,CACE,IAAK,OACL,MAAO,2BACP,KAAM,UAAC,SAAY,KACnB,QA/GmB,KAChB,IACL,EAAK,cAAc,CAAC,CAClB,KAAM,EAAW,IAAI,CACrB,YAAa,EAAW,WAAW,EAAI,EACzC,GACA,EAAoB,CAAA,IACtB,CAyGE,EACA,CACE,IAAK,SACL,MAAO,2BACP,KAAM,UAAC,SAAc,KACrB,OAAQ,CAAA,EACR,QAAS,IAAM,EAAsB,CAAA,EACvC,EACD,CAED,MACE,WAAC,eAAa,YAEZ,UAAC,SAAI,EACH,MAAO,CAAE,aAAc,EAAG,EAC1B,MACE,GACE,UAAC,SAAQ,EACP,KAAM,CAAE,MAAO,CAAU,EACzB,UAAU,cACV,QAAS,CAAC,QAAQ,UAElB,UAAC,SAAM,EACL,KAAK,OACL,KAAM,UAAC,SAAY,KACnB,MAAO,CAAE,SAAU,EAAG,eAM9B,WAAC,SAAK,EAAC,KAAK,QAAQ,MAAM,mBACxB,UAAC,SAAM,EACL,KAAM,GACN,KAAM,UAAC,SAAY,KACnB,MAAO,CACL,gBAAiB,UACjB,SAAU,EACZ,IAEF,WAAC,iBACC,UAAC,IAAM,MAAO,EAAG,MAAO,CAAE,OAAQ,EAAG,aAAc,CAAE,WAClD,EAAW,IAAI,GAElB,WAAC,SAAK,YACH,EAAW,SAAS,EACnB,UAAC,SAAG,EACF,KAAM,UAAC,SAAa,KACpB,MAAM,gBACP,uBAIH,UAAC,IAAK,KAAK,qBACR,EAAW,WAAW,EAAI,sEAQrC,UAAC,IACC,WAAY,EACZ,UAAW,IAIb,UAAC,SAAK,EACJ,MAAM,uCACN,KAAM,EACN,SAAU,KACR,EAAoB,CAAA,GACpB,EAAK,WAAW,GAClB,EACA,OAAQ,KACR,MAAO,aAEP,WAAC,SAAI,EACH,KAAM,EACN,OAAO,WACP,SAAU,YAEV,UAAC,SAAI,CAAC,IAAI,EACR,KAAK,OACL,MAAM,2BACN,MAAO,CACL,CAAE,SAAU,CAAA,EAAM,QAAS,4CAAU,EACrC,CAAE,IAAK,EAAG,IAAK,GAAI,QAAS,oFAAoB,EACjD,UAED,UAAC,SAAK,EAAC,YAAY,iDAErB,UAAC,SAAI,CAAC,IAAI,EACR,KAAK,cACL,MAAM,2BACN,MAAO,CACL,CAAE,IAAK,IAAK,QAAS,uEAAiB,EACvC,UAED,UAAC,IACC,KAAM,EACN,YAAY,qEACZ,SAAS,IACT,UAAW,QAGf,UAAC,SAAI,CAAC,IAAI,EAAC,MAAO,CAAE,aAAc,EAAG,UAAW,OAAQ,WACtD,WAAC,SAAK,YACJ,UAAC,SAAM,EAAC,QAAS,KACf,EAAoB,CAAA,GACpB,EAAK,WAAW,GAClB,WAAG,iBAGH,UAAC,SAAM,EACL,KAAK,UACL,SAAS,SACT,QAAS,EACT,KAAM,UAAC,SAAY,cACpB,2BAST,WAAC,SAAK,EACJ,MACE,WAAC,SAAK,YACJ,UAAC,UAAyB,EAAC,MAAO,CAAE,MAAO,SAAU,IACrD,UAAC,IAAK,KAAK,kBAAS,4CAGxB,KAAM,EACN,SAAU,KACR,EAAsB,CAAA,GACtB,EAAqB,IACvB,EACA,OAAQ,KACR,MAAO,cAEP,UAAC,SAAK,EACJ,QAAQ,+DACR,YACE,WAAC,iBACC,UAAC,cAAE,+CACH,WAAC,gBACC,UAAC,eAAG,mFACJ,UAAC,eAAG,qDACJ,UAAC,eAAG,2DACJ,UAAC,eAAG,2DAIV,KAAK,QACL,QAAQ,IACR,MAAO,CAAE,aAAc,EAAG,IAG5B,UAAC,OAAI,MAAO,CAAE,aAAc,EAAG,WAC7B,WAAC,IAAK,MAAM,cAAC,+CACF,UAAC,IAAK,IAAI,mBAAE,SAAA,EAAY,IAAI,GAAQ,8CAIjD,UAAC,SAAK,EACJ,YAAa,CAAC,gCAAI,QAAE,SAAA,EAAY,IAAI,CAAC,CAAC,CACtC,MAAO,EACP,SAAU,AAAC,GAAM,EAAqB,EAAE,MAAM,CAAC,KAAK,EACpD,MAAO,CAAE,aAAc,EAAG,IAG5B,UAAC,OAAI,MAAO,CAAE,UAAW,OAAQ,WAC/B,WAAC,SAAK,YACJ,UAAC,SAAM,EACL,QAAS,KACP,EAAsB,CAAA,GACtB,EAAqB,IACvB,WACD,iBAGD,UAAC,SAAM,EACL,KAAK,UACL,MAAM,IACN,QAAS,EACT,SAAU,WAAsB,SAAA,EAAY,IAAI,EAChD,QAAS,EACT,KAAM,UAAC,SAAc,cACtB,oDAQb"}