{"version": 3, "sources": ["src/pages/team-management/components/TeamMemberManagement.tsx", "src/pages/team-management/index.tsx"], "sourcesContent": ["/**\n * 团队成员与邀请管理组件\n *\n * 功能特性：\n * - 查看团队成员列表及详细信息\n * - 查看团队邀请列表及状态管理\n * - 添加新成员（通过邮箱邀请）\n * - 移除团队现有成员\n * - 取消待处理的邀请\n * - 批量操作支持\n * - 成员和邀请搜索筛选\n *\n * 权限控制：\n * - 只有团队创建者可以进行成员管理操作\n * - 创建者不能移除自己\n * - 提供详细的操作确认\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Modal,\n  Form,\n  message,\n  Tag,\n  Typography,\n  Popconfirm,\n  Select,\n  Tabs,\n  Tooltip\n} from 'antd';\nimport {\n  UserAddOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  MailOutlined,\n  UserOutlined,\n  CrownOutlined,\n  ReloadOutlined\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport dayjs from 'dayjs';\n\n// 导入服务和类型\nimport { TeamService } from '@/services/team';\nimport { InvitationService } from '@/services/invitation';\nimport type { TeamDetailResponse, TeamMemberResponse, TeamInvitationResponse } from '@/types/api';\nimport { InvitationStatus } from '@/types/api';\nimport InvitationStatusComponent from '@/components/InvitationStatus';\n\nconst { Text } = Typography;\nconst { TextArea } = Input;\n\ninterface TeamMemberManagementProps {\n  teamDetail: TeamDetailResponse;\n  onRefresh: () => void;\n}\n\nconst TeamMemberManagement: React.FC<TeamMemberManagementProps> = ({\n  teamDetail,\n  onRefresh\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [members, setMembers] = useState<TeamMemberResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [inviteModalVisible, setInviteModalVisible] = useState(false);\n  const [inviteForm] = Form.useForm();\n\n  // 邀请管理相关状态\n  const [invitations, setInvitations] = useState<TeamInvitationResponse[]>([]);\n  const [invitationLoading, setInvitationLoading] = useState(false);\n  const [invitationSearchText, setInvitationSearchText] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n  const [activeTab, setActiveTab] = useState('members');\n\n  useEffect(() => {\n    fetchMembers();\n    fetchInvitations();\n  }, []);\n\n  const fetchMembers = async () => {\n    try {\n      setLoading(true);\n      const memberList = await TeamService.getCurrentTeamMembers();\n      setMembers(memberList || []);\n    } catch (error) {\n      console.error('获取团队成员失败:', error);\n      message.error('获取团队成员失败');\n      setMembers([]); // 确保在错误时设置为空数组\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取邀请列表\n  const fetchInvitations = async () => {\n    try {\n      setInvitationLoading(true);\n      const invitationList = await InvitationService.getCurrentTeamInvitations();\n      setInvitations(invitationList || []);\n    } catch (error) {\n      console.error('获取邀请列表失败:', error);\n      message.error('获取邀请列表失败');\n      setInvitations([]);\n    } finally {\n      setInvitationLoading(false);\n    }\n  };\n\n  // 邀请新成员\n  const handleInviteMembers = async (values: { emails: string; message?: string }) => {\n    try {\n      const emailList = values.emails\n        .split('\\n')\n        .map(email => email.trim())\n        .filter(email => email);\n\n      // 使用新的邀请链接功能\n      const response = await InvitationService.sendInvitations({\n        emails: emailList,\n        message: values.message\n      });\n\n      // 显示详细的发送结果\n      if (response.successCount > 0) {\n        message.success(`成功发送 ${response.successCount} 个邀请`);\n\n        // 显示邀请链接（可选：在开发环境中显示）\n        if (process.env.NODE_ENV === 'development') {\n          console.log('邀请链接:', response.invitations.map(inv => ({\n            email: inv.email,\n            link: inv.invitationLink\n          })));\n        }\n      }\n\n      if (response.failureCount > 0) {\n        message.warning(`${response.failureCount} 个邀请发送失败`);\n      }\n\n      setInviteModalVisible(false);\n      inviteForm.resetFields();\n      fetchMembers();\n      fetchInvitations(); // 刷新邀请列表\n      onRefresh(); // 刷新团队详情\n    } catch (error) {\n      console.error('邀请成员失败:', error);\n      message.error('邀请成员失败');\n    }\n  };\n\n  // 取消邀请\n  const handleCancelInvitation = async (invitationId: number) => {\n    try {\n      await InvitationService.cancelInvitation(invitationId);\n      message.success('邀请取消成功');\n      fetchInvitations();\n      onRefresh();\n    } catch (error) {\n      console.error('取消邀请失败:', error);\n      message.error('取消邀请失败');\n    }\n  };\n\n  // 移除单个成员\n  const handleRemoveMember = async (member: TeamMemberResponse) => {\n    try {\n      await TeamService.removeMember(member.id);\n      message.success(`已移除成员：${member.name}`);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('移除成员失败:', error);\n      message.error('移除成员失败');\n    }\n  };\n\n  // 批量移除成员\n  const handleBatchRemove = async () => {\n    try {\n      const memberIds = selectedRowKeys as number[];\n      for (const memberId of memberIds) {\n        await TeamService.removeMember(memberId);\n      }\n      message.success(`已移除 ${memberIds.length} 名成员`);\n      setSelectedRowKeys([]);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('批量移除成员失败:', error);\n      message.error('批量移除成员失败');\n    }\n  };\n\n  // 筛选成员\n  const filteredMembers = (members || []).filter(member =>\n    member.name.toLowerCase().includes(searchText.toLowerCase()) ||\n    member.email.toLowerCase().includes(searchText.toLowerCase())\n  );\n\n  // 停用/启用成员\n  const handleToggleMemberStatus = async (member: TeamMemberResponse, isActive: boolean) => {\n    try {\n      await TeamService.updateMemberStatus(member.id, isActive);\n      message.success(`已${isActive ? '启用' : '停用'}成员：${member.name}`);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('更新成员状态失败:', error);\n      message.error('更新成员状态失败');\n    }\n  };\n\n  // 表格列配置\n  const columns: ColumnsType<TeamMemberResponse> = [\n    {\n      title: '姓名',\n      dataIndex: 'name',\n      key: 'name',\n      render: (name: string, record) => (\n        <Space>\n          <Text strong>{name}</Text>\n          {record.isCreator && (\n            <Tag icon={<CrownOutlined />} color=\"gold\">创建者</Tag>\n          )}\n        </Space>\n      ),\n    },\n    {\n      title: '邮箱',\n      dataIndex: 'email',\n      key: 'email',\n      render: (email: string) => (\n        <Text type=\"secondary\">{email}</Text>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'isActive',\n      key: 'status',\n      width: 100,\n      render: (isActive: boolean) => (\n        <Tag color={isActive ? 'green' : 'red'}>\n          {isActive ? '启用' : '停用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '加入时间',\n      dataIndex: 'assignedAt',\n      key: 'assignedAt',\n      width: 150,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '最后访问',\n      dataIndex: 'lastAccessTime',\n      key: 'lastAccessTime',\n      width: 150,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      render: (_, record) => {\n        if (record.isCreator) {\n          return <Text type=\"secondary\">-</Text>;\n        }\n\n        return (\n          <Space>\n            {record.isActive ? (\n              <Button\n                type=\"text\"\n                size=\"small\"\n                onClick={() => handleToggleMemberStatus(record, false)}\n              >\n                停用\n              </Button>\n            ) : (\n              <Button\n                type=\"text\"\n                size=\"small\"\n                onClick={() => handleToggleMemberStatus(record, true)}\n              >\n                启用\n              </Button>\n            )}\n            <Popconfirm\n              title=\"确认移除成员\"\n              description={`确定要移除成员 ${record.name} 吗？此操作不可恢复。`}\n              onConfirm={() => handleRemoveMember(record)}\n              okText=\"确认\"\n              cancelText=\"取消\"\n              okType=\"danger\"\n            >\n              <Button\n                type=\"text\"\n                danger\n                size=\"small\"\n                icon={<DeleteOutlined />}\n              >\n                移除\n              </Button>\n            </Popconfirm>\n          </Space>\n        );\n      },\n    },\n  ];\n\n  // 行选择配置\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: setSelectedRowKeys,\n    getCheckboxProps: (record: TeamMemberResponse) => ({\n      disabled: record.isCreator, // 创建者不能被选择\n    }),\n  };\n\n  // 邀请表格列定义\n  const invitationColumns: ColumnsType<TeamInvitationResponse> = [\n    {\n      title: '被邀请人',\n      key: 'invitee',\n      render: (_, record) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text strong>{record.inviteeName || '未注册用户'}</Text>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            <MailOutlined /> {record.inviteeEmail}\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '邀请状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status, record) => (\n        <InvitationStatusComponent\n          status={status}\n          isExpired={record.isExpired}\n        />\n      ),\n      filters: [\n        { text: '待确认', value: InvitationStatus.PENDING },\n        { text: '已接受', value: InvitationStatus.ACCEPTED },\n        { text: '已拒绝', value: InvitationStatus.REJECTED },\n        { text: '已过期', value: InvitationStatus.EXPIRED },\n        { text: '已取消', value: InvitationStatus.CANCELLED },\n      ],\n      onFilter: (value, record) => record.status === value,\n    },\n    {\n      title: '邀请时间',\n      dataIndex: 'invitedAt',\n      key: 'invitedAt',\n      render: (time) => (\n        <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>\n          {dayjs(time).format('MM-DD HH:mm')}\n        </Tooltip>\n      ),\n      sorter: (a, b) => dayjs(a.invitedAt).unix() - dayjs(b.invitedAt).unix(),\n    },\n    {\n      title: '过期时间',\n      dataIndex: 'expiresAt',\n      key: 'expiresAt',\n      render: (time, record) => {\n        const isExpired = record.isExpired;\n        return (\n          <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>\n            <Text type={isExpired ? 'danger' : 'secondary'}>\n              {dayjs(time).format('MM-DD HH:mm')}\n            </Text>\n          </Tooltip>\n        );\n      },\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => {\n        if (record.status === InvitationStatus.PENDING && !record.isExpired) {\n          return (\n            <Popconfirm\n              title=\"确定要取消这个邀请吗？\"\n              onConfirm={() => handleCancelInvitation(record.id)}\n              okText=\"确定\"\n              cancelText=\"取消\"\n            >\n              <Button size=\"small\" danger icon={<DeleteOutlined />}>\n                取消邀请\n              </Button>\n            </Popconfirm>\n          );\n        }\n        return <Text type=\"secondary\">-</Text>;\n      },\n    },\n  ];\n\n  // 过滤邀请列表\n  const filteredInvitations = invitations.filter(invitation => {\n    const matchesSearch = !invitationSearchText ||\n      invitation.inviteeEmail.toLowerCase().includes(invitationSearchText.toLowerCase()) ||\n      (invitation.inviteeName && invitation.inviteeName.toLowerCase().includes(invitationSearchText.toLowerCase()));\n\n    const matchesStatus = !statusFilter || invitation.status === statusFilter;\n\n    return matchesSearch && matchesStatus;\n  });\n\n  // 选项卡配置\n  const tabItems = [\n    {\n      key: 'members',\n      label: (\n        <Space>\n          <UserOutlined />\n          团队成员 ({(members || []).length})\n        </Space>\n      ),\n      children: (\n        <div>\n          {/* 成员操作栏 */}\n          <Card style={{ marginBottom: 16 }}>\n            <Space style={{ width: '100%', justifyContent: 'space-between' }}>\n              <Space>\n                <Input\n                  placeholder=\"搜索成员姓名或邮箱\"\n                  prefix={<SearchOutlined />}\n                  value={searchText}\n                  onChange={(e) => setSearchText(e.target.value)}\n                  style={{ width: 250 }}\n                />\n                {selectedRowKeys.length > 0 && (\n                  <Popconfirm\n                    title={`确定要移除选中的 ${selectedRowKeys.length} 名成员吗？`}\n                    onConfirm={handleBatchRemove}\n                    okText=\"确定\"\n                    cancelText=\"取消\"\n                  >\n                    <Button danger icon={<DeleteOutlined />}>\n                      批量移除 ({selectedRowKeys.length})\n                    </Button>\n                  </Popconfirm>\n                )}\n              </Space>\n              <Button\n                type=\"primary\"\n                icon={<UserAddOutlined />}\n                onClick={() => setInviteModalVisible(true)}\n              >\n                邀请成员\n              </Button>\n            </Space>\n          </Card>\n\n          {/* 成员列表 */}\n          <Card>\n            <Table\n              columns={columns}\n              dataSource={filteredMembers}\n              rowKey=\"id\"\n              loading={loading}\n              rowSelection={rowSelection}\n              pagination={{\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total) => `共 ${total} 名成员`,\n                pageSize: 10,\n              }}\n            />\n          </Card>\n        </div>\n      ),\n    },\n    {\n      key: 'invitations',\n      label: (\n        <Space>\n          <MailOutlined />\n          邀请记录 ({(invitations || []).length})\n        </Space>\n      ),\n      children: (\n        <div>\n          {/* 邀请操作栏 */}\n          <Card style={{ marginBottom: 16 }}>\n            <Space style={{ width: '100%', justifyContent: 'space-between' }}>\n              <Space>\n                <Input\n                  placeholder=\"搜索邮箱或姓名\"\n                  prefix={<SearchOutlined />}\n                  value={invitationSearchText}\n                  onChange={(e) => setInvitationSearchText(e.target.value)}\n                  style={{ width: 200 }}\n                />\n                <Select\n                  placeholder=\"筛选状态\"\n                  value={statusFilter}\n                  onChange={setStatusFilter}\n                  style={{ width: 120 }}\n                  allowClear\n                >\n                  <Select.Option value={InvitationStatus.PENDING}>待确认</Select.Option>\n                  <Select.Option value={InvitationStatus.ACCEPTED}>已接受</Select.Option>\n                  <Select.Option value={InvitationStatus.REJECTED}>已拒绝</Select.Option>\n                  <Select.Option value={InvitationStatus.EXPIRED}>已过期</Select.Option>\n                  <Select.Option value={InvitationStatus.CANCELLED}>已取消</Select.Option>\n                </Select>\n              </Space>\n              <Button\n                icon={<ReloadOutlined />}\n                onClick={fetchInvitations}\n                loading={invitationLoading}\n              >\n                刷新\n              </Button>\n            </Space>\n          </Card>\n\n          {/* 邀请列表 */}\n          <Card>\n            <Table\n              columns={invitationColumns}\n              dataSource={filteredInvitations}\n              rowKey=\"id\"\n              loading={invitationLoading}\n              pagination={{\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total) => `共 ${total} 条邀请记录`,\n                pageSize: 10,\n              }}\n            />\n          </Card>\n        </div>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        items={tabItems}\n        size=\"large\"\n      />\n\n      {/* 邀请成员弹窗 */}\n\n      {/* 邀请成员弹窗 */}\n      <Modal\n        title=\"邀请新成员\"\n        open={inviteModalVisible}\n        onCancel={() => {\n          setInviteModalVisible(false);\n          inviteForm.resetFields();\n        }}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={inviteForm}\n          layout=\"vertical\"\n          onFinish={handleInviteMembers}\n        >\n          <Form.Item\n            name=\"emails\"\n            label=\"邮箱地址\"\n            rules={[\n              { required: true, message: '请输入邮箱地址' },\n            ]}\n            extra=\"每行一个邮箱地址，支持批量邀请\"\n          >\n            <TextArea\n              rows={6}\n              placeholder=\"请输入邮箱地址，每行一个&#10;例如：&#10;<EMAIL>&#10;<EMAIL>\"\n            />\n          </Form.Item>\n          <Form.Item\n            name=\"message\"\n            label=\"邀请消息（可选）\"\n            extra=\"您可以添加一些邀请消息，让被邀请人更好地了解邀请意图\"\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"欢迎加入我们的团队！我们期待与您一起工作...\"\n              maxLength={500}\n              showCount\n            />\n          </Form.Item>\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\" icon={<MailOutlined />}>\n                发送邀请\n              </Button>\n              <Button onClick={() => setInviteModalVisible(false)}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default TeamMemberManagement;\n", "/**\n * 集成团队管理页面\n *\n * 功能特性：\n * - 统一的团队管理界面，包含多个功能模块\n * - 团队名称卡片显示，提升用户体验\n * - 合并的成员与邀请管理功能\n * - 团队设置功能\n * - 权限控制，只有团队创建者可以访问管理功能\n *\n * 模块组织：\n * - 团队成员与邀请管理：查看、添加、移除团队成员，管理邀请\n * - 团队设置：编辑团队信息、删除团队\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport {\n  Tabs,\n  Card,\n  Alert,\n  Spin,\n  Typography,\n  Space,\n  Tag,\n  Avatar,\n  Button,\n  Dropdown,\n  Modal,\n  Form,\n  Input,\n  message\n} from 'antd';\nimport {\n  UserOutlined,\n  TeamOutlined,\n  InfoCircleOutlined,\n  CrownOutlined,\n  MoreOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  SaveOutlined,\n  ExclamationCircleOutlined\n} from '@ant-design/icons';\nimport { history } from '@umijs/max';\n\n// 导入子组件\nimport TeamMemberManagement from './components/TeamMemberManagement';\n\n// 导入服务和类型\nimport { TeamService } from '@/services/team';\nimport type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\n\nconst TeamManagementPage: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);\n  const [activeTab, setActiveTab] = useState('members');\n\n  // 团队编辑相关状态\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [updating, setUpdating] = useState(false);\n  const [form] = Form.useForm();\n\n  // 团队删除相关状态\n  const [deleteModalVisible, setDeleteModalVisible] = useState(false);\n  const [deleteConfirmText, setDeleteConfirmText] = useState('');\n  const [deleting, setDeleting] = useState(false);\n\n  useEffect(() => {\n    fetchTeamDetail();\n  }, []);\n\n  const fetchTeamDetail = async () => {\n    try {\n      setLoading(true);\n      const detail = await TeamService.getCurrentTeamDetail();\n      setTeamDetail(detail);\n    } catch (error) {\n      console.error('获取团队详情失败:', error);\n      // 如果获取失败，可能是没有选择团队，跳转到个人中心页面\n      history.push('/personal-center');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 处理编辑团队\n  const handleEditTeam = () => {\n    if (!teamDetail) return;\n    form.setFieldsValue({\n      name: teamDetail.name,\n      description: teamDetail.description || '',\n    });\n    setEditModalVisible(true);\n  };\n\n  // 保存团队信息\n  const handleSaveTeam = async (values: UpdateTeamRequest) => {\n    try {\n      setUpdating(true);\n      await TeamService.updateCurrentTeam(values);\n      message.success('团队信息更新成功');\n      setEditModalVisible(false);\n      fetchTeamDetail();\n    } catch (error) {\n      console.error('更新团队失败:', error);\n      message.error('更新团队失败');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  // 删除团队\n  const handleDeleteTeam = async () => {\n    if (deleteConfirmText !== teamDetail?.name) {\n      message.error('请输入正确的团队名称');\n      return;\n    }\n\n    try {\n      setDeleting(true);\n      await TeamService.deleteCurrentTeam();\n      message.success('团队已删除');\n      setDeleteModalVisible(false);\n      // 删除成功后跳转到团队选择页面\n      history.push('/user/team-select');\n    } catch (error) {\n      console.error('删除团队失败:', error);\n      message.error('删除团队失败');\n    } finally {\n      setDeleting(false);\n    }\n  };\n\n  // 权限检查：只有团队创建者可以访问管理功能\n  const hasManagePermission = teamDetail?.isCreator || false;\n\n  if (loading) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Spin size=\"large\" />\n          <div style={{ marginTop: 16 }}>\n            <Text type=\"secondary\">正在加载团队信息...</Text>\n          </div>\n        </div>\n      </PageContainer>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <PageContainer>\n        <Card>\n          <div style={{ textAlign: 'center', padding: '50px 0' }}>\n            <InfoCircleOutlined style={{ fontSize: 48, color: '#faad14', marginBottom: 16 }} />\n            <Title level={4}>未找到团队信息</Title>\n            <Text type=\"secondary\">请先选择一个团队</Text>\n            <div style={{ marginTop: 16 }}>\n              <Button type=\"primary\" onClick={() => history.push('/personal-center')}>\n                返回个人中心\n              </Button>\n            </div>\n          </div>\n        </Card>\n      </PageContainer>\n    );\n  }\n\n  // 权限不足提示\n  if (!hasManagePermission) {\n    return (\n      <PageContainer>\n        <Card>\n          <Alert\n            message=\"权限不足\"\n            description=\"只有团队创建者可以访问团队管理功能。如果您需要管理权限，请联系团队创建者。\"\n            type=\"warning\"\n            showIcon\n            action={\n              <Button size=\"small\" onClick={() => history.push('/dashboard')}>\n                返回首页\n              </Button>\n            }\n          />\n        </Card>\n      </PageContainer>\n    );\n  }\n\n  // 选项卡配置\n  const tabItems = [\n    {\n      key: 'members',\n      label: (\n        <Space>\n          <UserOutlined />\n          成员与邀请管理\n        </Space>\n      ),\n      children: (\n        <TeamMemberManagement\n          teamDetail={teamDetail}\n          onRefresh={fetchTeamDetail}\n        />\n      ),\n    },\n  ];\n\n  // 操作菜单项\n  const menuItems = [\n    {\n      key: 'edit',\n      label: '编辑团队',\n      icon: <EditOutlined />,\n      onClick: handleEditTeam,\n    },\n    {\n      key: 'delete',\n      label: '删除团队',\n      icon: <DeleteOutlined />,\n      danger: true,\n      onClick: () => setDeleteModalVisible(true),\n    },\n  ];\n\n  return (\n    <PageContainer>\n      {/* 团队名称卡片 */}\n      <Card\n        style={{ marginBottom: 16 }}\n        extra={\n          hasManagePermission && (\n            <Dropdown\n              menu={{ items: menuItems }}\n              placement=\"bottomRight\"\n              trigger={['click']}\n            >\n              <Button\n                type=\"text\"\n                icon={<MoreOutlined />}\n                style={{ fontSize: 16 }}\n              />\n            </Dropdown>\n          )\n        }\n      >\n        <Space size=\"large\" align=\"center\">\n          <Avatar\n            size={48}\n            icon={<TeamOutlined />}\n            style={{\n              backgroundColor: '#1890ff',\n              fontSize: 20,\n            }}\n          />\n          <div>\n            <Title level={3} style={{ margin: 0, marginBottom: 4 }}>\n              {teamDetail.name}\n            </Title>\n            <Space>\n              {teamDetail.isCreator && (\n                <Tag\n                  icon={<CrownOutlined />}\n                  color=\"gold\"\n                >\n                  管理员\n                </Tag>\n              )}\n              <Text type=\"secondary\">\n                {teamDetail.description || '这个团队还没有描述'}\n              </Text>\n            </Space>\n          </div>\n        </Space>\n      </Card>\n\n      {/* 管理功能选项卡 */}\n      <Card>\n        <Tabs\n          activeKey={activeTab}\n          onChange={setActiveTab}\n          items={tabItems}\n          size=\"large\"\n          tabBarStyle={{ marginBottom: 24 }}\n        />\n      </Card>\n\n      {/* 编辑团队模态框 */}\n      <Modal\n        title=\"编辑团队信息\"\n        open={editModalVisible}\n        onCancel={() => {\n          setEditModalVisible(false);\n          form.resetFields();\n        }}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSaveTeam}\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"团队名称\"\n            rules={[\n              { required: true, message: '请输入团队名称' },\n              { min: 2, max: 50, message: '团队名称长度应在2-50个字符之间' },\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" />\n          </Form.Item>\n          <Form.Item\n            name=\"description\"\n            label=\"团队描述\"\n            rules={[\n              { max: 200, message: '团队描述不能超过200个字符' },\n            ]}\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入团队描述（可选）\"\n              showCount\n              maxLength={200}\n            />\n          </Form.Item>\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => {\n                setEditModalVisible(false);\n                form.resetFields();\n              }}>\n                取消\n              </Button>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={updating}\n                icon={<SaveOutlined />}\n              >\n                保存\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 删除团队确认模态框 */}\n      <Modal\n        title={\n          <Space>\n            <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />\n            <Text type=\"danger\">删除团队确认</Text>\n          </Space>\n        }\n        open={deleteModalVisible}\n        onCancel={() => {\n          setDeleteModalVisible(false);\n          setDeleteConfirmText('');\n        }}\n        footer={null}\n        width={600}\n      >\n        <Alert\n          message=\"警告：此操作不可恢复\"\n          description={\n            <div>\n              <p>删除团队将会：</p>\n              <ul>\n                <li>永久删除团队及所有相关数据</li>\n                <li>移除所有团队成员</li>\n                <li>清除团队设置和配置</li>\n                <li>无法恢复任何数据</li>\n              </ul>\n            </div>\n          }\n          type=\"error\"\n          showIcon\n          style={{ marginBottom: 24 }}\n        />\n\n        <div style={{ marginBottom: 16 }}>\n          <Text strong>\n            请输入团队名称 \"<Text code>{teamDetail?.name}</Text>\" 来确认删除：\n          </Text>\n        </div>\n\n        <Input\n          placeholder={`请输入：${teamDetail?.name}`}\n          value={deleteConfirmText}\n          onChange={(e) => setDeleteConfirmText(e.target.value)}\n          style={{ marginBottom: 24 }}\n        />\n\n        <div style={{ textAlign: 'right' }}>\n          <Space>\n            <Button\n              onClick={() => {\n                setDeleteModalVisible(false);\n                setDeleteConfirmText('');\n              }}\n            >\n              取消\n            </Button>\n            <Button\n              type=\"primary\"\n              danger\n              loading={deleting}\n              disabled={deleteConfirmText !== teamDetail?.name}\n              onClick={handleDeleteTeam}\n              icon={<DeleteOutlined />}\n            >\n              确认删除团队\n            </Button>\n          </Space>\n        </div>\n      </Modal>\n    </PageContainer>\n  );\n};\n\nexport default TeamManagementPage;\n"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;;;CAgBC;;;;4BAylBD;;;eAAA;;;;;;;wEAvlB2C;6BAgBpC;8BASA;uEAEW;6BAGU;mCACM;4BAED;kFACK;;;;;;;;;;AAEtC,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;AAO1B,MAAM,uBAA4D,CAAC,EACjE,UAAU,EACV,SAAS,EACV;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAc,EAAE;IACtE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,WAAW,GAAG,UAAI,CAAC,OAAO;IAEjC,WAAW;IACX,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAA2B,EAAE;IAC3E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,eAAQ,EAAC;IAC3D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,IAAA,eAAQ,EAAC;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAS;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;IAE3C,IAAA,gBAAS,EAAC;QACR;QACA;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,aAAa,MAAM,iBAAW,CAAC,qBAAqB;YAC1D,WAAW,cAAc,EAAE;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;YACd,WAAW,EAAE,GAAG,eAAe;QACjC,SAAU;YACR,WAAW;QACb;IACF;IAEA,SAAS;IACT,MAAM,mBAAmB;QACvB,IAAI;YACF,qBAAqB;YACrB,MAAM,iBAAiB,MAAM,6BAAiB,CAAC,yBAAyB;YACxE,eAAe,kBAAkB,EAAE;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;YACd,eAAe,EAAE;QACnB,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,QAAQ;IACR,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,YAAY,OAAO,MAAM,CAC5B,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI,IACvB,MAAM,CAAC,CAAA,QAAS;YAEnB,aAAa;YACb,MAAM,WAAW,MAAM,6BAAiB,CAAC,eAAe,CAAC;gBACvD,QAAQ;gBACR,SAAS,OAAO,OAAO;YACzB;YAEA,YAAY;YACZ,IAAI,SAAS,YAAY,GAAG,GAAG;gBAC7B,aAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,SAAS,YAAY,CAAC,IAAI,CAAC;gBAIjD,QAAQ,GAAG,CAAC,SAAS,SAAS,WAAW,CAAC,GAAG,CAAC,CAAA,MAAQ,CAAA;wBACpD,OAAO,IAAI,KAAK;wBAChB,MAAM,IAAI,cAAc;oBAC1B,CAAA;YAEJ;YAEA,IAAI,SAAS,YAAY,GAAG,GAC1B,aAAO,CAAC,OAAO,CAAC,CAAC,EAAE,SAAS,YAAY,CAAC,QAAQ,CAAC;YAGpD,sBAAsB;YACtB,WAAW,WAAW;YACtB;YACA,oBAAoB,SAAS;YAC7B,aAAa,SAAS;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,OAAO;IACP,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,MAAM,6BAAiB,CAAC,gBAAgB,CAAC;YACzC,aAAO,CAAC,OAAO,CAAC;YAChB;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,SAAS;IACT,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,iBAAW,CAAC,YAAY,CAAC,OAAO,EAAE;YACxC,aAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC;YACtC;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,YAAY;YAClB,KAAK,MAAM,YAAY,UACrB,MAAM,iBAAW,CAAC,YAAY,CAAC;YAEjC,aAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,UAAU,MAAM,CAAC,IAAI,CAAC;YAC7C,mBAAmB,EAAE;YACrB;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,OAAO;IACP,MAAM,kBAAkB,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,SAC7C,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAG5D,UAAU;IACV,MAAM,2BAA2B,OAAO,QAA4B;QAClE,IAAI;YACF,MAAM,iBAAW,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE;YAChD,aAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW,OAAO,KAAK,GAAG,EAAE,OAAO,IAAI,CAAC,CAAC;YAC7D;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,QAAQ;IACR,MAAM,UAA2C;QAC/C;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,MAAc,uBACrB,2BAAC,WAAK;;sCACJ,2BAAC;4BAAK,MAAM;sCAAE;;;;;;wBACb,OAAO,SAAS,kBACf,2BAAC,SAAG;4BAAC,oBAAM,2BAAC,oBAAa;;;;;4BAAK,OAAM;sCAAO;;;;;;;;;;;;QAInD;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,sBACP,2BAAC;oBAAK,MAAK;8BAAa;;;;;;QAE5B;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,yBACP,2BAAC,SAAG;oBAAC,OAAO,WAAW,UAAU;8BAC9B,WAAW,OAAO;;;;;;QAGzB;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;QAC7D;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;QAC7D;QACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG;gBACV,IAAI,OAAO,SAAS,EAClB,qBAAO,2BAAC;oBAAK,MAAK;8BAAY;;;;;;gBAGhC,qBACE,2BAAC,WAAK;;wBACH,OAAO,QAAQ,iBACd,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAK;4BACL,SAAS,IAAM,yBAAyB,QAAQ;sCACjD;;;;;iDAID,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAK;4BACL,SAAS,IAAM,yBAAyB,QAAQ;sCACjD;;;;;;sCAIH,2BAAC,gBAAU;4BACT,OAAM;4BACN,aAAa,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,WAAW,CAAC;4BAChD,WAAW,IAAM,mBAAmB;4BACpC,QAAO;4BACP,YAAW;4BACX,QAAO;sCAEP,cAAA,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAM;gCACN,MAAK;gCACL,oBAAM,2BAAC,qBAAc;;;;;0CACtB;;;;;;;;;;;;;;;;;YAMT;QACF;KACD;IAED,QAAQ;IACR,MAAM,eAAe;QACnB;QACA,UAAU;QACV,kBAAkB,CAAC,SAAgC,CAAA;gBACjD,UAAU,OAAO,SAAS;YAC5B,CAAA;IACF;IAEA,UAAU;IACV,MAAM,oBAAyD;QAC7D;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,2BAAC,WAAK;oBAAC,WAAU;oBAAW,MAAM;;sCAChC,2BAAC;4BAAK,MAAM;sCAAE,OAAO,WAAW,IAAI;;;;;;sCACpC,2BAAC;4BAAK,MAAK;4BAAY,OAAO;gCAAE,UAAU;4BAAO;;8CAC/C,2BAAC,mBAAY;;;;;gCAAG;gCAAE,OAAO,YAAY;;;;;;;;;;;;;QAI7C;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,QAAQ,uBACf,2BAAC,yBAAyB;oBACxB,QAAQ;oBACR,WAAW,OAAO,SAAS;;;;;;YAG/B,SAAS;gBACP;oBAAE,MAAM;oBAAO,OAAO,qBAAgB,CAAC,OAAO;gBAAC;gBAC/C;oBAAE,MAAM;oBAAO,OAAO,qBAAgB,CAAC,QAAQ;gBAAC;gBAChD;oBAAE,MAAM;oBAAO,OAAO,qBAAgB,CAAC,QAAQ;gBAAC;gBAChD;oBAAE,MAAM;oBAAO,OAAO,qBAAgB,CAAC,OAAO;gBAAC;gBAC/C;oBAAE,MAAM;oBAAO,OAAO,qBAAgB,CAAC,SAAS;gBAAC;aAClD;YACD,UAAU,CAAC,OAAO,SAAW,OAAO,MAAM,KAAK;QACjD;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,qBACP,2BAAC,aAAO;oBAAC,OAAO,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;8BAChC,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;;;;;;YAGxB,QAAQ,CAAC,GAAG,IAAM,IAAA,cAAK,EAAC,EAAE,SAAS,EAAE,IAAI,KAAK,IAAA,cAAK,EAAC,EAAE,SAAS,EAAE,IAAI;QACvE;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,MAAM;gBACb,MAAM,YAAY,OAAO,SAAS;gBAClC,qBACE,2BAAC,aAAO;oBAAC,OAAO,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;8BACjC,cAAA,2BAAC;wBAAK,MAAM,YAAY,WAAW;kCAChC,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;;;;;;;;;;;YAI5B;QACF;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG;gBACV,IAAI,OAAO,MAAM,KAAK,qBAAgB,CAAC,OAAO,IAAI,CAAC,OAAO,SAAS,EACjE,qBACE,2BAAC,gBAAU;oBACT,OAAM;oBACN,WAAW,IAAM,uBAAuB,OAAO,EAAE;oBACjD,QAAO;oBACP,YAAW;8BAEX,cAAA,2BAAC,YAAM;wBAAC,MAAK;wBAAQ,MAAM;wBAAC,oBAAM,2BAAC,qBAAc;;;;;kCAAK;;;;;;;;;;;gBAM5D,qBAAO,2BAAC;oBAAK,MAAK;8BAAY;;;;;;YAChC;QACF;KACD;IAED,SAAS;IACT,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAA;QAC7C,MAAM,gBAAgB,CAAC,wBACrB,WAAW,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,qBAAqB,WAAW,OAC9E,WAAW,WAAW,IAAI,WAAW,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,qBAAqB,WAAW;QAE3G,MAAM,gBAAgB,CAAC,gBAAgB,WAAW,MAAM,KAAK;QAE7D,OAAO,iBAAiB;IAC1B;IAEA,QAAQ;IACR,MAAM,WAAW;QACf;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,mBAAY;;;;;oBAAG;oBACR,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM;oBAAC;;;;;;;YAGlC,wBACE,2BAAC;;kCAEC,2BAAC,UAAI;wBAAC,OAAO;4BAAE,cAAc;wBAAG;kCAC9B,cAAA,2BAAC,WAAK;4BAAC,OAAO;gCAAE,OAAO;gCAAQ,gBAAgB;4BAAgB;;8CAC7D,2BAAC,WAAK;;sDACJ,2BAAC,WAAK;4CACJ,aAAY;4CACZ,sBAAQ,2BAAC,qBAAc;;;;;4CACvB,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,OAAO;gDAAE,OAAO;4CAAI;;;;;;wCAErB,gBAAgB,MAAM,GAAG,mBACxB,2BAAC,gBAAU;4CACT,OAAO,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC,MAAM,CAAC;4CACjD,WAAW;4CACX,QAAO;4CACP,YAAW;sDAEX,cAAA,2BAAC,YAAM;gDAAC,MAAM;gDAAC,oBAAM,2BAAC,qBAAc;;;;;;oDAAK;oDAChC,gBAAgB,MAAM;oDAAC;;;;;;;;;;;;;;;;;;8CAKtC,2BAAC,YAAM;oCACL,MAAK;oCACL,oBAAM,2BAAC,sBAAe;;;;;oCACtB,SAAS,IAAM,sBAAsB;8CACtC;;;;;;;;;;;;;;;;;kCAOL,2BAAC,UAAI;kCACH,cAAA,2BAAC,WAAK;4BACJ,SAAS;4BACT,YAAY;4BACZ,QAAO;4BACP,SAAS;4BACT,cAAc;4BACd,YAAY;gCACV,iBAAiB;gCACjB,iBAAiB;gCACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;gCACtC,UAAU;4BACZ;;;;;;;;;;;;;;;;;QAKV;QACA;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,mBAAY;;;;;oBAAG;oBACR,CAAA,eAAe,EAAE,AAAD,EAAG,MAAM;oBAAC;;;;;;;YAGtC,wBACE,2BAAC;;kCAEC,2BAAC,UAAI;wBAAC,OAAO;4BAAE,cAAc;wBAAG;kCAC9B,cAAA,2BAAC,WAAK;4BAAC,OAAO;gCAAE,OAAO;gCAAQ,gBAAgB;4BAAgB;;8CAC7D,2BAAC,WAAK;;sDACJ,2BAAC,WAAK;4CACJ,aAAY;4CACZ,sBAAQ,2BAAC,qBAAc;;;;;4CACvB,OAAO;4CACP,UAAU,CAAC,IAAM,wBAAwB,EAAE,MAAM,CAAC,KAAK;4CACvD,OAAO;gDAAE,OAAO;4CAAI;;;;;;sDAEtB,2BAAC,YAAM;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU;4CACV,OAAO;gDAAE,OAAO;4CAAI;4CACpB,UAAU;;8DAEV,2BAAC,YAAM,CAAC,MAAM;oDAAC,OAAO,qBAAgB,CAAC,OAAO;8DAAE;;;;;;8DAChD,2BAAC,YAAM,CAAC,MAAM;oDAAC,OAAO,qBAAgB,CAAC,QAAQ;8DAAE;;;;;;8DACjD,2BAAC,YAAM,CAAC,MAAM;oDAAC,OAAO,qBAAgB,CAAC,QAAQ;8DAAE;;;;;;8DACjD,2BAAC,YAAM,CAAC,MAAM;oDAAC,OAAO,qBAAgB,CAAC,OAAO;8DAAE;;;;;;8DAChD,2BAAC,YAAM,CAAC,MAAM;oDAAC,OAAO,qBAAgB,CAAC,SAAS;8DAAE;;;;;;;;;;;;;;;;;;8CAGtD,2BAAC,YAAM;oCACL,oBAAM,2BAAC,qBAAc;;;;;oCACrB,SAAS;oCACT,SAAS;8CACV;;;;;;;;;;;;;;;;;kCAOL,2BAAC,UAAI;kCACH,cAAA,2BAAC,WAAK;4BACJ,SAAS;4BACT,YAAY;4BACZ,QAAO;4BACP,SAAS;4BACT,YAAY;gCACV,iBAAiB;gCACjB,iBAAiB;gCACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,MAAM,CAAC;gCACxC,UAAU;4BACZ;;;;;;;;;;;;;;;;;QAKV;KACD;IAED,qBACE,2BAAC;;0BACC,2BAAC,UAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,MAAK;;;;;;0BAMP,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU;oBACR,sBAAsB;oBACtB,WAAW,WAAW;gBACxB;gBACA,QAAQ;gBACR,OAAO;0BAEP,cAAA,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;sCAEV,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BACtC;4BACD,OAAM;sCAEN,cAAA,2BAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;sCAGhB,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAM;sCAEN,cAAA,2BAAC;gCACC,MAAM;gCACN,aAAY;gCACZ,WAAW;gCACX,SAAS;;;;;;;;;;;sCAGb,2BAAC,UAAI,CAAC,IAAI;sCACR,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCAAC,MAAK;wCAAU,UAAS;wCAAS,oBAAM,2BAAC,mBAAY;;;;;kDAAK;;;;;;kDAGjE,2BAAC,YAAM;wCAAC,SAAS,IAAM,sBAAsB;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE;GAziBM;;QASiB,UAAI,CAAC;;;KATtB;IA2iBN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzmBf;;;;;;;;;;;;;CAaC;;;;4BA6ZD;;;eAAA;;;;;;;wEA3Z2C;sCACb;6BAgBvB;8BAWA;4BACiB;sFAGS;6BAGL;;;;;;;;;;AAG5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,gBAAU;AAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;AAE1B,MAAM,qBAA+B;;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAA4B;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;IAE3C,WAAW;IACX,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAE3B,WAAW;IACX,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,eAAQ,EAAC;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IAEzC,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,MAAM,iBAAW,CAAC,oBAAoB;YACrD,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,6BAA6B;YAC7B,YAAO,CAAC,IAAI,CAAC;QACf,SAAU;YACR,WAAW;QACb;IACF;IAEA,SAAS;IACT,MAAM,iBAAiB;QACrB,IAAI,CAAC,YAAY;QACjB,KAAK,cAAc,CAAC;YAClB,MAAM,WAAW,IAAI;YACrB,aAAa,WAAW,WAAW,IAAI;QACzC;QACA,oBAAoB;IACtB;IAEA,SAAS;IACT,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,YAAY;YACZ,MAAM,iBAAW,CAAC,iBAAiB,CAAC;YACpC,aAAO,CAAC,OAAO,CAAC;YAChB,oBAAoB;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,YAAY;QACd;IACF;IAEA,OAAO;IACP,MAAM,mBAAmB;QACvB,IAAI,uBAAsB,uBAAA,iCAAA,WAAY,IAAI,GAAE;YAC1C,aAAO,CAAC,KAAK,CAAC;YACd;QACF;QAEA,IAAI;YACF,YAAY;YACZ,MAAM,iBAAW,CAAC,iBAAiB;YACnC,aAAO,CAAC,OAAO,CAAC;YAChB,sBAAsB;YACtB,iBAAiB;YACjB,YAAO,CAAC,IAAI,CAAC;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,YAAY;QACd;IACF;IAEA,uBAAuB;IACvB,MAAM,sBAAsB,CAAA,uBAAA,iCAAA,WAAY,SAAS,KAAI;IAErD,IAAI,SACF,qBACE,2BAAC,4BAAa;kBACZ,cAAA,2BAAC;YAAI,OAAO;gBAAE,WAAW;gBAAU,SAAS;YAAS;;8BACnD,2BAAC,UAAI;oBAAC,MAAK;;;;;;8BACX,2BAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAG;8BAC1B,cAAA,2BAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;;;;;;;;;;;;;IAOjC,IAAI,CAAC,YACH,qBACE,2BAAC,4BAAa;kBACZ,cAAA,2BAAC,UAAI;sBACH,cAAA,2BAAC;gBAAI,OAAO;oBAAE,WAAW;oBAAU,SAAS;gBAAS;;kCACnD,2BAAC,yBAAkB;wBAAC,OAAO;4BAAE,UAAU;4BAAI,OAAO;4BAAW,cAAc;wBAAG;;;;;;kCAC9E,2BAAC;wBAAM,OAAO;kCAAG;;;;;;kCACjB,2BAAC;wBAAK,MAAK;kCAAY;;;;;;kCACvB,2BAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAG;kCAC1B,cAAA,2BAAC,YAAM;4BAAC,MAAK;4BAAU,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;sCAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUpF,SAAS;IACT,IAAI,CAAC,qBACH,qBACE,2BAAC,4BAAa;kBACZ,cAAA,2BAAC,UAAI;sBACH,cAAA,2BAAC,WAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;gBACR,sBACE,2BAAC,YAAM;oBAAC,MAAK;oBAAQ,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;8BAAe;;;;;;;;;;;;;;;;;;;;;IAU5E,QAAQ;IACR,MAAM,WAAW;QACf;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,mBAAY;;;;;oBAAG;;;;;;;YAIpB,wBACE,2BAAC,6BAAoB;gBACnB,YAAY;gBACZ,WAAW;;;;;;QAGjB;KACD;IAED,QAAQ;IACR,MAAM,YAAY;QAChB;YACE,KAAK;YACL,OAAO;YACP,oBAAM,2BAAC,mBAAY;;;;;YACnB,SAAS;QACX;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,2BAAC,qBAAc;;;;;YACrB,QAAQ;YACR,SAAS,IAAM,sBAAsB;QACvC;KACD;IAED,qBACE,2BAAC,4BAAa;;0BAEZ,2BAAC,UAAI;gBACH,OAAO;oBAAE,cAAc;gBAAG;gBAC1B,OACE,qCACE,2BAAC,cAAQ;oBACP,MAAM;wBAAE,OAAO;oBAAU;oBACzB,WAAU;oBACV,SAAS;wBAAC;qBAAQ;8BAElB,cAAA,2BAAC,YAAM;wBACL,MAAK;wBACL,oBAAM,2BAAC,mBAAY;;;;;wBACnB,OAAO;4BAAE,UAAU;wBAAG;;;;;;;;;;;0BAM9B,cAAA,2BAAC,WAAK;oBAAC,MAAK;oBAAQ,OAAM;;sCACxB,2BAAC,YAAM;4BACL,MAAM;4BACN,oBAAM,2BAAC,mBAAY;;;;;4BACnB,OAAO;gCACL,iBAAiB;gCACjB,UAAU;4BACZ;;;;;;sCAEF,2BAAC;;8CACC,2BAAC;oCAAM,OAAO;oCAAG,OAAO;wCAAE,QAAQ;wCAAG,cAAc;oCAAE;8CAClD,WAAW,IAAI;;;;;;8CAElB,2BAAC,WAAK;;wCACH,WAAW,SAAS,kBACnB,2BAAC,SAAG;4CACF,oBAAM,2BAAC,oBAAa;;;;;4CACpB,OAAM;sDACP;;;;;;sDAIH,2BAAC;4CAAK,MAAK;sDACR,WAAW,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQrC,2BAAC,UAAI;0BACH,cAAA,2BAAC,UAAI;oBACH,WAAW;oBACX,UAAU;oBACV,OAAO;oBACP,MAAK;oBACL,aAAa;wBAAE,cAAc;oBAAG;;;;;;;;;;;0BAKpC,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU;oBACR,oBAAoB;oBACpB,KAAK,WAAW;gBAClB;gBACA,QAAQ;gBACR,OAAO;0BAEP,cAAA,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;sCAEV,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAU;gCACrC;oCAAE,KAAK;oCAAG,KAAK;oCAAI,SAAS;gCAAoB;6BACjD;sCAED,cAAA,2BAAC,WAAK;gCAAC,aAAY;;;;;;;;;;;sCAErB,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCACL;oCAAE,KAAK;oCAAK,SAAS;gCAAiB;6BACvC;sCAED,cAAA,2BAAC;gCACC,MAAM;gCACN,aAAY;gCACZ,SAAS;gCACT,WAAW;;;;;;;;;;;sCAGf,2BAAC,UAAI,CAAC,IAAI;4BAAC,OAAO;gCAAE,cAAc;gCAAG,WAAW;4BAAQ;sCACtD,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCAAC,SAAS;4CACf,oBAAoB;4CACpB,KAAK,WAAW;wCAClB;kDAAG;;;;;;kDAGH,2BAAC,YAAM;wCACL,MAAK;wCACL,UAAS;wCACT,SAAS;wCACT,oBAAM,2BAAC,mBAAY;;;;;kDACpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,2BAAC,WAAK;gBACJ,qBACE,2BAAC,WAAK;;sCACJ,2BAAC,gCAAyB;4BAAC,OAAO;gCAAE,OAAO;4BAAU;;;;;;sCACrD,2BAAC;4BAAK,MAAK;sCAAS;;;;;;;;;;;;gBAGxB,MAAM;gBACN,UAAU;oBACR,sBAAsB;oBACtB,qBAAqB;gBACvB;gBACA,QAAQ;gBACR,OAAO;;kCAEP,2BAAC,WAAK;wBACJ,SAAQ;wBACR,2BACE,2BAAC;;8CACC,2BAAC;8CAAE;;;;;;8CACH,2BAAC;;sDACC,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;;;;;;;;;;;;;wBAIV,MAAK;wBACL,QAAQ;wBACR,OAAO;4BAAE,cAAc;wBAAG;;;;;;kCAG5B,2BAAC;wBAAI,OAAO;4BAAE,cAAc;wBAAG;kCAC7B,cAAA,2BAAC;4BAAK,MAAM;;gCAAC;8CACF,2BAAC;oCAAK,IAAI;8CAAE,uBAAA,iCAAA,WAAY,IAAI;;;;;;gCAAQ;;;;;;;;;;;;kCAIjD,2BAAC,WAAK;wBACJ,aAAa,CAAC,IAAI,EAAE,uBAAA,iCAAA,WAAY,IAAI,CAAC,CAAC;wBACtC,OAAO;wBACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;wBACpD,OAAO;4BAAE,cAAc;wBAAG;;;;;;kCAG5B,2BAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAQ;kCAC/B,cAAA,2BAAC,WAAK;;8CACJ,2BAAC,YAAM;oCACL,SAAS;wCACP,sBAAsB;wCACtB,qBAAqB;oCACvB;8CACD;;;;;;8CAGD,2BAAC,YAAM;oCACL,MAAK;oCACL,MAAM;oCACN,SAAS;oCACT,UAAU,uBAAsB,uBAAA,iCAAA,WAAY,IAAI;oCAChD,SAAS;oCACT,oBAAM,2BAAC,qBAAc;;;;;8CACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAhXM;;QAQW,UAAI,CAAC;;;KARhB;IAkXN,WAAe"}