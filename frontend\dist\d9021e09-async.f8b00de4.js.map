{"version": 3, "sources": ["node_modules/@ant-design/icons-svg/es/asn/ArrowLeftOutlined.js", "node_modules/@ant-design/icons/es/icons/ArrowLeftOutlined.js", "node_modules/antd/es/statistic/Number.js", "node_modules/antd/es/statistic/style/index.js", "node_modules/antd/es/statistic/Statistic.js", "node_modules/antd/es/statistic/utils.js", "node_modules/antd/es/statistic/Timer.js", "node_modules/antd/es/statistic/Countdown.js", "node_modules/antd/es/statistic/index.js", "node_modules/@ant-design/icons-svg/es/asn/HarmonyOSOutlined.js", "node_modules/@ant-design/icons/es/icons/HarmonyOSOutlined.js", "src/utils/roleUtils.ts", "src/pages/team/detail/components/TeamMemberList.tsx", "src/pages/team/detail/components/TeamDetailContent.tsx", "src/pages/team/detail/index.tsx"], "sourcesContent": ["// This icon file is generated automatically.\nvar ArrowLeftOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z\" } }] }, \"name\": \"arrow-left\", \"theme\": \"outlined\" };\nexport default ArrowLeftOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ArrowLeftOutlinedSvg from \"@ant-design/icons-svg/es/asn/ArrowLeftOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ArrowLeftOutlined = function ArrowLeftOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ArrowLeftOutlinedSvg\n  }));\n};\n\n/**![arrow-left](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg3MiA0NzRIMjg2LjlsMzUwLjItMzA0YzUuNi00LjkgMi4yLTE0LTUuMi0xNGgtODguNWMtMy45IDAtNy42IDEuNC0xMC41IDMuOUwxNTUgNDg3LjhhMzEuOTYgMzEuOTYgMCAwMDAgNDguM0w1MzUuMSA4NjZjMS41IDEuMyAzLjMgMiA1LjIgMmg5MS41YzcuNCAwIDEwLjgtOS4yIDUuMi0xNEwyODYuOSA1NTBIODcyYzQuNCAwIDgtMy42IDgtOHYtNjBjMC00LjQtMy42LTgtOC04eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ArrowLeftOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ArrowLeftOutlined';\n}\nexport default RefIcon;", "\"use client\";\n\nimport * as React from 'react';\nconst StatisticNumber = props => {\n  const {\n    value,\n    formatter,\n    precision,\n    decimalSeparator,\n    groupSeparator = '',\n    prefixCls\n  } = props;\n  let valueNode;\n  if (typeof formatter === 'function') {\n    // Customize formatter\n    valueNode = formatter(value);\n  } else {\n    // Internal formatter\n    const val = String(value);\n    const cells = val.match(/^(-?)(\\d*)(\\.(\\d+))?$/);\n    // Process if illegal number\n    if (!cells || val === '-') {\n      valueNode = val;\n    } else {\n      const negative = cells[1];\n      let int = cells[2] || '0';\n      let decimal = cells[4] || '';\n      int = int.replace(/\\B(?=(\\d{3})+(?!\\d))/g, groupSeparator);\n      if (typeof precision === 'number') {\n        decimal = decimal.padEnd(precision, '0').slice(0, precision > 0 ? precision : 0);\n      }\n      if (decimal) {\n        decimal = `${decimalSeparator}${decimal}`;\n      }\n      valueNode = [/*#__PURE__*/React.createElement(\"span\", {\n        key: \"int\",\n        className: `${prefixCls}-content-value-int`\n      }, negative, int), decimal && (/*#__PURE__*/React.createElement(\"span\", {\n        key: \"decimal\",\n        className: `${prefixCls}-content-value-decimal`\n      }, decimal))];\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-content-value`\n  }, valueNode);\n};\nexport default StatisticNumber;", "import { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genStatisticStyle = token => {\n  const {\n    componentCls,\n    marginXXS,\n    padding,\n    colorTextDescription,\n    titleFontSize,\n    colorTextHeading,\n    contentFontSize,\n    fontFamily\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      [`${componentCls}-title`]: {\n        marginBottom: marginXXS,\n        color: colorTextDescription,\n        fontSize: titleFontSize\n      },\n      [`${componentCls}-skeleton`]: {\n        paddingTop: padding\n      },\n      [`${componentCls}-content`]: {\n        color: colorTextHeading,\n        fontSize: contentFontSize,\n        fontFamily,\n        [`${componentCls}-content-value`]: {\n          display: 'inline-block',\n          direction: 'ltr'\n        },\n        [`${componentCls}-content-prefix, ${componentCls}-content-suffix`]: {\n          display: 'inline-block'\n        },\n        [`${componentCls}-content-prefix`]: {\n          marginInlineEnd: marginXXS\n        },\n        [`${componentCls}-content-suffix`]: {\n          marginInlineStart: marginXXS\n        }\n      }\n    })\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => {\n  const {\n    fontSizeHeading3,\n    fontSize\n  } = token;\n  return {\n    titleFontSize: fontSize,\n    contentFontSize: fontSizeHeading3\n  };\n};\nexport default genStyleHooks('Statistic', token => {\n  const statisticToken = mergeToken(token, {});\n  return [genStatisticStyle(statisticToken)];\n}, prepareComponentToken);", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { useComponentConfig } from '../config-provider/context';\nimport Skeleton from '../skeleton';\nimport StatisticNumber from './Number';\nimport useStyle from './style';\nconst Statistic = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      rootClassName,\n      style,\n      valueStyle,\n      value = 0,\n      title,\n      valueRender,\n      prefix,\n      suffix,\n      loading = false,\n      /* --- FormatConfig starts --- */\n      formatter,\n      precision,\n      decimalSeparator = '.',\n      groupSeparator = ',',\n      /* --- FormatConfig starts --- */\n      onMouseEnter,\n      onMouseLeave\n    } = props,\n    rest = __rest(props, [\"prefixCls\", \"className\", \"rootClassName\", \"style\", \"valueStyle\", \"value\", \"title\", \"valueRender\", \"prefix\", \"suffix\", \"loading\", \"formatter\", \"precision\", \"decimalSeparator\", \"groupSeparator\", \"onMouseEnter\", \"onMouseLeave\"]);\n  const {\n    getPrefixCls,\n    direction,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('statistic');\n  const prefixCls = getPrefixCls('statistic', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const valueNode = /*#__PURE__*/React.createElement(StatisticNumber, {\n    decimalSeparator: decimalSeparator,\n    groupSeparator: groupSeparator,\n    prefixCls: prefixCls,\n    formatter: formatter,\n    precision: precision,\n    value: value\n  });\n  const cls = classNames(prefixCls, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, contextClassName, className, rootClassName, hashId, cssVarCls);\n  const internalRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => ({\n    nativeElement: internalRef.current\n  }));\n  const restProps = pickAttrs(rest, {\n    aria: true,\n    data: true\n  });\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", Object.assign({}, restProps, {\n    ref: internalRef,\n    className: cls,\n    style: Object.assign(Object.assign({}, contextStyle), style),\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave\n  }), title && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-title`\n  }, title), /*#__PURE__*/React.createElement(Skeleton, {\n    paragraph: false,\n    loading: loading,\n    className: `${prefixCls}-skeleton`\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: valueStyle,\n    className: `${prefixCls}-content`\n  }, prefix && /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-content-prefix`\n  }, prefix), valueRender ? valueRender(valueNode) : valueNode, suffix && /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-content-suffix`\n  }, suffix)))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Statistic.displayName = 'Statistic';\n}\nexport default Statistic;", "// Countdown\nconst timeUnits = [['Y', 1000 * 60 * 60 * 24 * 365],\n// years\n['M', 1000 * 60 * 60 * 24 * 30],\n// months\n['D', 1000 * 60 * 60 * 24],\n// days\n['H', 1000 * 60 * 60],\n// hours\n['m', 1000 * 60],\n// minutes\n['s', 1000],\n// seconds\n['S', 1] // million seconds\n];\nexport function formatTimeStr(duration, format) {\n  let leftDuration = duration;\n  const escapeRegex = /\\[[^\\]]*]/g;\n  const keepList = (format.match(escapeRegex) || []).map(str => str.slice(1, -1));\n  const templateText = format.replace(escapeRegex, '[]');\n  const replacedText = timeUnits.reduce((current, [name, unit]) => {\n    if (current.includes(name)) {\n      const value = Math.floor(leftDuration / unit);\n      leftDuration -= value * unit;\n      return current.replace(new RegExp(`${name}+`, 'g'), match => {\n        const len = match.length;\n        return value.toString().padStart(len, '0');\n      });\n    }\n    return current;\n  }, templateText);\n  let index = 0;\n  return replacedText.replace(escapeRegex, () => {\n    const match = keepList[index];\n    index += 1;\n    return match;\n  });\n}\nexport function formatCounter(value, config, down) {\n  const {\n    format = ''\n  } = config;\n  const target = new Date(value).getTime();\n  const current = Date.now();\n  const diff = down ? Math.max(target - current, 0) : Math.max(current - target, 0);\n  return formatTimeStr(diff, format);\n}", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { useEvent } from 'rc-util';\nimport raf from \"rc-util/es/raf\";\nimport { cloneElement } from '../_util/reactNode';\nimport Statistic from './Statistic';\nimport { formatCounter } from './utils';\nfunction getTime(value) {\n  return new Date(value).getTime();\n}\nconst StatisticTimer = props => {\n  const {\n      value,\n      format = 'HH:mm:ss',\n      onChange,\n      onFinish,\n      type\n    } = props,\n    rest = __rest(props, [\"value\", \"format\", \"onChange\", \"onFinish\", \"type\"]);\n  const down = type === 'countdown';\n  // We reuse state here to do same as `forceUpdate`\n  const [showTime, setShowTime] = React.useState(null);\n  // ======================== Update ========================\n  const update = useEvent(() => {\n    const now = Date.now();\n    const timestamp = getTime(value);\n    setShowTime({});\n    const timeDiff = !down ? now - timestamp : timestamp - now;\n    onChange === null || onChange === void 0 ? void 0 : onChange(timeDiff);\n    // Only countdown will trigger `onFinish`\n    if (down && timestamp < now) {\n      onFinish === null || onFinish === void 0 ? void 0 : onFinish();\n      return false;\n    }\n    return true;\n  });\n  // Effect trigger\n  React.useEffect(() => {\n    let rafId;\n    const clear = () => raf.cancel(rafId);\n    const rafUpdate = () => {\n      rafId = raf(() => {\n        if (update()) {\n          rafUpdate();\n        }\n      });\n    };\n    rafUpdate();\n    return clear;\n  }, [value, down]);\n  React.useEffect(() => {\n    setShowTime({});\n  }, []);\n  // ======================== Format ========================\n  const formatter = (formatValue, config) => showTime ? formatCounter(formatValue, Object.assign(Object.assign({}, config), {\n    format\n  }), down) : '-';\n  const valueRender = node => cloneElement(node, {\n    title: undefined\n  });\n  // ======================== Render ========================\n  return /*#__PURE__*/React.createElement(Statistic, Object.assign({}, rest, {\n    value: value,\n    valueRender: valueRender,\n    formatter: formatter\n  }));\n};\nexport default StatisticTimer;", "\"use client\";\n\nimport * as React from 'react';\nimport { devUseWarning } from '../_util/warning';\nimport StatisticTimer from './Timer';\nconst Countdown = props => {\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Countdown');\n    warning.deprecated(false, '<Statistic.Countdown />', '<Statistic.Timer type=\"countdown\" />');\n  }\n  return /*#__PURE__*/React.createElement(StatisticTimer, Object.assign({}, props, {\n    type: \"countdown\"\n  }));\n};\nexport default /*#__PURE__*/React.memo(Countdown);", "\"use client\";\n\nimport Countdown from './Countdown';\nimport Statistic from './Statistic';\nimport Timer from './Timer';\nStatistic.Timer = Timer;\nStatistic.Countdown = Countdown;\nexport default Statistic;", "// This icon file is generated automatically.\nvar HarmonyOSOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"fill-rule\": \"evenodd\", \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M511.5 65C719.99 65 889 234.01 889 442.5S719.99 820 511.5 820 134 650.99 134 442.5 303.01 65 511.5 65m0 64C338.36 129 198 269.36 198 442.5S338.36 756 511.5 756 825 615.64 825 442.5 684.64 129 511.5 129M745 889v72H278v-72z\" } }] }, \"name\": \"harmony-o-s\", \"theme\": \"outlined\" };\nexport default HarmonyOSOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport HarmonyOSOutlinedSvg from \"@ant-design/icons-svg/es/asn/HarmonyOSOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar HarmonyOSOutlined = function HarmonyOSOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: HarmonyOSOutlinedSvg\n  }));\n};\n\n/**![harmony-o-s](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNTExLjUgNjVDNzE5Ljk5IDY1IDg4OSAyMzQuMDEgODg5IDQ0Mi41UzcxOS45OSA4MjAgNTExLjUgODIwIDEzNCA2NTAuOTkgMTM0IDQ0Mi41IDMwMy4wMSA2NSA1MTEuNSA2NW0wIDY0QzMzOC4zNiAxMjkgMTk4IDI2OS4zNiAxOTggNDQyLjVTMzM4LjM2IDc1NiA1MTEuNSA3NTYgODI1IDYxNS42NCA4MjUgNDQyLjUgNjg0LjY0IDEyOSA1MTEuNSAxMjlNNzQ1IDg4OXY3MkgyNzh2LTcyeiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(HarmonyOSOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'HarmonyOSOutlined';\n}\nexport default RefIcon;", "import { TeamRole, TeamMemberResponse, TeamDetailResponse, TeamInfo } from '@/types/api';\n\n/**\n * 角色工具函数\n * \n * 提供统一的角色判断和处理逻辑，确保向后兼容性\n */\n\n/**\n * 判断用户是否为团队创建者\n * 优先使用 role 字段，如果没有则回退到 isCreator 字段\n * \n * @param user 用户对象（可以是 TeamMemberResponse、TeamDetailResponse 或 TeamInfo）\n * @returns 是否为团队创建者\n */\nexport function isTeamCreator(user: { role?: TeamRole; isCreator: boolean }): boolean {\n  if (user.role !== undefined) {\n    return user.role === TeamRole.TEAM_CREATOR;\n  }\n  return user.isCreator;\n}\n\n/**\n * 判断用户是否为团队成员\n * 优先使用 role 字段，如果没有则回退到 isCreator 字段\n * \n * @param user 用户对象\n * @returns 是否为团队成员\n */\nexport function isTeamMember(user: { role?: TeamRole; isCreator: boolean }): boolean {\n  if (user.role !== undefined) {\n    return user.role === TeamRole.TEAM_MEMBER;\n  }\n  return !user.isCreator;\n}\n\n/**\n * 获取用户角色的显示名称\n * \n * @param user 用户对象\n * @returns 角色显示名称\n */\nexport function getRoleDisplayName(user: { role?: TeamRole; isCreator: boolean }): string {\n  if (isTeamCreator(user)) {\n    return '团队创建者';\n  }\n  return '团队成员';\n}\n\n/**\n * 获取用户角色的标签颜色\n * \n * @param user 用户对象\n * @returns 标签颜色\n */\nexport function getRoleTagColor(user: { role?: TeamRole; isCreator: boolean }): string {\n  return isTeamCreator(user) ? 'gold' : 'blue';\n}\n\n/**\n * 获取用户的实际角色枚举值\n * 优先使用 role 字段，如果没有则根据 isCreator 推断\n * \n * @param user 用户对象\n * @returns 角色枚举值\n */\nexport function getUserRole(user: { role?: TeamRole; isCreator: boolean }): TeamRole {\n  if (user.role !== undefined) {\n    return user.role;\n  }\n  return user.isCreator ? TeamRole.TEAM_CREATOR : TeamRole.TEAM_MEMBER;\n}\n\n/**\n * 检查用户是否有管理团队的权限\n * \n * @param user 用户对象\n * @returns 是否有管理权限\n */\nexport function canManageTeam(user: { role?: TeamRole; isCreator: boolean }): boolean {\n  return isTeamCreator(user);\n}\n\n/**\n * 检查用户是否有管理成员的权限\n * \n * @param user 用户对象\n * @returns 是否有管理成员权限\n */\nexport function canManageMembers(user: { role?: TeamRole; isCreator: boolean }): boolean {\n  return isTeamCreator(user);\n}\n\n/**\n * 检查用户是否可以访问团队数据\n * \n * @param user 用户对象\n * @returns 是否可以访问数据\n */\nexport function canAccessData(user: { role?: TeamRole; isCreator: boolean }): boolean {\n  return true; // 所有团队成员都可以访问数据\n}\n\n/**\n * 角色权限级别映射\n */\nconst ROLE_LEVELS = {\n  [TeamRole.TEAM_CREATOR]: 100,\n  [TeamRole.TEAM_MEMBER]: 10,\n};\n\n/**\n * 检查用户是否有足够的权限级别\n * \n * @param user 用户对象\n * @param requiredRole 所需的最低角色\n * @returns 是否有足够权限\n */\nexport function hasPermissionLevel(\n  user: { role?: TeamRole; isCreator: boolean },\n  requiredRole: TeamRole\n): boolean {\n  const userRole = getUserRole(user);\n  return ROLE_LEVELS[userRole] >= ROLE_LEVELS[requiredRole];\n}\n\n/**\n * 批量过滤团队创建者\n * \n * @param users 用户列表\n * @returns 团队创建者列表\n */\nexport function filterTeamCreators<T extends { role?: TeamRole; isCreator: boolean }>(users: T[]): T[] {\n  return users.filter(isTeamCreator);\n}\n\n/**\n * 批量过滤团队成员\n * \n * @param users 用户列表\n * @returns 团队成员列表\n */\nexport function filterTeamMembers<T extends { role?: TeamRole; isCreator: boolean }>(users: T[]): T[] {\n  return users.filter(isTeamMember);\n}\n\n/**\n * 统计各角色数量\n * \n * @param users 用户列表\n * @returns 角色统计对象\n */\nexport function countRoles<T extends { role?: TeamRole; isCreator: boolean }>(users: T[]): {\n  creators: number;\n  members: number;\n  total: number;\n} {\n  const creators = filterTeamCreators(users).length;\n  const members = filterTeamMembers(users).length;\n  \n  return {\n    creators,\n    members,\n    total: users.length,\n  };\n}\n", "/**\n * 团队成员列表组件\n *\n * 功能特性：\n * - 展示团队所有成员信息（头像、姓名、邮箱、角色、状态等）\n * - 支持成员搜索和筛选功能\n * - 提供成员管理操作（移除成员、角色变更等）\n * - 区分创建者和普通成员的权限显示\n * - 响应式表格设计，适配不同屏幕尺寸\n *\n * 权限控制：\n * - 只有团队创建者可以看到管理操作按钮\n * - 创建者不能移除自己\n * - 普通成员只能查看成员列表\n *\n * 交互设计：\n * - 支持批量操作（预留功能）\n * - 提供详细的操作确认对话框\n * - 实时更新成员状态和数量\n */\n\nimport {\n  CheckCircleOutlined,\n  CrownOutlined,\n  DeleteOutlined,\n  ExclamationCircleOutlined,\n  FilterOutlined,\n  HarmonyOSOutlined,\n  MoreOutlined,\n  SearchOutlined,\n  StopOutlined,\n  UserOutlined,\n  UserSwitchOutlined,\n} from '@ant-design/icons';\nimport type { MenuProps } from 'antd';\nimport {\n  Avatar,\n  Badge,\n  Button,\n  Card,\n  Checkbox,\n  Divider,\n  Dropdown,\n  Input,\n  Modal,\n  message,\n  Select,\n  Space,\n  Table,\n  Tag,\n  Tooltip,\n  Typography,\n} from 'antd';\nimport type { ColumnsType } from 'antd/es/table';\nimport React, { useEffect, useState } from 'react';\nimport { TeamService } from '@/services';\nimport type { TeamMemberResponse } from '@/types/api';\nimport { isTeamCreator, getRoleDisplayName, getRoleTagColor } from '@/utils/roleUtils';\n\nconst { Text } = Typography;\nconst { Option } = Select;\n\n/**\n * 团队成员列表组件的Props接口\n */\ninterface TeamMemberListProps {\n  /** 团队ID，用于获取成员列表 */\n  teamId: number;\n  /** 当前用户是否为团队创建者，控制管理功能的显示 */\n  isCreator: boolean;\n  /** 成员变更时的回调函数，用于通知父组件刷新数据 */\n  onMemberChange?: () => void;\n}\n\nconst TeamMemberList: React.FC<TeamMemberListProps> = ({\n  teamId,\n  isCreator,\n  onMemberChange,\n}) => {\n  const [loading, setLoading] = useState(true);\n  const [members, setMembers] = useState<TeamMemberResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [filteredMembers, setFilteredMembers] = useState<TeamMemberResponse[]>(\n    [],\n  );\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [statusFilter, setStatusFilter] = useState<string>('all');\n\n  useEffect(() => {\n    fetchMembers();\n  }, [teamId]);\n\n  /**\n   * 成员列表过滤效果\n   *\n   * 过滤条件：\n   * 1. 搜索文本：匹配成员姓名或邮箱（不区分大小写）\n   * 2. 状态筛选：全部/活跃/非活跃/创建者/普通成员\n   *\n   * 安全性：\n   * - 添加空值检查，防止数据异常导致的错误\n   * - 确保成员对象的必要属性存在\n   */\n  useEffect(() => {\n    // 过滤成员列表 - 添加空值检查\n    if (!members || !Array.isArray(members)) {\n      setFilteredMembers([]);\n      return;\n    }\n\n    const filtered = members.filter((member) => {\n      // 确保member对象存在且有必要的属性\n      if (!member || !member.name || !member.email) {\n        return false;\n      }\n\n      // 搜索文本匹配（姓名或邮箱）\n      const matchesSearch =\n        !searchText ||\n        member.name.toLowerCase().includes(searchText.toLowerCase()) ||\n        member.email.toLowerCase().includes(searchText.toLowerCase());\n\n      // 状态筛选匹配\n      const matchesStatus =\n        statusFilter === 'all' ||\n        (statusFilter === 'active' && member.isActive) ||\n        (statusFilter === 'inactive' && !member.isActive) ||\n        (statusFilter === 'creator' && member.isCreator) ||\n        (statusFilter === 'member' && !member.isCreator);\n\n      return matchesSearch && matchesStatus;\n    });\n    setFilteredMembers(filtered);\n  }, [members, searchText, statusFilter]);\n\n  /**\n   * 获取团队成员列表\n   *\n   * 功能：\n   * - 调用API获取当前团队的所有成员\n   * - 设置加载状态，提供用户反馈\n   * - 处理错误情况，确保组件稳定性\n   *\n   * 数据处理：\n   * - 确保返回数据为数组格式，防止渲染错误\n   * - 错误时设置空数组，保持组件正常显示\n   */\n  const fetchMembers = async () => {\n    try {\n      setLoading(true);\n      const response = await TeamService.getTeamMembers({\n        current: 1,\n        pageSize: 1000,\n      });\n      // 确保返回的数据是数组格式，防止渲染错误\n      setMembers(response?.list || []);\n    } catch (error) {\n      console.error('获取团队成员失败:', error);\n      message.error('获取团队成员失败');\n      // 出错时设置为空数组，保持组件正常显示\n      setMembers([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRemoveMember = (member: TeamMemberResponse) => {\n    if (member.isCreator) {\n      message.warning('不能移除团队创建者');\n      return;\n    }\n\n    Modal.confirm({\n      title: '确认移除成员',\n      content: `确定要移除成员 \"${member.name}\" 吗？`,\n      okText: '确认',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await TeamService.removeMember(member.id);\n          message.success('成员移除成功');\n          fetchMembers();\n          onMemberChange?.();\n        } catch (error) {\n          console.error('移除成员失败:', error);\n        }\n      },\n    });\n  };\n\n  const handleBatchRemove = () => {\n    const selectedMembers = (members || []).filter(\n      (member) => selectedRowKeys.includes(member.id) && !member.isCreator,\n    );\n\n    if (selectedMembers.length === 0) {\n      message.warning('请选择要移除的成员');\n      return;\n    }\n\n    Modal.confirm({\n      title: '批量移除成员',\n      content: `确定要移除选中的 ${selectedMembers.length} 名成员吗？`,\n      okText: '确认',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await Promise.all(\n            selectedMembers.map((member) =>\n              TeamService.removeMember(member.id),\n            ),\n          );\n          message.success(`成功移除 ${selectedMembers.length} 名成员`);\n          setSelectedRowKeys([]);\n          fetchMembers();\n          onMemberChange?.();\n        } catch (error) {\n          console.error('批量移除成员失败:', error);\n          message.error('批量移除失败');\n        }\n      },\n    });\n  };\n\n  const columns: ColumnsType<TeamMemberResponse> = [\n    {\n      title: '成员',\n      dataIndex: 'name',\n      key: 'name',\n      render: (name, record) => (\n        <Space>\n          <Avatar size=\"small\" icon={<UserOutlined />} />\n          <div>\n            <div>{name}</div>\n            <div style={{ fontSize: 12, color: '#999' }}>{record.email}</div>\n          </div>\n        </Space>\n      ),\n    },\n    {\n      title: '角色',\n      key: 'role',\n      width: 100,\n      render: (_, record) => {\n        const isCreator = isTeamCreator(record);\n        return (\n          <Tag\n            color={getRoleTagColor(record)}\n            icon={isCreator ? <CrownOutlined /> : <UserOutlined />}\n          >\n            {getRoleDisplayName(record)}\n          </Tag>\n        );\n      },\n    },\n    {\n      title: '状态',\n      dataIndex: 'isActive',\n      key: 'status',\n      width: 80,\n      render: (isActive) => (\n        <Tag color={isActive ? 'green' : 'red'}>\n          {isActive ? '活跃' : '停用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '加入时间',\n      dataIndex: 'assignedAt',\n      key: 'assignedAt',\n      width: 150,\n      render: (assignedAt) => new Date(assignedAt).toLocaleDateString(),\n    },\n    {\n      title: '最后访问',\n      dataIndex: 'lastAccessTime',\n      key: 'lastAccessTime',\n      width: 150,\n      render: (lastAccessTime) => {\n        const date = new Date(lastAccessTime);\n        const now = new Date();\n        const diffDays = Math.floor(\n          (now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24),\n        );\n\n        let color = 'green';\n        if (diffDays > 7) color = 'orange';\n        if (diffDays > 30) color = 'red';\n\n        return (\n          <Tooltip title={date.toLocaleString()}>\n            <Tag color={color}>\n              {diffDays === 0 ? '今天' : `${diffDays}天前`}\n            </Tag>\n          </Tooltip>\n        );\n      },\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      render: (_, record) => {\n        if (!isCreator || record.isCreator) {\n          return <Text type=\"secondary\">-</Text>;\n        }\n\n        const menuItems: MenuProps['items'] = [\n          {\n            key: 'remove',\n            label: '移除成员',\n            icon: <DeleteOutlined />,\n            danger: true,\n            onClick: () => handleRemoveMember(record),\n          },\n        ];\n\n        return (\n          <Space size=\"small\">\n            <Button\n              type=\"text\"\n              danger\n              size=\"small\"\n              icon={<DeleteOutlined />}\n              onClick={() => handleRemoveMember(record)}\n            >\n              移除\n            </Button>\n            <Dropdown menu={{ items: menuItems }} trigger={['click']}>\n              <Button type=\"text\" size=\"small\" icon={<HarmonyOSOutlined />} />\n            </Dropdown>\n          </Space>\n        );\n      },\n    },\n  ];\n\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: (newSelectedRowKeys: React.Key[]) => {\n      setSelectedRowKeys(newSelectedRowKeys);\n    },\n    getCheckboxProps: (record: TeamMemberResponse) => ({\n      disabled: record.isCreator, // 创建者不能被选中\n    }),\n  };\n\n  return (\n    <Card\n      title={\n        <Space>\n          <Text strong>团队成员</Text>\n          <Badge count={filteredMembers.length} showZero />\n        </Space>\n      }\n      extra={\n        <Space>\n          <Select\n            value={statusFilter}\n            onChange={setStatusFilter}\n            style={{ width: 120 }}\n            size=\"small\"\n          >\n            <Option value=\"all\">全部</Option>\n            <Option value=\"active\">活跃</Option>\n            <Option value=\"inactive\">停用</Option>\n            <Option value=\"creator\">创建者</Option>\n            <Option value=\"member\">成员</Option>\n          </Select>\n          <Input\n            placeholder=\"搜索成员\"\n            prefix={<SearchOutlined />}\n            value={searchText}\n            onChange={(e) => setSearchText(e.target.value)}\n            style={{ width: 200 }}\n            size=\"small\"\n          />\n        </Space>\n      }\n    >\n      {selectedRowKeys.length > 0 && isCreator && (\n        <div\n          style={{\n            marginBottom: 16,\n            padding: 12,\n            background: '#f5f5f5',\n            borderRadius: 6,\n          }}\n        >\n          <Space>\n            <Text>已选择 {selectedRowKeys.length} 名成员</Text>\n            <Button\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={handleBatchRemove}\n            >\n              批量移除\n            </Button>\n            <Button size=\"small\" onClick={() => setSelectedRowKeys([])}>\n              取消选择\n            </Button>\n          </Space>\n        </div>\n      )}\n\n      <Table\n        columns={columns}\n        dataSource={filteredMembers}\n        rowKey=\"id\"\n        loading={loading}\n        rowSelection={isCreator ? rowSelection : undefined}\n        pagination={{\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total) => `共 ${total} 名成员`,\n          pageSize: 10,\n        }}\n      />\n    </Card>\n  );\n};\n\nexport default TeamMemberList;\n", "/**\n * 团队详情组件 - 增强模式显示\n */\n\nimport {\n  ArrowLeftOutlined,\n  CalendarOutlined,\n  ClockCircleOutlined,\n  CrownOutlined,\n  DeleteOutlined,\n  EditOutlined,\n  ExclamationCircleOutlined,\n  SettingOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { history, useModel } from '@umijs/max';\nimport {\n  Avatar,\n  Badge,\n  Button,\n  Card,\n  Col,\n  Dropdown,\n  Empty,\n  Form,\n  Input,\n  Modal,\n  message,\n  Progress,\n  Row,\n  Space,\n  Spin,\n  Statistic,\n  Tag,\n  Typography,\n} from 'antd';\nimport React, { useState } from 'react';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';\nimport TeamMemberList from './TeamMemberList';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\n\ninterface TeamDetailContentProps {\n  teamDetail: TeamDetailResponse | null;\n  loading: boolean;\n  onRefresh: () => void;\n  /** 是否显示返回按钮 */\n  showBackButton?: boolean;\n  /** 返回按钮点击回调 */\n  onBack?: () => void;\n}\n\nconst TeamDetailContent: React.FC<TeamDetailContentProps> = ({\n  teamDetail,\n  loading,\n  onRefresh,\n  showBackButton = false,\n  onBack,\n}) => {\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [updating, setUpdating] = useState(false);\n  const [deleting, setDeleting] = useState(false);\n  const [form] = Form.useForm();\n  const { setInitialState } = useModel('@@initialState');\n\n  // 辅助函数\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('zh-CN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n    });\n  };\n\n  const getTeamStatusColor = () => {\n    if (!teamDetail) return '#1890ff';\n    const memberCount = teamDetail.memberCount;\n    if (memberCount >= 10) return '#52c41a'; // 绿色 - 活跃\n    if (memberCount >= 5) return '#faad14'; // 橙色 - 正常\n    return '#1890ff'; // 蓝色 - 小团队\n  };\n\n  const getTeamStatusText = () => {\n    if (!teamDetail) return '小型团队';\n    const memberCount = teamDetail.memberCount;\n    if (memberCount >= 10) return '活跃团队';\n    if (memberCount >= 5) return '正常团队';\n    return '小型团队';\n  };\n\n  const handleGoBack = () => {\n    if (onBack) {\n      onBack();\n    } else {\n      history.push('/user/team-select');\n    }\n  };\n\n  /**\n   * 处理编辑团队信息操作\n   */\n  const handleEdit = () => {\n    if (!teamDetail) return;\n    form.setFieldsValue({\n      name: teamDetail.name,\n      description: teamDetail.description || '',\n    });\n    setEditModalVisible(true);\n  };\n\n  /**\n   * 处理团队信息更新操作\n   */\n  const handleUpdateTeam = async (values: UpdateTeamRequest) => {\n    if (!teamDetail) return;\n\n    try {\n      setUpdating(true);\n      await TeamService.updateCurrentTeam(values);\n      message.success('团队信息更新成功');\n      setEditModalVisible(false);\n      onRefresh();\n    } catch (error) {\n      console.error('更新团队失败:', error);\n      message.error('更新团队失败');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  /**\n   * 处理删除团队操作\n   */\n  const handleDeleteTeam = () => {\n    if (!teamDetail) return;\n\n    Modal.confirm({\n      title: '确认删除团队',\n      content: `确定要删除团队 \"${teamDetail.name}\" 吗？此操作不可恢复。`,\n      icon: <ExclamationCircleOutlined />,\n      okText: '确认删除',\n      cancelText: '取消',\n      okType: 'danger',\n      onOk: async () => {\n        try {\n          setDeleting(true);\n          await TeamService.deleteCurrentTeam();\n          message.success('团队删除成功');\n          // 更新全局状态，清除当前团队\n          setInitialState((s) => ({ ...s, currentTeam: undefined }));\n          history.push('/user/team-select');\n        } catch (error) {\n          console.error('删除团队失败:', error);\n          message.error('删除团队失败');\n        } finally {\n          setDeleting(false);\n        }\n      },\n    });\n  };\n\n  // 创建下拉菜单项（增强模式使用）\n  const createMenuItems = () => [\n    {\n      key: 'edit',\n      icon: <EditOutlined />,\n      label: '编辑团队',\n      onClick: handleEdit,\n    },\n    {\n      key: 'delete',\n      icon: <DeleteOutlined />,\n      label: '删除团队',\n      danger: true,\n      onClick: handleDeleteTeam,\n    },\n  ];\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px 0' }}>\n        <Spin size=\"large\" />\n      </div>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <Empty\n        image={Empty.PRESENTED_IMAGE_SIMPLE}\n        description=\"请先选择一个团队\"\n      />\n    );\n  }\n\n  // 增强模式渲染\n  return (\n    <div style={{ padding: '0 24px' }}>\n      {/* 团队头部信息卡片 */}\n      <Card\n        style={{\n          marginBottom: 24,\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          border: 'none',\n          borderRadius: 16,\n        }}\n        styles={{ body: { padding: '32px' } }}\n      >\n        <Row align=\"middle\" justify=\"space-between\">\n          {/* 左列：返回按钮 */}\n          {showBackButton && (\n            <Col>\n              <Button\n                type=\"text\"\n                icon={<ArrowLeftOutlined />}\n                onClick={handleGoBack}\n                style={{\n                  color: 'rgba(255, 255, 255, 0.8)',\n                  fontSize: 16,\n                  padding: '4px 8px',\n                }}\n              >\n                返回\n              </Button>\n            </Col>\n          )}\n\n          {/* 中间列：团队名称显示 */}\n          <Col\n            flex=\"auto\"\n            style={{\n              display: 'flex',\n              justifyContent: 'center',\n              maxWidth: '60%',\n            }}\n          >\n            <Space size=\"large\" align=\"center\">\n              <Avatar\n                size={65}\n                icon={<TeamOutlined />}\n                style={{\n                  backgroundColor: 'rgba(255, 255, 255, 0.2)',\n                  color: 'white',\n                  fontSize: 28,\n                }}\n              />\n              <div>\n                <Space align=\"center\" style={{ marginBottom: 8 }}>\n                  <Title level={2} style={{ color: 'white', margin: 0 }}>\n                    {teamDetail.name}\n                  </Title>\n                  {teamDetail.isCreator && (\n                    <Tag\n                      icon={<CrownOutlined />}\n                      color=\"gold\"\n                      style={{ fontSize: 12 }}\n                    >\n                      管理员\n                    </Tag>\n                  )}\n                  <Badge\n                    color={getTeamStatusColor()}\n                    text={\n                      <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>\n                        {getTeamStatusText()}\n                      </Text>\n                    }\n                  />\n                </Space>\n                <Paragraph\n                  style={{\n                    color: 'rgba(255, 255, 255, 0.8)',\n                    margin: 0,\n                    textAlign: 'center',\n                  }}\n                  ellipsis={{ rows: 2 }}\n                >\n                  {teamDetail.description || '这个团队还没有描述'}\n                </Paragraph>\n              </div>\n            </Space>\n          </Col>\n\n          {/* 右列：团队操作菜单 */}\n          <Col>\n            {teamDetail.isCreator && (\n              <Dropdown\n                menu={{ items: createMenuItems() }}\n                trigger={['click']}\n                placement=\"bottomRight\"\n              >\n                <Button\n                  type=\"text\"\n                  icon={<SettingOutlined />}\n                  style={{\n                    color: 'rgba(255, 255, 255, 0.8)',\n                    fontSize: 20,\n                    width: 50,\n                    height: 50,\n                  }}\n                />\n              </Dropdown>\n            )}\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 团队统计信息 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"团队成员\"\n              value={teamDetail.memberCount}\n              suffix=\"人\"\n              prefix={<UserOutlined style={{ color: '#1890ff' }} />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"创建时间\"\n              value={formatDate(teamDetail.createdAt)}\n              prefix={<CalendarOutlined style={{ color: '#52c41a' }} />}\n              valueStyle={{ color: '#52c41a', fontSize: 16 }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"最后活动\"\n              value={formatDate(teamDetail.updatedAt)}\n              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}\n              valueStyle={{ color: '#faad14', fontSize: 16 }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <div style={{ textAlign: 'center' }}>\n              <Text type=\"secondary\" style={{ fontSize: 14 }}>\n                团队活跃度\n              </Text>\n              <div style={{ marginTop: 8 }}>\n                <Progress\n                  type=\"circle\"\n                  size={60}\n                  percent={Math.min(teamDetail.memberCount * 10, 100)}\n                  strokeColor={getTeamStatusColor()}\n                  format={() => (\n                    <Text style={{ fontSize: 12, color: getTeamStatusColor() }}>\n                      {teamDetail.memberCount >= 10\n                        ? '高'\n                        : teamDetail.memberCount >= 5\n                          ? '中'\n                          : '低'}\n                    </Text>\n                  )}\n                />\n              </div>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 团队成员列表 */}\n      <TeamMemberList\n        teamId={teamDetail.id}\n        isCreator={teamDetail.isCreator}\n        onMemberChange={onRefresh}\n      />\n\n      {/* 编辑团队模态框 */}\n      <Modal\n        title=\"编辑团队信息\"\n        open={editModalVisible}\n        onCancel={() => setEditModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Form form={form} layout=\"vertical\" onFinish={handleUpdateTeam}>\n          <Form.Item\n            label=\"团队名称\"\n            name=\"name\"\n            rules={[\n              { required: true, message: '请输入团队名称' },\n              { max: 50, message: '团队名称不能超过50个字符' },\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" />\n          </Form.Item>\n          <Form.Item\n            label=\"团队描述\"\n            name=\"description\"\n            rules={[{ max: 200, message: '团队描述不能超过200个字符' }]}\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入团队描述（可选）\"\n              showCount\n              maxLength={200}\n            />\n          </Form.Item>\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => setEditModalVisible(false)}>取消</Button>\n              <Button type=\"primary\" htmlType=\"submit\" loading={updating}>\n                保存\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default TeamDetailContent;\n", "/**\n * 团队详情页面\n */\n\nimport { PageContainer } from '@ant-design/pro-components';\nimport { message, Spin, Typography } from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse } from '@/types/api';\nimport TeamDetailContent from './components/TeamDetailContent';\n\nconst { Text } = Typography;\n\nconst TeamDetailPage: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);\n\n  useEffect(() => {\n    fetchTeamDetail();\n  }, []);\n\n  const fetchTeamDetail = async () => {\n    try {\n      setLoading(true);\n      const detail = await TeamService.getCurrentTeamDetail();\n      setTeamDetail(detail);\n    } catch (error) {\n      console.error('获取团队详情失败:', error);\n      message.error('获取团队详情失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Spin size=\"large\" />\n        </div>\n      </PageContainer>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Text type=\"secondary\">团队信息加载失败</Text>\n        </div>\n      </PageContainer>\n    );\n  }\n\n  return (\n    <PageContainer style={{ background: '#f5f5f5' }}>\n      <TeamDetailContent\n        teamDetail={teamDetail}\n        loading={loading}\n        onRefresh={fetchTeamDetail}\n        showBackButton={true}\n      />\n    </PageContainer>\n  );\n};\n\nexport default TeamDetailPage;\n"], "names": [], "mappings": "6eACI,EAAoB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,OAAQ,EAAG,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,2NAA4N,CAAE,EAAE,AAAC,EAAG,KAAQ,aAAc,MAAS,UAAW,2BCcza,EAAuB,EAAM,UAAU,CARnB,SAA2B,CAAK,CAAE,CAAG,EAC3D,OAAoB,EAAM,aAAa,CAAC,SAAQ,CAAE,GAAA,SAAQ,EAAC,CAAC,EAAG,EAAO,CACpE,IAAK,EACL,KAAM,CACR,IACF,iwBCTA,IAAM,GAAkB,QASlB,EARJ,GAAM,CACJ,MAAA,CAAK,CACL,UAAA,CAAS,CACT,UAAA,CAAS,CACT,iBAAA,CAAgB,CAChB,eAAA,EAAiB,EAAE,CACnB,UAAA,CAAS,CACV,CAAG,EAEJ,GAAI,AAAqB,YAArB,OAAO,EAET,EAAY,EAAU,OACjB,CAEL,IAAM,EAAM,OAAO,GACb,EAAQ,EAAI,KAAK,CAAC,yBAExB,GAAI,AAAC,GAAS,AAAQ,MAAR,EAEP,CACL,IAAM,EAAW,CAAK,CAAC,EAAE,CACrB,EAAM,CAAK,CAAC,EAAE,EAAI,IAClB,EAAU,CAAK,CAAC,EAAE,EAAI,GAC1B,EAAM,EAAI,OAAO,CAAC,wBAAyB,GAClB,UAArB,OAAO,GACT,CAAA,EAAU,EAAQ,MAAM,CAAC,EAAW,KAAK,KAAK,CAAC,EAAG,EAAY,EAAI,EAAY,EAAC,EAE7E,GACF,CAAA,EAAU,CAAC,EAAE,EAAiB,EAAE,EAAQ,CAAC,AAAD,EAE1C,EAAY,CAAc,EAAM,aAAa,CAAC,OAAQ,CACpD,IAAK,MACL,UAAW,CAAC,EAAE,EAAU,kBAAkB,CAAC,AAC7C,EAAG,EAAU,GAAM,GAAyB,EAAM,aAAa,CAAC,OAAQ,CACtE,IAAK,UACL,UAAW,CAAC,EAAE,EAAU,sBAAsB,CAAC,AACjD,EAAG,GAAU,CACf,MAnBE,EAAY,EAoBhB,CACA,OAAoB,EAAM,aAAa,CAAC,OAAQ,CAC9C,UAAW,CAAC,EAAE,EAAU,cAAc,CAAC,AACzC,EAAG,GACL,yDC5CA,IAAM,GAAoB,IACxB,GAAM,CACJ,aAAA,CAAY,CACZ,UAAA,CAAS,CACT,QAAA,CAAO,CACP,qBAAA,CAAoB,CACpB,cAAA,CAAa,CACb,iBAAA,CAAgB,CAChB,gBAAA,CAAe,CACf,WAAA,CAAU,CACX,CAAG,EACJ,MAAO,CACL,CAAC,EAAa,CAAE,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,EAAG,GAAA,iBAAc,EAAC,IAAS,CACtE,CAAC,CAAC,EAAE,EAAa,MAAM,CAAC,CAAC,CAAE,CACzB,aAAc,EACd,MAAO,EACP,SAAU,CACZ,EACA,CAAC,CAAC,EAAE,EAAa,SAAS,CAAC,CAAC,CAAE,CAC5B,WAAY,CACd,EACA,CAAC,CAAC,EAAE,EAAa,QAAQ,CAAC,CAAC,CAAE,CAC3B,MAAO,EACP,SAAU,EACV,WAAA,EACA,CAAC,CAAC,EAAE,EAAa,cAAc,CAAC,CAAC,CAAE,CACjC,QAAS,eACT,UAAW,KACb,EACA,CAAC,CAAC,EAAE,EAAa,iBAAiB,EAAE,EAAa,eAAe,CAAC,CAAC,CAAE,CAClE,QAAS,cACX,EACA,CAAC,CAAC,EAAE,EAAa,eAAe,CAAC,CAAC,CAAE,CAClC,gBAAiB,CACnB,EACA,CAAC,CAAC,EAAE,EAAa,eAAe,CAAC,CAAC,CAAE,CAClC,kBAAmB,CACrB,CACF,CACF,EACF,EACF,MAYA,GAAe,GAAA,gBAAa,EAAC,YAAa,GAEjC,CAAC,GADe,GAAA,aAAU,EAAC,EAAO,CAAC,IACA,CAZP,IACnC,GAAM,CACJ,iBAAA,CAAgB,CAChB,SAAA,CAAQ,CACT,CAAG,EACJ,MAAO,CACL,cAAe,EACf,gBAAiB,CACnB,EACF,GCpDI,GAAS,IAAI,EAAI,IAAI,CAAC,MAAM,EAAI,SAAU,CAAC,CAAE,CAAC,EAChD,IAAI,EAAI,CAAC,EACT,IAAK,IAAI,KAAK,EAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAG,IAAM,AAAe,EAAf,EAAE,OAAO,CAAC,IAAQ,CAAA,CAAC,CAAC,EAAE,CAAG,CAAC,CAAC,EAAE,AAAD,EAC/F,GAAI,AAAK,MAAL,GAAa,AAAwC,YAAxC,OAAO,OAAO,qBAAqB,CAAiB,IAAK,IAAI,EAAI,EAAG,EAAI,OAAO,qBAAqB,CAAC,GAAI,EAAI,EAAE,MAAM,CAAE,IAChH,EAAlB,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAS,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAG,CAAC,CAAC,EAAE,GAAG,CAAA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,AAAD,EAElG,OAAO,EACT,EAQA,IAAM,GAAyB,EAAM,UAAU,CAAC,CAAC,EAAO,KACtD,GAAM,CACF,UAAW,CAAkB,CAC7B,UAAA,CAAS,CACT,cAAA,CAAa,CACb,MAAA,CAAK,CACL,WAAA,CAAU,CACV,MAAA,EAAQ,CAAC,CACT,MAAA,CAAK,CACL,YAAA,CAAW,CACX,OAAA,CAAM,CACN,OAAA,CAAM,CACN,QAAA,EAAU,CAAA,CAAK,CAEf,UAAA,CAAS,CACT,UAAA,CAAS,CACT,iBAAA,EAAmB,GAAG,CACtB,eAAA,EAAiB,GAAG,CAEpB,aAAA,CAAY,CACZ,aAAA,CAAY,CACb,CAAG,EACJ,EAAO,GAAO,EAAO,CAAC,YAAa,YAAa,gBAAiB,QAAS,aAAc,QAAS,QAAS,cAAe,SAAU,SAAU,UAAW,YAAa,YAAa,mBAAoB,iBAAkB,eAAgB,eAAe,EACnP,CACJ,aAAA,CAAY,CACZ,UAAA,CAAS,CACT,UAAW,CAAgB,CAC3B,MAAO,CAAY,CACpB,CAAG,GAAA,qBAAkB,EAAC,aACjB,EAAY,EAAa,YAAa,GACtC,CAAC,EAAY,EAAQ,EAAU,CAAG,GAAS,GAC3C,EAAyB,EAAM,aAAa,CAAC,GAAiB,CAClE,iBAAkB,EAClB,eAAgB,EAChB,UAAW,EACX,UAAW,EACX,UAAW,EACX,MAAO,CACT,GACM,EAAM,GAAA,UAAU,EAAC,EAAW,CAChC,CAAC,CAAC,EAAE,EAAU,IAAI,CAAC,CAAC,CAAE,AAAc,QAAd,CACxB,EAAG,EAAkB,EAAW,EAAe,EAAQ,GACjD,EAAc,EAAM,MAAM,CAAC,MACjC,EAAM,mBAAmB,CAAC,EAAK,IAAO,CAAA,CACpC,cAAe,EAAY,OAAO,AACpC,CAAA,GACA,IAAM,EAAY,GAAA,UAAS,EAAC,EAAM,CAChC,KAAM,CAAA,EACN,KAAM,CAAA,CACR,GACA,OAAO,EAAwB,EAAM,aAAa,CAAC,MAAO,OAAO,MAAM,CAAC,CAAC,EAAG,EAAW,CACrF,IAAK,EACL,UAAW,EACX,MAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,EAAG,GAAe,GACtD,aAAc,EACd,aAAc,CAChB,GAAI,GAAsB,EAAM,aAAa,CAAC,MAAO,CACnD,UAAW,CAAC,EAAE,EAAU,MAAM,CAAC,AACjC,EAAG,GAAqB,EAAM,aAAa,CAAC,UAAQ,CAAE,CACpD,UAAW,CAAA,EACX,QAAS,EACT,UAAW,CAAC,EAAE,EAAU,SAAS,CAAC,AACpC,EAAgB,EAAM,aAAa,CAAC,MAAO,CACzC,MAAO,EACP,UAAW,CAAC,EAAE,EAAU,QAAQ,CAAC,AACnC,EAAG,GAAuB,EAAM,aAAa,CAAC,OAAQ,CACpD,UAAW,CAAC,EAAE,EAAU,eAAe,CAAC,AAC1C,EAAG,GAAS,EAAc,EAAY,GAAa,EAAW,GAAuB,EAAM,aAAa,CAAC,OAAQ,CAC/G,UAAW,CAAC,EAAE,EAAU,eAAe,CAAC,AAC1C,EAAG,OACL,GCtFM,GAAY,CAAC,CAAC,IAAK,QAA0B,CAEnD,CAAC,IAAK,OAAyB,CAE/B,CAAC,IAAK,MAAoB,CAE1B,CAAC,IAAK,KAAe,CAErB,CAAC,IAAK,IAAU,CAEhB,CAAC,IAAK,IAAK,CAEX,CAAC,IAAK,EAAE,CACP,CCZD,IAAI,GAAS,IAAI,EAAI,IAAI,CAAC,MAAM,EAAI,SAAU,CAAC,CAAE,CAAC,EAChD,IAAI,EAAI,CAAC,EACT,IAAK,IAAI,KAAK,EAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAG,IAAM,AAAe,EAAf,EAAE,OAAO,CAAC,IAAQ,CAAA,CAAC,CAAC,EAAE,CAAG,CAAC,CAAC,EAAE,AAAD,EAC/F,GAAI,AAAK,MAAL,GAAa,AAAwC,YAAxC,OAAO,OAAO,qBAAqB,CAAiB,IAAK,IAAI,EAAI,EAAG,EAAI,OAAO,qBAAqB,CAAC,GAAI,EAAI,EAAE,MAAM,CAAE,IAChH,EAAlB,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAS,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAG,CAAC,CAAC,EAAE,GAAG,CAAA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,AAAD,EAElG,OAAO,EACT,EAUA,IAAM,GAAiB,IACrB,GAAM,CACF,MAAA,CAAK,CACL,OAAA,EAAS,UAAU,CACnB,SAAA,CAAQ,CACR,SAAA,CAAQ,CACR,KAAA,CAAI,CACL,CAAG,EACJ,EAAO,GAAO,EAAO,CAAC,QAAS,SAAU,WAAY,WAAY,OAAO,EACpE,EAAO,AAAS,cAAT,EAEP,CAAC,EAAU,EAAY,CAAG,EAAM,QAAQ,CAAC,MAEzC,EAAS,GAAA,WAAQ,EAAC,KACtB,IAAM,EAAM,KAAK,GAAG,GACd,EAjBD,IAAI,KAiBiB,GAjBL,OAAO,UAkB5B,EAAY,CAAC,GAEb,MAAA,GAAoD,EADnC,AAAC,EAAyB,EAAY,EAA9B,EAAM,IAG3B,IAAQ,CAAA,EAAY,CAAE,IACxB,MAAA,GAAoD,IAC7C,CAAA,GAGX,UAEA,EAAM,SAAS,CAAC,SACV,EAEJ,IAAM,EAAY,KAChB,EAAQ,GAAA,UAAG,EAAC,KACN,KACF,IAEJ,GACF,EAEA,OADA,IARc,IAAM,UAAG,CAAC,MAAM,CAAC,GAUjC,EAAG,CAAC,EAAO,EAAK,EAChB,EAAM,SAAS,CAAC,KACd,EAAY,CAAC,GACf,EAAG,EAAE,EASe,EAAM,aAAa,CAAC,GAAW,OAAO,MAAM,CAAC,CAAC,EAAG,EAAM,CACzE,MAAO,EACP,YANkB,GAAQ,GAAA,eAAY,EAAC,EAAM,CAC7C,MAAO,KAAA,CACT,GAKE,UAVgB,CAAC,EAAa,IAAW,EAAW,ADzBjD,SAAuB,CAAK,CAAE,CAAM,CAAE,CAAI,EAC/C,GAAM,CACJ,OAAA,EAAS,EAAE,CACZ,CAAG,EACE,EAAS,IAAI,KAAK,GAAO,OAAO,GAChC,EAAU,KAAK,GAAG,GAExB,OAAO,AA9BF,SAAuB,CAAQ,CAAE,CAAM,EAC5C,IAAI,EAAe,EACb,EAAc,aACd,EAAW,AAAC,CAAA,EAAO,KAAK,CAAC,IAAgB,EAAE,AAAD,EAAG,GAAG,CAAC,GAAO,EAAI,KAAK,CAAC,EAAG,KACrE,EAAe,EAAO,OAAO,CAAC,EAAa,MAC3C,EAAe,GAAU,MAAM,CAAC,CAAC,EAAS,CAAC,EAAM,EAAK,IAC1D,GAAI,EAAQ,QAAQ,CAAC,GAAO,CAC1B,IAAM,EAAQ,KAAK,KAAK,CAAC,EAAe,GAExC,OADA,GAAgB,EAAQ,EACjB,EAAQ,OAAO,CAAC,AAAI,OAAO,CAAC,EAAE,EAAK,CAAC,CAAC,CAAE,KAAM,IAClD,IAAM,EAAM,EAAM,MAAM,CACxB,OAAO,EAAM,QAAQ,GAAG,QAAQ,CAAC,EAAK,KACxC,GACF,CACA,OAAO,EACT,EAAG,GACC,EAAQ,EACZ,OAAO,EAAa,OAAO,CAAC,EAAa,KACvC,IAAM,EAAQ,CAAQ,CAAC,EAAM,CAE7B,OADA,GAAS,EACF,EACT,GACF,EAOe,EAAO,KAAK,GAAG,CAAC,EAAS,EAAS,GAAK,KAAK,GAAG,CAAC,EAAU,EAAQ,GACpD,GAC7B,ECiBsE,EAAa,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,EAAG,GAAS,CACxH,OAAA,CACF,GAAI,GAAQ,GASZ,IACF,MC7DA,GAA4B,EAAM,IAAI,CATpB,GAKI,EAAM,aAAa,CAAC,GAAgB,OAAO,MAAM,CAAC,CAAC,EAAG,EAAO,CAC/E,KAAM,WACR,KCPF,GAAU,KAAK,CAAG,GAClB,GAAU,SAAS,CAAG,mCCLlB,GAAoB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,YAAa,UAAW,QAAW,gBAAiB,UAAa,OAAQ,EAAG,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,+NAAgO,CAAE,EAAE,AAAC,EAAG,KAAQ,cAAe,MAAS,UAAW,ECctc,GAAuB,EAAM,UAAU,CARnB,SAA2B,CAAK,CAAE,CAAG,EAC3D,OAAoB,EAAM,aAAa,CAAC,SAAQ,CAAE,GAAA,SAAQ,EAAC,CAAC,EAAG,EAAO,CACpE,IAAK,EACL,KAAM,EACR,IACF,oICGO,SAAS,GAAc,CAA6C,SACzE,AAAI,AAAc,KAAA,IAAd,EAAK,IAAI,CACJ,EAAK,IAAI,GAAK,WAAQ,CAAC,YAAY,CAErC,EAAK,SAAS,CACvB,CAuFG,WAAQ,CAAC,YAAY,CACrB,WAAQ,CAAC,WAAW,CCjDvB,GAAM,CAAE,KAAA,EAAI,CAAE,CAAG,SAAU,CACrB,CAAE,OAAA,EAAM,CAAE,CAAG,UAAM,CAcnB,GAAgD,CAAC,CACrD,OAAA,CAAM,CACN,UAAA,CAAS,CACT,eAAA,CAAc,CACf,IACC,GAAM,CAAC,EAAS,EAAW,CAAG,GAAA,UAAQ,EAAC,CAAA,GACjC,CAAC,EAAS,EAAW,CAAG,GAAA,UAAQ,EAAuB,EAAE,EACzD,CAAC,EAAY,EAAc,CAAG,GAAA,UAAQ,EAAC,IACvC,CAAC,EAAiB,EAAmB,CAAG,GAAA,UAAQ,EACpD,EAAE,EAEE,CAAC,EAAiB,EAAmB,CAAG,GAAA,UAAQ,EAAc,EAAE,EAChE,CAAC,EAAc,EAAgB,CAAG,GAAA,UAAQ,EAAS,OAEzD,GAAA,WAAS,EAAC,KACR,IACF,EAAG,CAAC,EAAO,EAaX,GAAA,WAAS,EAAC,KAER,GAAI,CAAC,GAAW,CAAC,MAAM,OAAO,CAAC,GAAU,CACvC,EAAmB,EAAE,EACrB,OACF,CAwBA,EAtBiB,EAAQ,MAAM,CAAC,AAAC,IAE/B,GAAI,CAAC,GAAU,CAAC,EAAO,IAAI,EAAI,CAAC,EAAO,KAAK,CAC1C,MAAO,CAAA,EAIT,IAAM,EACJ,CAAC,GACD,EAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,EAAW,WAAW,KACzD,EAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,EAAW,WAAW,IAGtD,EACJ,AAAiB,QAAjB,GACC,AAAiB,WAAjB,GAA6B,EAAO,QAAQ,EAC5C,AAAiB,aAAjB,GAA+B,CAAC,EAAO,QAAQ,EAC/C,AAAiB,YAAjB,GAA8B,EAAO,SAAS,EAC9C,AAAiB,WAAjB,GAA6B,CAAC,EAAO,SAAS,CAEjD,OAAO,GAAiB,EAC1B,IAEF,EAAG,CAAC,EAAS,EAAY,EAAa,EActC,IAAM,EAAe,UACnB,GAAI,CACF,EAAW,CAAA,GACX,IAAM,EAAW,MAAM,aAAW,CAAC,cAAc,CAAC,CAChD,QAAS,EACT,SAAU,GACZ,GAEA,EAAW,OAAA,SAAA,EAAU,IAAI,GAAI,EAAE,EACjC,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,oDAAa,GAC3B,SAAO,CAAC,KAAK,CAAC,oDAEd,EAAW,EAAE,EACf,QAAU,CACR,EAAW,CAAA,GACb,CACF,EAEM,EAAqB,AAAC,IAC1B,GAAI,EAAO,SAAS,CAAE,CACpB,SAAO,CAAC,OAAO,CAAC,0DAChB,OACF,CAEA,UAAK,CAAC,OAAO,CAAC,CACZ,MAAO,uCACP,QAAS,CAAC,0DAAS,EAAE,EAAO,IAAI,CAAC,kBAAI,CAAC,CACtC,OAAQ,eACR,WAAY,eACZ,KAAM,UACJ,GAAI,CACF,MAAM,aAAW,CAAC,YAAY,CAAC,EAAO,EAAE,EACxC,SAAO,CAAC,OAAO,CAAC,wCAChB,UACA,GAAA,IACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wCAAW,GAC3B,CACF,CACF,GACF,EAoCM,EAA2C,CAC/C,CACE,MAAO,eACP,UAAW,OACX,IAAK,OACL,OAAQ,CAAC,EAAM,IACb,WAAC,UAAK,YACJ,UAAC,SAAM,EAAC,KAAK,QAAQ,KAAM,UAAC,SAAY,OACxC,WAAC,iBACC,UAAC,gBAAK,IACN,UAAC,OAAI,MAAO,CAAE,SAAU,GAAI,MAAO,MAAO,WAAI,EAAO,KAAK,QAIlE,EACA,CACE,MAAO,eACP,IAAK,OACL,MAAO,IACP,OAAQ,CAAC,EAAG,KACV,IAAM,EAAY,GAAc,GAChC,MACE,UAAC,UAAG,EACF,MD/LH,GC+L0B,GD/LJ,OAAS,OCgM5B,KAAM,EAAY,UAAC,SAAa,KAAM,UAAC,SAAY,cD7M7D,AAAI,GC+M0B,GD9MrB,iCAEF,6BC+MH,CACF,EACA,CACE,MAAO,eACP,UAAW,WACX,IAAK,SACL,MAAO,GACP,OAAQ,AAAC,GACP,UAAC,UAAG,EAAC,MAAO,EAAW,QAAU,eAC9B,EAAW,eAAO,gBAGzB,EACA,CACE,MAAO,2BACP,UAAW,aACX,IAAK,aACL,MAAO,IACP,OAAQ,AAAC,GAAe,IAAI,KAAK,GAAY,kBAAkB,EACjE,EACA,CACE,MAAO,2BACP,UAAW,iBACX,IAAK,iBACL,MAAO,IACP,OAAQ,AAAC,IACP,IAAM,EAAO,IAAI,KAAK,GAEhB,EAAW,KAAK,KAAK,CACzB,AAAC,CAAA,AAFS,IAAI,OAET,OAAO,GAAK,EAAK,OAAO,EAAC,EAAM,OAGlC,EAAQ,QAIZ,OAHI,EAAW,GAAG,CAAA,EAAQ,QAAO,EAC7B,EAAW,IAAI,CAAA,EAAQ,KAAI,EAG7B,UAAC,UAAO,EAAC,MAAO,EAAK,cAAc,YACjC,UAAC,UAAG,EAAC,MAAO,WACT,AAAa,IAAb,EAAiB,eAAO,CAAC,EAAE,EAAS,gBAAE,CAAC,KAIhD,CACF,EACA,CACE,MAAO,eACP,IAAK,SACL,MAAO,IACP,OAAQ,CAAC,EAAG,KACV,GAAI,CAAC,GAAa,EAAO,SAAS,CAChC,MAAO,UAAC,IAAK,KAAK,qBAAY,MAGhC,IAAM,EAAgC,CACpC,CACE,IAAK,SACL,MAAO,2BACP,KAAM,UAAC,SAAc,KACrB,OAAQ,CAAA,EACR,QAAS,IAAM,EAAmB,EACpC,EACD,CAED,MACE,WAAC,UAAK,EAAC,KAAK,kBACV,UAAC,SAAM,EACL,KAAK,OACL,MAAM,IACN,KAAK,QACL,KAAM,UAAC,SAAc,KACrB,QAAS,IAAM,EAAmB,YACnC,iBAGD,UAAC,SAAQ,EAAC,KAAM,CAAE,MAAO,CAAU,EAAG,QAAS,CAAC,QAAQ,UACtD,UAAC,SAAM,EAAC,KAAK,OAAO,KAAK,QAAQ,KAAM,UAAC,cAIhD,CACF,EACD,CAYD,MACE,WAAC,SAAI,EACH,MACE,WAAC,UAAK,YACJ,UAAC,IAAK,MAAM,aAAC,6BACb,UAAC,SAAK,EAAC,MAAO,EAAgB,MAAM,CAAE,QAAQ,SAGlD,MACE,WAAC,UAAK,YACJ,WAAC,UAAM,EACL,MAAO,EACP,SAAU,EACV,MAAO,CAAE,MAAO,GAAI,EACpB,KAAK,kBAEL,UAAC,IAAO,MAAM,eAAM,iBACpB,UAAC,IAAO,MAAM,kBAAS,iBACvB,UAAC,IAAO,MAAM,oBAAW,iBACzB,UAAC,IAAO,MAAM,mBAAU,uBACxB,UAAC,IAAO,MAAM,kBAAS,oBAEzB,UAAC,UAAK,EACJ,YAAY,2BACZ,OAAQ,UAAC,UAAc,KACvB,MAAO,EACP,SAAU,AAAC,GAAM,EAAc,EAAE,MAAM,CAAC,KAAK,EAC7C,MAAO,CAAE,MAAO,GAAI,EACpB,KAAK,uBAKV,EAAgB,MAAM,CAAG,GAAK,GAC7B,UAAC,OACC,MAAO,CACL,aAAc,GACd,QAAS,GACT,WAAY,UACZ,aAAc,CAChB,WAEA,WAAC,UAAK,YACJ,WAAC,cAAK,sBAAK,EAAgB,MAAM,CAAC,yBAClC,UAAC,SAAM,EACL,KAAK,QACL,MAAM,IACN,KAAM,UAAC,SAAc,KACrB,QA7Mc,KACxB,IAAM,EAAkB,AAAC,CAAA,GAAW,EAAE,AAAD,EAAG,MAAM,CAC5C,AAAC,GAAW,EAAgB,QAAQ,CAAC,EAAO,EAAE,GAAK,CAAC,EAAO,SAAS,EAGtE,GAAI,AAA2B,IAA3B,EAAgB,MAAM,CAAQ,CAChC,SAAO,CAAC,OAAO,CAAC,0DAChB,OACF,CAEA,UAAK,CAAC,OAAO,CAAC,CACZ,MAAO,uCACP,QAAS,CAAC,iEAAS,EAAE,EAAgB,MAAM,CAAC,yCAAM,CAAC,CACnD,OAAQ,eACR,WAAY,eACZ,KAAM,UACJ,GAAI,CACF,MAAM,QAAQ,GAAG,CACf,EAAgB,GAAG,CAAC,AAAC,GACnB,aAAW,CAAC,YAAY,CAAC,EAAO,EAAE,IAGtC,SAAO,CAAC,OAAO,CAAC,CAAC,iCAAK,EAAE,EAAgB,MAAM,CAAC,yBAAI,CAAC,EACpD,EAAmB,EAAE,EACrB,UACA,GAAA,IACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,oDAAa,GAC3B,SAAO,CAAC,KAAK,CAAC,wCAChB,CACF,CACF,GACF,WA8KW,6BAGD,UAAC,SAAM,EAAC,KAAK,QAAQ,QAAS,IAAM,EAAmB,EAAE,WAAG,kCAOlE,UAAC,UAAK,EACJ,QAAS,EACT,WAAY,EACZ,OAAO,KACP,QAAS,EACT,aAAc,EA1EC,CACnB,gBAAA,EACA,SAAU,AAAC,IACT,EAAmB,GACrB,EACA,iBAAkB,AAAC,GAAgC,CAAA,CACjD,SAAU,EAAO,SAAS,AAC5B,CAAA,CACF,EAkE+C,KAAA,EACzC,WAAY,CACV,gBAAiB,CAAA,EACjB,gBAAiB,CAAA,EACjB,UAAW,AAAC,GAAU,CAAC,SAAE,EAAE,EAAM,yBAAI,CAAC,CACtC,SAAU,EACZ,OAIR,EC3XM,CAAE,MAAA,EAAK,CAAE,KAAA,EAAI,CAAE,UAAA,EAAS,CAAE,CAAG,SAAU,CACvC,CAAE,SAAA,EAAQ,CAAE,CAAG,UAAK,CAYpB,GAAsD,CAAC,CAC3D,WAAA,CAAU,CACV,QAAA,CAAO,CACP,UAAA,CAAS,CACT,eAAA,EAAiB,CAAA,CAAK,CACtB,OAAA,CAAM,CACP,IACC,GAAM,CAAC,EAAkB,EAAoB,CAAG,GAAA,UAAQ,EAAC,CAAA,GACnD,CAAC,EAAU,EAAY,CAAG,GAAA,UAAQ,EAAC,CAAA,GACnC,CAAC,EAAU,EAAY,CAAG,GAAA,UAAQ,EAAC,CAAA,GACnC,CAAC,EAAK,CAAG,UAAI,CAAC,OAAO,GACrB,CAAE,gBAAA,CAAe,CAAE,CAAG,GAAA,UAAQ,EAAC,kBAG/B,EAAa,AAAC,GACX,IAAI,KAAK,GAAY,kBAAkB,CAAC,QAAS,CACtD,KAAM,UACN,MAAO,OACP,IAAK,SACP,GAGI,EAAqB,KACzB,GAAI,CAAC,EAAY,MAAO,UACxB,IAAM,EAAc,EAAW,WAAW,QAC1C,AAAI,GAAe,GAAW,UAC1B,GAAe,EAAU,UACtB,UACT,EAiCM,EAAmB,MAAO,IAC9B,GAAK,EAEL,GAAI,CACF,EAAY,CAAA,GACZ,MAAM,aAAW,CAAC,iBAAiB,CAAC,GACpC,SAAO,CAAC,OAAO,CAAC,oDAChB,EAAoB,CAAA,GACpB,IACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wCAAW,GACzB,SAAO,CAAC,KAAK,CAAC,wCAChB,QAAU,CACR,EAAY,CAAA,GACd,CACF,SAkDA,AAAI,EAEA,UAAC,OAAI,MAAO,CAAE,UAAW,SAAU,QAAS,QAAS,WACnD,UAAC,SAAI,EAAC,KAAK,YAKZ,EAWH,WAAC,OAAI,MAAO,CAAE,QAAS,QAAS,YAE9B,UAAC,SAAI,EACH,MAAO,CACL,aAAc,GACd,WAAY,oDACZ,OAAQ,OACR,aAAc,EAChB,EACA,OAAQ,CAAE,KAAM,CAAE,QAAS,MAAO,CAAE,WAEpC,WAAC,UAAG,EAAC,MAAM,SAAS,QAAQ,0BAEzB,GACC,UAAC,SAAG,WACF,UAAC,SAAM,EACL,KAAK,OACL,KAAM,UAAC,MACP,QA7HO,KACf,EACF,IAEA,SAAO,CAAC,IAAI,CAAC,qBAEjB,EAwHc,MAAO,CACL,MAAO,2BACP,SAAU,GACV,QAAS,SACX,WACD,mBAOL,UAAC,SAAG,EACF,KAAK,OACL,MAAO,CACL,QAAS,OACT,eAAgB,SAChB,SAAU,KACZ,WAEA,WAAC,UAAK,EAAC,KAAK,QAAQ,MAAM,mBACxB,UAAC,SAAM,EACL,KAAM,GACN,KAAM,UAAC,SAAY,KACnB,MAAO,CACL,gBAAiB,2BACjB,MAAO,QACP,SAAU,EACZ,IAEF,WAAC,iBACC,WAAC,UAAK,EAAC,MAAM,SAAS,MAAO,CAAE,aAAc,CAAE,YAC7C,UAAC,IAAM,MAAO,EAAG,MAAO,CAAE,MAAO,QAAS,OAAQ,CAAE,WACjD,EAAW,IAAI,GAEjB,EAAW,SAAS,EACnB,UAAC,UAAG,EACF,KAAM,UAAC,SAAa,KACpB,MAAM,OACN,MAAO,CAAE,SAAU,EAAG,WACvB,uBAIH,UAAC,SAAK,EACJ,MAAO,IACP,KACE,UAAC,IAAK,MAAO,CAAE,MAAO,0BAA2B,WAC9C,AAtLG,CAAA,KACxB,GAAI,CAAC,EAAY,MAAO,2BACxB,IAAM,EAAc,EAAW,WAAW,QAC1C,AAAI,GAAe,GAAW,2BAC1B,GAAe,EAAU,2BACtB,2BACT,CAAA,WAqLc,UAAC,IACC,MAAO,CACL,MAAO,2BACP,OAAQ,EACR,UAAW,QACb,EACA,SAAU,CAAE,KAAM,CAAE,WAEnB,EAAW,WAAW,EAAI,mEAOnC,UAAC,SAAG,WACD,EAAW,SAAS,EACnB,UAAC,SAAQ,EACP,KAAM,CAAE,KAAK,EA5HzB,CACE,IAAK,OACL,KAAM,UAAC,SAAY,KACnB,MAAO,2BACP,QAlEe,KACZ,IACL,EAAK,cAAc,CAAC,CAClB,KAAM,EAAW,IAAI,CACrB,YAAa,EAAW,WAAW,EAAI,EACzC,GACA,EAAoB,CAAA,IACtB,CA4DE,EACA,CACE,IAAK,SACL,KAAM,UAAC,SAAc,KACrB,MAAO,2BACP,OAAQ,CAAA,EACR,QAzCqB,KAClB,GAEL,UAAK,CAAC,OAAO,CAAC,CACZ,MAAO,uCACP,QAAS,CAAC,0DAAS,EAAE,EAAW,IAAI,CAAC,kFAAY,CAAC,CAClD,KAAM,UAAC,SAAyB,KAChC,OAAQ,2BACR,WAAY,eACZ,OAAQ,SACR,KAAM,UACJ,GAAI,CACF,EAAY,CAAA,GACZ,MAAM,aAAW,CAAC,iBAAiB,GACnC,SAAO,CAAC,OAAO,CAAC,wCAEhB,EAAgB,AAAC,GAAO,CAAA,CAAE,GAAG,CAAC,CAAE,YAAa,KAAA,CAAU,CAAA,GACvD,SAAO,CAAC,IAAI,CAAC,qBACf,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wCAAW,GACzB,SAAO,CAAC,KAAK,CAAC,wCAChB,QAAU,CACR,EAAY,CAAA,GACd,CACF,CACF,GACF,CAgBE,EAgH6C,EACjC,QAAS,CAAC,QAAQ,CAClB,UAAU,uBAEV,UAAC,SAAM,EACL,KAAK,OACL,KAAM,UAAC,SAAe,KACtB,MAAO,CACL,MAAO,2BACP,SAAU,GACV,MAAO,GACP,OAAQ,EACV,aASZ,WAAC,UAAG,EAAC,OAAQ,CAAC,GAAI,GAAG,CAAE,MAAO,CAAE,aAAc,EAAG,YAC/C,UAAC,SAAG,EAAC,GAAI,GAAI,GAAI,GAAI,GAAI,WACvB,UAAC,SAAI,WACH,UAAC,IACC,MAAM,2BACN,MAAO,EAAW,WAAW,CAC7B,OAAO,SACP,OAAQ,UAAC,SAAY,EAAC,MAAO,CAAE,MAAO,SAAU,IAChD,WAAY,CAAE,MAAO,SAAU,QAIrC,UAAC,SAAG,EAAC,GAAI,GAAI,GAAI,GAAI,GAAI,WACvB,UAAC,SAAI,WACH,UAAC,IACC,MAAM,2BACN,MAAO,EAAW,EAAW,SAAS,EACtC,OAAQ,UAAC,SAAgB,EAAC,MAAO,CAAE,MAAO,SAAU,IACpD,WAAY,CAAE,MAAO,UAAW,SAAU,EAAG,QAInD,UAAC,SAAG,EAAC,GAAI,GAAI,GAAI,GAAI,GAAI,WACvB,UAAC,SAAI,WACH,UAAC,IACC,MAAM,2BACN,MAAO,EAAW,EAAW,SAAS,EACtC,OAAQ,UAAC,SAAmB,EAAC,MAAO,CAAE,MAAO,SAAU,IACvD,WAAY,CAAE,MAAO,UAAW,SAAU,EAAG,QAInD,UAAC,SAAG,EAAC,GAAI,GAAI,GAAI,GAAI,GAAI,WACvB,UAAC,SAAI,WACH,WAAC,OAAI,MAAO,CAAE,UAAW,QAAS,YAChC,UAAC,IAAK,KAAK,YAAY,MAAO,CAAE,SAAU,EAAG,WAAG,mCAGhD,UAAC,OAAI,MAAO,CAAE,UAAW,CAAE,WACzB,UAAC,UAAQ,EACP,KAAK,SACL,KAAM,GACN,QAAS,KAAK,GAAG,CAAC,AAAyB,GAAzB,EAAW,WAAW,CAAO,KAC/C,YAAa,IACb,OAAQ,IACN,UAAC,IAAK,MAAO,CAAE,SAAU,GAAI,MAAO,GAAqB,WACtD,EAAW,WAAW,EAAI,GACvB,SACA,EAAW,WAAW,EAAI,EACxB,SACA,yBAWtB,UAAC,IACC,OAAQ,EAAW,EAAE,CACrB,UAAW,EAAW,SAAS,CAC/B,eAAgB,IAIlB,UAAC,UAAK,EACJ,MAAM,uCACN,KAAM,EACN,SAAU,IAAM,EAAoB,CAAA,GACpC,OAAQ,KACR,MAAO,aAEP,WAAC,UAAI,EAAC,KAAM,EAAM,OAAO,WAAW,SAAU,YAC5C,UAAC,UAAI,CAAC,IAAI,EACR,MAAM,2BACN,KAAK,OACL,MAAO,CACL,CAAE,SAAU,CAAA,EAAM,QAAS,4CAAU,EACrC,CAAE,IAAK,GAAI,QAAS,sEAAgB,EACrC,UAED,UAAC,UAAK,EAAC,YAAY,iDAErB,UAAC,UAAI,CAAC,IAAI,EACR,MAAM,2BACN,KAAK,cACL,MAAO,CAAC,CAAE,IAAK,IAAK,QAAS,uEAAiB,EAAE,UAEhD,UAAC,IACC,KAAM,EACN,YAAY,qEACZ,SAAS,IACT,UAAW,QAGf,UAAC,UAAI,CAAC,IAAI,EAAC,MAAO,CAAE,aAAc,EAAG,UAAW,OAAQ,WACtD,WAAC,UAAK,YACJ,UAAC,SAAM,EAAC,QAAS,IAAM,EAAoB,CAAA,YAAQ,iBACnD,UAAC,SAAM,EAAC,KAAK,UAAU,SAAS,SAAS,QAAS,WAAU,8BA7NpE,UAAC,UAAK,EACJ,MAAO,UAAK,CAAC,sBAAsB,CACnC,YAAY,qDAoOpB,EC1ZM,CAAE,KAAA,EAAI,CAAE,CAAG,SAAU,CAErB,GAA2B,KAC/B,GAAM,CAAC,EAAS,EAAW,CAAG,GAAA,UAAQ,EAAC,CAAA,GACjC,CAAC,EAAY,EAAc,CAAG,GAAA,UAAQ,EAA4B,MAExE,GAAA,WAAS,EAAC,KACR,IACF,EAAG,EAAE,EAEL,IAAM,EAAkB,UACtB,GAAI,CACF,EAAW,CAAA,GACX,IAAM,EAAS,MAAM,aAAW,CAAC,oBAAoB,GACrD,EAAc,GAChB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,oDAAa,GAC3B,SAAO,CAAC,KAAK,CAAC,oDAChB,QAAU,CACR,EAAW,CAAA,GACb,CACF,SAEA,AAAI,EAEA,UAAC,eAAa,WACZ,UAAC,OAAI,MAAO,CAAE,UAAW,SAAU,QAAS,QAAS,WACnD,UAAC,SAAI,EAAC,KAAK,cAMd,EAWH,UAAC,eAAa,EAAC,MAAO,CAAE,WAAY,SAAU,WAC5C,UAAC,IACC,WAAY,EACZ,QAAS,EACT,UAAW,EACX,eAAgB,CAAA,MAdlB,UAAC,eAAa,WACZ,UAAC,OAAI,MAAO,CAAE,UAAW,SAAU,QAAS,QAAS,WACnD,UAAC,IAAK,KAAK,qBAAY,yDAgBjC"}