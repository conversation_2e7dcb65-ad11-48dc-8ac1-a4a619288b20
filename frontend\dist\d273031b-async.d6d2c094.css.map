{"version": 3, "sources": ["src/.umi-production/plugin-layout/Layout.css"], "sourcesContent": ["@media screen and (max-width: 480px) {\n  /* 在小屏幕的时候可以有更好的体验 */\n  .umi-plugin-layout-container {\n    width: 100% !important;\n  }\n  .umi-plugin-layout-container > * {\n    border-radius: 0 !important;\n  }\n}\n.umi-plugin-layout-menu .anticon {\n  margin-right: 8px;\n}\n.umi-plugin-layout-menu .ant-dropdown-menu-item {\n  min-width: 160px;\n}\n.umi-plugin-layout-right {\n  display: flex !important;\n  float: right;\n  height: 100%;\n  margin-left: auto;\n  overflow: hidden;\n}\n.umi-plugin-layout-right .umi-plugin-layout-action {\n  display: flex;\n  align-items: center;\n  height: 100%;\n  padding: 0 12px;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n.umi-plugin-layout-right .umi-plugin-layout-action > i {\n  color: rgba(255, 255, 255, 0.85);\n  vertical-align: middle;\n}\n.umi-plugin-layout-right .umi-plugin-layout-action:hover {\n  background: rgba(0, 0, 0, 0.025);\n}\n.umi-plugin-layout-right .umi-plugin-layout-action.opened {\n  background: rgba(0, 0, 0, 0.025);\n}\n.umi-plugin-layout-right .umi-plugin-layout-search {\n  padding: 0 12px;\n}\n.umi-plugin-layout-right .umi-plugin-layout-search:hover {\n  background: transparent;\n}\n.umi-plugin-layout-name {\n  margin-left: 8px;\n}\n.umi-plugin-layout-name.umi-plugin-layout-hide-avatar-img {\n  margin-left: 0;\n}\n"], "names": [], "mappings": "AAAA,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,SAAS,CAAE,GAAG,EAAE,CAAC,AAAC,CAAC,AAEpC,CAAC,2BAA2B,AAAC,CAAC,AAC5B,KAAK,CAAE,GAAG,CAAC,AAAC,CAAC,SAAS,AACxB,CAAC,AACD,CAAC,2BAA2B,AAAC,CAAC,AAAC,CAAC,AAAC,CAAC,AAChC,aAAa,CAAE,CAAC,AAAC,CAAC,SAAS,AAC7B,CAAC,AACH,CAAC,AACD,CAAC,sBAAsB,CAAC,CAAC,OAAO,AAAC,CAAC,AAChC,YAAY,CAAE,CAAC,EAAE,AACnB,CAAC,AACD,CAAC,sBAAsB,CAAC,CAAC,sBAAsB,AAAC,CAAC,AAC/C,SAAS,CAAE,GAAG,EAAE,AAClB,CAAC,AACD,CAAC,uBAAuB,AAAC,CAAC,AACxB,OAAO,CAAE,IAAI,AAAC,CAAC,SAAS,CACxB,KAAK,CAAE,KAAK,CACZ,MAAM,CAAE,GAAG,CAAC,CACZ,WAAW,CAAE,IAAI,CACjB,QAAQ,CAAE,MAAM,AAClB,CAAC,AACD,CAAC,uBAAuB,CAAC,CAAC,wBAAwB,AAAC,CAAC,AAClD,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,CACZ,OAAO,CAAE,CAAC,CAAC,EAAE,EAAE,CACf,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,GAAG,AAAC,EAAG,CAAC,AACtB,CAAC,AACD,CAAC,uBAAuB,CAAC,CAAC,wBAAwB,AAAC,CAAC,AAAC,CAAC,AAAC,CAAC,AACtD,KAAK,uBACL,cAAc,CAAE,MAAM,AACxB,CAAC,AACD,CAAC,uBAAuB,CAAC,CAAC,wBAAwB,CAAC,KAAK,AAAC,CAAC,AACxD,UAAU,iBACZ,CAAC,AACD,CAAC,uBAAuB,CAAC,CAAC,wBAAwB,CAAC,MAAM,AAAC,CAAC,AACzD,UAAU,iBACZ,CAAC,AACD,CAAC,uBAAuB,CAAC,CAAC,wBAAwB,AAAC,CAAC,AAClD,OAAO,CAAE,CAAC,CAAC,EAAE,EAAE,AACjB,CAAC,AACD,CAAC,uBAAuB,CAAC,CAAC,wBAAwB,CAAC,KAAK,AAAC,CAAC,AACxD,UAAU,CAAE,WAAW,AACzB,CAAC,AACD,CAAC,sBAAsB,AAAC,CAAC,AACvB,WAAW,CAAE,CAAC,EAAE,AAClB,CAAC,AACD,CAAC,sBAAsB,CAAC,iCAAiC,AAAC,CAAC,AACzD,WAAW,CAAE,CAAC,AAChB,CAAC"}