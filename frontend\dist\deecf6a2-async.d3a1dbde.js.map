{"version": 3, "sources": ["F:/Project/teamAuth/frontend/src/.umi-production/core/EmptyRoute.tsx"], "sourcesContent": ["// @ts-nocheck\n// This file is generated by Um<PERSON> automatically\n// DO NOT CHANGE IT MANUALLY!\nimport React from 'react';\nimport { Outlet, useOutletContext } from 'umi';\nexport default function EmptyRoute() {\n  const context = useOutletContext();\n  return <Outlet context={context} />;\n}\n"], "names": [], "mappings": "qQAKA,+CAAwB,4BAFN,oBACuB,YAC1B,SAAS,IACtB,IAAM,EAAU,GAAA,kBAAgB,IAChC,MAAO,UAAC,QAAM,EAAC,QAAS,IAC1B"}