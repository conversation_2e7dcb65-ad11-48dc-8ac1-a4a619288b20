{"version": 3, "sources": ["node_modules/@ant-design/icons-svg/es/asn/SafetyOutlined.js", "node_modules/@ant-design/icons/es/icons/SafetyOutlined.js", "src/pages/user/login/index.tsx"], "sourcesContent": ["// This icon file is generated automatically.\nvar SafetyOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"0 0 1024 1024\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M512 64L128 192v384c0 212.1 171.9 384 384 384s384-171.9 384-384V192L512 64zm312 512c0 172.3-139.7 312-312 312S200 748.3 200 576V246l312-110 312 110v330z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M378.4 475.1a35.91 35.91 0 00-50.9 0 35.91 35.91 0 000 50.9l129.4 129.4 2.1 2.1a33.98 33.98 0 0048.1 0L730.6 434a33.98 33.98 0 000-48.1l-2.8-2.8a33.98 33.98 0 00-48.1 0L483 579.7 378.4 475.1z\" } }] }, \"name\": \"safety\", \"theme\": \"outlined\" };\nexport default SafetyOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SafetyOutlinedSvg from \"@ant-design/icons-svg/es/asn/SafetyOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SafetyOutlined = function SafetyOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SafetyOutlinedSvg\n  }));\n};\n\n/**![safety](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEwxMjggMTkydjM4NGMwIDIxMi4xIDE3MS45IDM4NCAzODQgMzg0czM4NC0xNzEuOSAzODQtMzg0VjE5Mkw1MTIgNjR6bTMxMiA1MTJjMCAxNzIuMy0xMzkuNyAzMTItMzEyIDMxMlMyMDAgNzQ4LjMgMjAwIDU3NlYyNDZsMzEyLTExMCAzMTIgMTEwdjMzMHoiIC8+PHBhdGggZD0iTTM3OC40IDQ3NS4xYTM1LjkxIDM1LjkxIDAgMDAtNTAuOSAwIDM1LjkxIDM1LjkxIDAgMDAwIDUwLjlsMTI5LjQgMTI5LjQgMi4xIDIuMWEzMy45OCAzMy45OCAwIDAwNDguMSAwTDczMC42IDQzNGEzMy45OCAzMy45OCAwIDAwMC00OC4xbC0yLjgtMi44YTMzLjk4IDMzLjk4IDAgMDAtNDguMSAwTDQ4MyA1NzkuNyAzNzguNCA0NzUuMXoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SafetyOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SafetyOutlined';\n}\nexport default RefIcon;", "/**\n * 登录页面\n * 实现双阶段认证的第一阶段：账号登录\n */\n\nimport { MailOutlined, SafetyOutlined } from '@ant-design/icons';\nimport { Helmet, history, useModel } from '@umijs/max';\nimport {\n  Button,\n  Card,\n  Form,\n  Input,\n  message,\n  Space,\n  Typography,\n} from 'antd';\nimport { createStyles } from 'antd-style';\nimport React, { useState, useCallback, useMemo, useEffect } from 'react';\nimport { Footer } from '@/components';\nimport { AuthService } from '@/services';\nimport type { LoginRequest, SendVerificationCodeRequest } from '@/types/api';\nimport Settings from '../../../../config/defaultSettings';\n\nconst { Title, Text } = Typography;\n\n// 登录表单组件（移到外部避免重新创建）\nconst LoginFormComponent: React.FC<{\n  form: any;\n  handleLogin: (values: LoginRequest) => void;\n  handleSendCode: () => void;\n  sendingCode: boolean;\n  countdown: number;\n  loading: boolean;\n}> = React.memo(({ form, handleLogin, handleSendCode, sendingCode, countdown, loading }) => {\n  // 使用 useMemo 稳定按钮渲染，避免因倒计时变化导致输入框重新渲染\n  const sendCodeButton = useMemo(() => (\n    <Button\n      type=\"link\"\n      size=\"small\"\n      disabled={countdown > 0 || sendingCode}\n      loading={sendingCode}\n      onClick={handleSendCode}\n      style={{ padding: 0, height: 'auto' }}\n    >\n      {countdown > 0 ? `${countdown}s后重发` : '发送验证码'}\n    </Button>\n  ), [countdown, sendingCode, handleSendCode]);\n\n  // 使用 useMemo 稳定邮箱输入框，避免重新渲染\n  const emailField = useMemo(() => (\n    <Form.Item\n      key=\"email-field\"\n      name=\"email\"\n      rules={[\n        { required: true, message: '请输入邮箱！' },\n        { type: 'email', message: '请输入有效的邮箱地址！' },\n      ]}\n    >\n      <Input\n        key=\"email-input\"\n        prefix={<MailOutlined />}\n        placeholder=\"邮箱\"\n        autoComplete=\"email\"\n      />\n    </Form.Item>\n  ), []);\n\n  // 使用 useMemo 稳定验证码输入框，只在按钮变化时重新渲染\n  const codeField = useMemo(() => (\n    <Form.Item\n      key=\"code-field\"\n      name=\"code\"\n      rules={[\n        { required: true, message: '请输入验证码！' },\n        { len: 6, message: '验证码为6位数字！' },\n        { pattern: /^\\d{6}$/, message: '验证码只能包含数字！' },\n      ]}\n    >\n      <Input\n        key=\"code-input\"\n        prefix={<SafetyOutlined />}\n        placeholder=\"6位验证码\"\n        maxLength={6}\n        suffix={sendCodeButton}\n      />\n    </Form.Item>\n  ), [sendCodeButton]);\n\n  return (\n    <Form\n      form={form}\n      name=\"login\"\n      size=\"large\"\n      onFinish={handleLogin}\n      autoComplete=\"off\"\n    >\n      {emailField}\n      {codeField}\n\n      {/* 提示信息 */}\n      <div style={{ marginBottom: 16, textAlign: 'center' }}>\n        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n          新用户将自动完成注册并登录\n        </Text>\n      </div>\n\n      <Form.Item>\n        <Button type=\"primary\" htmlType=\"submit\" loading={loading} block>\n          登录 / 注册\n        </Button>\n      </Form.Item>\n    </Form>\n  );\n});\n\nconst useStyles = createStyles(({ token }) => {\n  return {\n    container: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh',\n      overflow: 'auto',\n      backgroundImage:\n        \"url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')\",\n      backgroundSize: '100% 100%',\n    },\n    content: {\n      flex: 1,\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: '32px 16px',\n    },\n    header: {\n      marginBottom: 40,\n      textAlign: 'center',\n    },\n    logo: {\n      marginBottom: 16,\n    },\n    title: {\n      marginBottom: 0,\n    },\n    loginCard: {\n      width: '100%',\n      maxWidth: 400,\n      boxShadow: token.boxShadowTertiary,\n    },\n    footer: {\n      marginTop: 40,\n      textAlign: 'center',\n    },\n    lang: {\n      width: 42,\n      height: 42,\n      lineHeight: '42px',\n      position: 'fixed',\n      right: 16,\n      top: 16,\n      borderRadius: token.borderRadius,\n      ':hover': {\n        backgroundColor: token.colorBgTextHover,\n      },\n    },\n  };\n});\n\nconst LoginPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [sendingCode, setSendingCode] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const [form] = Form.useForm(); // 将表单实例提升到父组件\n  const { styles } = useStyles();\n  const { setInitialState } = useModel('@@initialState');\n\n  // 使用 Form 内置的邮箱验证\n\n  // 组件挂载时清除倒计时状态，避免页面刷新后无法输入\n  useEffect(() => {\n    setCountdown(0);\n  }, []);\n\n  // 倒计时效果\n  React.useEffect(() => {\n    let timer: NodeJS.Timeout;\n    if (countdown > 0) {\n      timer = setTimeout(() => {\n        setCountdown(countdown - 1);\n      }, 1000);\n    }\n    return () => {\n      if (timer) clearTimeout(timer);\n    };\n  }, [countdown]);\n\n  // 发送验证码\n  const handleSendCode = useCallback(async (type: 'login' | 'register' = 'login') => {\n    let email: string;\n\n    try {\n      // 验证邮箱字段\n      await form.validateFields(['email']);\n\n      // 从表单获取邮箱值\n      email = form.getFieldValue('email');\n      console.log('发送验证码前的邮箱值:', email);\n\n      if (!email) {\n        message.error('请输入邮箱地址');\n        return;\n      }\n    } catch (error) {\n      // 表单验证失败\n      message.error('请输入有效的邮箱地址');\n      return;\n    }\n\n    setSendingCode(true);\n    try {\n      const request: SendVerificationCodeRequest = { email, type };\n      const response = await AuthService.sendVerificationCode(request);\n\n      if (response.success) {\n        message.success(response.message);\n        setCountdown(60); // 60秒倒计时\n\n        // 验证码发送成功后检查表单值\n        console.log('发送验证码成功后的邮箱值:', form.getFieldValue('email'));\n\n        // 在开发环境中提示查看控制台\n        if (process.env.NODE_ENV === 'development') {\n          message.info('开发环境：请查看浏览器控制台或后端日志获取验证码', 5);\n        }\n      } else {\n        message.error(response.message);\n        if (response.nextSendTime) {\n          setCountdown(response.nextSendTime);\n        }\n      }\n    } catch (error) {\n      console.error('发送验证码失败:', error);\n      message.error('发送验证码失败，请稍后重试');\n    } finally {\n      setSendingCode(false);\n    }\n  }, [form]);\n\n  // 处理登录/注册\n  const handleLogin = useCallback(async (values: LoginRequest) => {\n    setLoading(true);\n    try {\n      const response = await AuthService.login(values);\n      message.success('登录成功！');\n\n      // 登录成功后停止倒计时\n      setCountdown(0);\n\n      // 登录成功后，刷新 initialState\n      await setInitialState((prevState) => ({\n        ...prevState,\n        currentUser: response.user,\n        currentTeam: response.teams.length > 0 ? response.teams[0] : undefined,\n      }));\n\n      // 根据团队数量进行不同的跳转处理\n      if (response.teams.length === 0) {\n        // 没有团队，跳转到个人中心页面\n        history.push('/personal-center');\n      } else {\n        // 有团队（无论一个还是多个），都跳转到个人中心整合页面\n        history.push('/personal-center', { teams: response.teams });\n      }\n    } catch (error) {\n      console.error('登录失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, [setInitialState]);\n\n  // 注册功能已移除，统一使用验证码登录/注册流程\n\n  return (\n    <div className={styles.container}>\n      <Helmet>\n        <title>\n          登录 / 注册\n          {Settings.title && ` - ${Settings.title}`}\n        </title>\n      </Helmet>\n      <div className={styles.content}>\n        <div className={styles.header}>\n          <Space direction=\"vertical\" align=\"center\" size=\"large\">\n            <div className={styles.logo}>\n              <img src=\"/logo.svg\" alt=\"TeamAuth\" height={48} />\n            </div>\n            <div className={styles.title}>\n              <Title level={2}>团队管理系统</Title>\n              <Text type=\"secondary\">现代化的团队协作与管理平台</Text>\n            </div>\n          </Space>\n        </div>\n\n        <Card className={styles.loginCard}>\n          <LoginFormComponent\n            form={form}\n            handleLogin={handleLogin}\n            handleSendCode={() => handleSendCode('login')}\n            sendingCode={sendingCode}\n            countdown={countdown}\n            loading={loading}\n          />\n        </Card>\n\n        <div className={styles.footer}>\n          <Text type=\"secondary\">© 2025 TeamAuth. All rights reserved.</Text>\n        </div>\n      </div>\n      <Footer />\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "names": [], "mappings": "0ZACI,EAAiB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,OAAQ,EAAG,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,0JAA2J,CAAE,EAAG,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,iMAAkM,CAAE,EAAE,AAAC,EAAG,KAAQ,SAAU,MAAS,UAAW,2BCczkB,EAAuB,EAAM,UAAU,CARtB,SAAwB,CAAK,CAAE,CAAG,EACrD,OAAoB,EAAM,aAAa,CAAC,SAAQ,CAAE,GAAA,SAAQ,EAAC,CAAC,EAAG,EAAO,CACpE,IAAK,EACL,KAAM,CACR,IACF,2QCWA,GAAM,CAAE,MAAA,CAAK,CAAE,KAAA,CAAI,CAAE,CAAG,SAAU,CAG5B,EAOD,SAAK,CAAC,IAAI,CAAC,CAAC,CAAE,KAAA,CAAI,CAAE,YAAA,CAAW,CAAE,eAAA,CAAc,CAAE,YAAA,CAAW,CAAE,UAAA,CAAS,CAAE,QAAA,CAAO,CAAE,IAErF,IAAM,EAAiB,GAAA,SAAO,EAAC,IAC7B,UAAC,SAAM,EACL,KAAK,OACL,KAAK,QACL,SAAU,EAAY,GAAK,EAC3B,QAAS,EACT,QAAS,EACT,MAAO,CAAE,QAAS,EAAG,OAAQ,MAAO,WAEnC,EAAY,EAAI,CAAC,EAAE,EAAU,yBAAI,CAAC,CAAG,mCAEvC,CAAC,EAAW,EAAa,EAAe,EAGrC,EAAa,GAAA,SAAO,EAAC,IACzB,UAAC,SAAI,CAAC,IAAI,EAER,KAAK,QACL,MAAO,CACL,CAAE,SAAU,CAAA,EAAM,QAAS,sCAAS,EACpC,CAAE,KAAM,QAAS,QAAS,oEAAc,EACzC,UAED,UAAC,SAAK,EAEJ,OAAQ,UAAC,SAAY,KACrB,YAAY,eACZ,aAAa,SAHT,gBARF,eAcL,EAAE,EAGC,EAAY,GAAA,SAAO,EAAC,IACxB,UAAC,SAAI,CAAC,IAAI,EAER,KAAK,OACL,MAAO,CACL,CAAE,SAAU,CAAA,EAAM,QAAS,4CAAU,EACrC,CAAE,IAAK,EAAG,QAAS,mDAAY,EAC/B,CAAE,QAAS,UAAW,QAAS,8DAAa,EAC7C,UAED,UAAC,SAAK,EAEJ,OAAQ,UAAC,MACT,YAAY,4BACZ,UAAW,EACX,OAAQ,GAJJ,eATF,cAgBL,CAAC,EAAe,EAEnB,MACE,WAAC,SAAI,EACH,KAAM,EACN,KAAK,QACL,KAAK,QACL,SAAU,EACV,aAAa,gBAEZ,EACA,EAGD,UAAC,OAAI,MAAO,CAAE,aAAc,GAAI,UAAW,QAAS,WAClD,UAAC,GAAK,KAAK,YAAY,MAAO,CAAE,SAAU,MAAO,WAAG,qFAKtD,UAAC,SAAI,CAAC,IAAI,WACR,UAAC,SAAM,EAAC,KAAK,UAAU,SAAS,SAAS,QAAS,EAAS,KAAK,aAAC,qCAMzE,GAEM,EAAY,GAAA,cAAY,EAAC,CAAC,CAAE,MAAA,CAAK,CAAE,GAChC,CAAA,CACL,UAAW,CACT,QAAS,OACT,cAAe,SACf,OAAQ,QACR,SAAU,OACV,gBACE,8FACF,eAAgB,WAClB,EACA,QAAS,CACP,KAAM,EACN,QAAS,OACT,cAAe,SACf,eAAgB,SAChB,WAAY,SACZ,QAAS,WACX,EACA,OAAQ,CACN,aAAc,GACd,UAAW,QACb,EACA,KAAM,CACJ,aAAc,EAChB,EACA,MAAO,CACL,aAAc,CAChB,EACA,UAAW,CACT,MAAO,OACP,SAAU,IACV,UAAW,EAAM,iBAAiB,AACpC,EACA,OAAQ,CACN,UAAW,GACX,UAAW,QACb,EACA,KAAM,CACJ,MAAO,GACP,OAAQ,GACR,WAAY,OACZ,SAAU,QACV,MAAO,GACP,IAAK,GACL,aAAc,EAAM,YAAY,CAChC,SAAU,CACR,gBAAiB,EAAM,gBAAgB,AACzC,CACF,CACF,CAAA,GAGI,EAAsB,KAC1B,GAAM,CAAC,EAAS,EAAW,CAAG,GAAA,UAAQ,EAAC,CAAA,GACjC,CAAC,EAAa,EAAe,CAAG,GAAA,UAAQ,EAAC,CAAA,GACzC,CAAC,EAAW,EAAa,CAAG,GAAA,UAAQ,EAAC,GACrC,CAAC,EAAK,CAAG,SAAI,CAAC,OAAO,GACrB,CAAE,OAAA,CAAM,CAAE,CAAG,IACb,CAAE,gBAAA,CAAe,CAAE,CAAG,GAAA,UAAQ,EAAC,kBAKrC,GAAA,WAAS,EAAC,KACR,EAAa,GACf,EAAG,EAAE,EAGL,SAAK,CAAC,SAAS,CAAC,KACd,IAAI,EAMJ,OALI,EAAY,GACd,CAAA,EAAQ,WAAW,KACjB,EAAa,EAAY,GAC3B,EAAG,IAAI,EAEF,KACD,GAAO,aAAa,GAC1B,EACF,EAAG,CAAC,EAAU,EAGd,IAAM,EAAiB,GAAA,aAAW,EAAC,MAAO,EAA6B,OAAO,IAC5E,IAAI,EAEJ,GAAI,CAQF,GANA,MAAM,EAAK,cAAc,CAAC,CAAC,QAAQ,EAGnC,EAAQ,EAAK,aAAa,CAAC,SAC3B,QAAQ,GAAG,CAAC,gEAAe,GAEvB,CAAC,EAAO,CACV,SAAO,CAAC,KAAK,CAAC,8CACd,OACF,CACF,CAAE,MAAO,EAAO,CAEd,SAAO,CAAC,KAAK,CAAC,gEACd,OACF,CAEA,EAAe,CAAA,GACf,GAAI,CACF,IAAM,EAAuC,CAAE,MAAA,EAAO,KAAA,CAAK,EACrD,EAAW,MAAM,aAAW,CAAC,oBAAoB,CAAC,GAEpD,EAAS,OAAO,EAClB,SAAO,CAAC,OAAO,CAAC,EAAS,OAAO,EAChC,EAAa,IAGb,QAAQ,GAAG,CAAC,4EAAiB,EAAK,aAAa,CAAC,YAOhD,SAAO,CAAC,KAAK,CAAC,EAAS,OAAO,EAC1B,EAAS,YAAY,EACvB,EAAa,EAAS,YAAY,GAGxC,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,8CAAY,GAC1B,SAAO,CAAC,KAAK,CAAC,kFAChB,QAAU,CACR,EAAe,CAAA,GACjB,CACF,EAAG,CAAC,EAAK,EAGH,EAAc,GAAA,aAAW,EAAC,MAAO,IACrC,EAAW,CAAA,GACX,GAAI,CACF,IAAM,EAAW,MAAM,aAAW,CAAC,KAAK,CAAC,GACzC,SAAO,CAAC,OAAO,CAAC,kCAGhB,EAAa,GAGb,MAAM,EAAgB,AAAC,GAAe,CAAA,CACpC,GAAG,CAAS,CACZ,YAAa,EAAS,IAAI,CAC1B,YAAa,EAAS,KAAK,CAAC,MAAM,CAAG,EAAI,EAAS,KAAK,CAAC,EAAE,CAAG,KAAA,CAC/D,CAAA,GAGI,AAA0B,IAA1B,EAAS,KAAK,CAAC,MAAM,CAEvB,SAAO,CAAC,IAAI,CAAC,oBAGb,SAAO,CAAC,IAAI,CAAC,mBAAoB,CAAE,MAAO,EAAS,KAAK,AAAC,GAE7D,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,4BAAS,GACzB,QAAU,CACR,EAAW,CAAA,GACb,CACF,EAAG,CAAC,EAAgB,EAIpB,MACE,WAAC,OAAI,UAAW,EAAO,SAAS,WAC9B,UAAC,QAAM,WACL,WAAC,mBAAM,8BAEJ,SAAQ,CAAC,KAAK,EAAI,CAAC,GAAG,EAAE,SAAQ,CAAC,KAAK,CAAC,CAAC,MAG7C,WAAC,OAAI,UAAW,EAAO,OAAO,WAC5B,UAAC,OAAI,UAAW,EAAO,MAAM,UAC3B,WAAC,SAAK,EAAC,UAAU,WAAW,MAAM,SAAS,KAAK,kBAC9C,UAAC,OAAI,UAAW,EAAO,IAAI,UACzB,UAAC,OAAI,IAAI,YAAY,IAAI,WAAW,OAAQ,OAE9C,WAAC,OAAI,UAAW,EAAO,KAAK,WAC1B,UAAC,GAAM,MAAO,WAAG,yCACjB,UAAC,GAAK,KAAK,qBAAY,2FAK7B,UAAC,SAAI,EAAC,UAAW,EAAO,SAAS,UAC/B,UAAC,GACC,KAAM,EACN,YAAa,EACb,eAAgB,IAAM,EAAe,SACrC,YAAa,EACb,UAAW,EACX,QAAS,MAIb,UAAC,OAAI,UAAW,EAAO,MAAM,UAC3B,UAAC,GAAK,KAAK,qBAAY,kDAG3B,UAAC,QAAM,QAGb"}