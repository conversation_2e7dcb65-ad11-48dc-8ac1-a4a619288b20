{"version": 3, "sources": ["node_modules/@ant-design/icons-svg/es/asn/BookOutlined.js", "node_modules/@ant-design/icons/es/icons/BookOutlined.js", "src/pages/help/index.tsx"], "sourcesContent": ["// This icon file is generated automatically.\nvar BookOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-260 72h96v209.9L621.5 312 572 347.4V136zm220 752H232V136h280v296.9c0 3.3 1 6.6 3 9.3a15.9 15.9 0 0022.3 3.7l83.8-59.9 81.4 59.4c2.7 2 6 3.1 9.4 3.1 8.8 0 16-7.2 16-16V136h64v752z\" } }] }, \"name\": \"book\", \"theme\": \"outlined\" };\nexport default BookOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport BookOutlinedSvg from \"@ant-design/icons-svg/es/asn/BookOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar BookOutlined = function BookOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: BookOutlinedSvg\n  }));\n};\n\n/**![book](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiA2NEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjgzMmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWOTZjMC0xNy43LTE0LjMtMzItMzItMzJ6bS0yNjAgNzJoOTZ2MjA5LjlMNjIxLjUgMzEyIDU3MiAzNDcuNFYxMzZ6bTIyMCA3NTJIMjMyVjEzNmgyODB2Mjk2LjljMCAzLjMgMSA2LjYgMyA5LjNhMTUuOSAxNS45IDAgMDAyMi4zIDMuN2w4My44LTU5LjkgODEuNCA1OS40YzIuNyAyIDYgMy4xIDkuNCAzLjEgOC44IDAgMTYtNy4yIDE2LTE2VjEzNmg2NHY3NTJ6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(BookOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'BookOutlined';\n}\nexport default RefIcon;", "import {\n  BookOutlined,\n  QuestionCircleOutlined,\n  SettingOutlined,\n  TeamOutlined,\n} from '@ant-design/icons';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { Button, Card, Divider, Space, Typography } from 'antd';\n\nconst { Title, Paragraph, Text } = Typography;\n\nconst HelpPage: React.FC = () => {\n  return (\n    <PageContainer\n      title=\"帮助中心\"\n      subTitle=\"团队协作管理系统使用指南\"\n      extra={[\n        <Button key=\"contact\" type=\"primary\">\n          联系技术支持\n        </Button>,\n      ]}\n    >\n      <div style={{ maxWidth: 1200, margin: '0 auto' }}>\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          {/* 快速开始 */}\n          <Card>\n            <Title level={3}>\n              <BookOutlined style={{ marginRight: 8 }} />\n              快速开始\n            </Title>\n            <Paragraph>\n              欢迎使用团队协作管理系统！本系统帮助您高效管理团队成员、项目和任务。\n            </Paragraph>\n            <Paragraph>\n              <Text strong>首次使用步骤：</Text>\n              <ol>\n                <li>注册账号并登录系统</li>\n                <li>创建或加入团队</li>\n                <li>邀请团队成员</li>\n                <li>开始协作管理</li>\n              </ol>\n            </Paragraph>\n          </Card>\n\n          {/* 团队管理 */}\n          <Card>\n            <Title level={3}>\n              <TeamOutlined style={{ marginRight: 8 }} />\n              团队管理\n            </Title>\n            <Paragraph>\n              <Text strong>创建团队：</Text>\n              在团队页面点击\"创建团队\"按钮，填写团队信息即可创建新团队。\n            </Paragraph>\n            <Paragraph>\n              <Text strong>邀请成员：</Text>\n              团队管理员可以通过邮箱邀请新成员加入团队。\n            </Paragraph>\n            <Paragraph>\n              <Text strong>角色权限：</Text>\n              系统支持多种角色权限，包括管理员、普通成员等，确保团队协作的安全性。\n            </Paragraph>\n          </Card>\n\n          {/* 系统设置 */}\n          <Card>\n            <Title level={3}>\n              <SettingOutlined style={{ marginRight: 8 }} />\n              系统设置\n            </Title>\n            <Paragraph>\n              <Text strong>个人设置：</Text>\n              在右上角头像菜单中可以修改个人信息、密码等设置。\n            </Paragraph>\n            <Paragraph>\n              <Text strong>团队设置：</Text>\n              团队管理员可以在团队设置页面修改团队信息、管理成员权限。\n            </Paragraph>\n          </Card>\n\n          {/* 常见问题 */}\n          <Card>\n            <Title level={3}>\n              <QuestionCircleOutlined style={{ marginRight: 8 }} />\n              常见问题\n            </Title>\n            <Paragraph>\n              <Text strong>Q: 如何切换团队？</Text>\n              <br />\n              A: 在顶部导航栏的团队名称处点击，可以选择切换到其他团队。\n            </Paragraph>\n            <Divider />\n            <Paragraph>\n              <Text strong>Q: 忘记密码怎么办？</Text>\n              <br />\n              A: 在登录页面点击\"忘记密码\"，通过邮箱重置密码。\n            </Paragraph>\n            <Divider />\n            <Paragraph>\n              <Text strong>Q: 如何邀请新成员？</Text>\n              <br />\n              A: 团队管理员可以在团队管理页面通过邮箱邀请新成员。\n            </Paragraph>\n          </Card>\n\n          {/* 联系我们 */}\n          <Card>\n            <Title level={3}>联系我们</Title>\n            <Paragraph>\n              如果您在使用过程中遇到问题，可以通过以下方式联系我们：\n            </Paragraph>\n            <Paragraph>\n              <Text strong>技术支持邮箱：</Text> <EMAIL>\n              <br />\n              <Text strong>用户反馈：</Text> <EMAIL>\n              <br />\n              <Text strong>工作时间：</Text> 周一至周五 9:00-18:00\n            </Paragraph>\n          </Card>\n        </Space>\n      </div>\n    </PageContainer>\n  );\n};\n\nexport default HelpPage;\n"], "names": [], "mappings": "iYACI,EAAe,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,OAAQ,EAAG,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,kSAAmS,CAAE,EAAE,AAAC,EAAG,KAAQ,OAAQ,MAAS,UAAW,2BCcre,EAAuB,EAAM,UAAU,CARxB,SAAsB,CAAK,CAAE,CAAG,EACjD,OAAoB,EAAM,aAAa,CAAC,SAAQ,CAAE,GAAA,SAAQ,EAAC,CAAC,EAAG,EAAO,CACpE,IAAK,EACL,KAAM,CACR,IACF,kNCHA,GAAM,CAAE,MAAA,CAAK,CAAE,UAAA,CAAS,CAAE,KAAA,CAAI,CAAE,CAAG,cAAU,CAEvC,EAAqB,IAEvB,UAAC,eAAa,EACZ,MAAM,2BACN,SAAS,2EACT,MAAO,CACL,UAAC,SAAM,EAAe,KAAK,mBAAU,wCAAzB,WAGb,UAED,UAAC,OAAI,MAAO,CAAE,SAAU,KAAM,OAAQ,QAAS,WAC7C,WAAC,SAAK,EAAC,UAAU,WAAW,KAAK,QAAQ,MAAO,CAAE,MAAO,MAAO,YAE9D,WAAC,SAAI,YACH,WAAC,GAAM,MAAO,YACZ,UAAC,GAAa,MAAO,CAAE,YAAa,CAAE,IAAK,8BAG7C,UAAC,YAAU,iNAGX,WAAC,aACC,UAAC,GAAK,MAAM,aAAC,+CACb,WAAC,gBACC,UAAC,eAAG,2DACJ,UAAC,eAAG,+CACJ,UAAC,eAAG,yCACJ,UAAC,eAAG,kDAMV,WAAC,SAAI,YACH,WAAC,GAAM,MAAO,YACZ,UAAC,SAAY,EAAC,MAAO,CAAE,YAAa,CAAE,IAAK,8BAG7C,WAAC,aACC,UAAC,GAAK,MAAM,aAAC,mCAAY,gLAG3B,WAAC,aACC,UAAC,GAAK,MAAM,aAAC,mCAAY,oIAG3B,WAAC,aACC,UAAC,GAAK,MAAM,aAAC,mCAAY,qNAM7B,WAAC,SAAI,YACH,WAAC,GAAM,MAAO,YACZ,UAAC,SAAe,EAAC,MAAO,CAAE,YAAa,CAAE,IAAK,8BAGhD,WAAC,aACC,UAAC,GAAK,MAAM,aAAC,mCAAY,sJAG3B,WAAC,aACC,UAAC,GAAK,MAAM,aAAC,mCAAY,iLAM7B,WAAC,SAAI,YACH,WAAC,GAAM,MAAO,YACZ,UAAC,SAAsB,EAAC,MAAO,CAAE,YAAa,CAAE,IAAK,8BAGvD,WAAC,aACC,UAAC,GAAK,MAAM,aAAC,kDACb,UAAC,SAAK,2KAGR,UAAC,SAAO,KACR,WAAC,aACC,UAAC,GAAK,MAAM,aAAC,wDACb,UAAC,SAAK,yIAGR,UAAC,SAAO,KACR,WAAC,aACC,UAAC,GAAK,MAAM,aAAC,wDACb,UAAC,SAAK,4JAMV,WAAC,SAAI,YACH,UAAC,GAAM,MAAO,WAAG,6BACjB,UAAC,YAAU,uKAGX,WAAC,aACC,UAAC,GAAK,MAAM,aAAC,+CAAc,wBAC3B,UAAC,SACD,UAAC,GAAK,MAAM,aAAC,mCAAY,yBACzB,UAAC,SACD,UAAC,GAAK,MAAM,aAAC,mCAAY"}