{"level":30,"time":1753801657612,"pid":1956,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] 如果你需要增加 Git 提交消息校验和自动代码格式化, max g precommit 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#precommit-配置生成器\u001b[39m"}
{"level":30,"time":1753801657615,"pid":1956,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753801657616,"pid":1956,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753801657637,"pid":1956,"hostname":"DESKTOP-KLNCNCN","msg":"generate files"}
{"level":30,"time":1753801659515,"pid":1956,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":30,"time":1753801682311,"pid":19740,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] HMR=none max dev 可以关闭 Umi 开发服务器的模块热替换功能。\u001b[39m"}
{"level":30,"time":1753801682313,"pid":19740,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753801682314,"pid":19740,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753801682315,"pid":19740,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753801683178,"pid":19740,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":30,"time":1753877058100,"pid":20088,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] max g tsconfig 可一键完成项目的 TypeScript 配置。\u001b[39m"}
{"level":30,"time":1753877058111,"pid":20088,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753877058111,"pid":20088,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753877058112,"pid":20088,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753877059492,"pid":20088,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":30,"time":1753880650095,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] PORT=9000 max dev 可以指定 Umi 开发服务器的端口。\u001b[39m"}
{"level":30,"time":1753880650105,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753880650106,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753880650107,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753880651607,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":50,"time":1753883191023,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"F:\\\\Project\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753883191537,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753883190942,"pid":9572,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] 想快速修改 html 模板、DIY 项目？试试编写项目级插件，详见 https://umijs.org/docs/guides/directory-structure#plugints\u001b[39m"}
{"level":30,"time":1753883190954,"pid":9572,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753883190954,"pid":9572,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753883190955,"pid":9572,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753883193061,"pid":9572,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":50,"time":1753883193122,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 19 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753883193328,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 19 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753884812395,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"F:\\\\Project\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753884812602,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753884812281,"pid":18568,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] 遇到难解的配置问题，试试从 Umi FAQ 中寻找答案，详见 https://umijs.org/docs/introduce/faq\u001b[39m"}
{"level":30,"time":1753884812283,"pid":18568,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753884812283,"pid":18568,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753884812284,"pid":18568,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753884813557,"pid":18568,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":50,"time":1753884813673,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 20 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753884813918,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 18 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753886054970,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] @umijs/max 是蚂蚁内网框架 Bigfish 的对外版本。\u001b[39m"}
{"level":30,"time":1753886054972,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753886054972,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753886054973,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753886055729,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":30,"time":1753886060447,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Memory Usage: 134.78 MB (RSS: 687.61 MB)"}
{"level":32,"time":1753886060497,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build user\\index.html"}
{"level":32,"time":1753886060498,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build user\\login\\index.html"}
{"level":32,"time":1753886060498,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build dashboard\\index.html"}
{"level":32,"time":1753886060499,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build team-management\\index.html"}
{"level":32,"time":1753886060499,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build team\\index.html"}
{"level":32,"time":1753886060500,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build team\\list\\index.html"}
{"level":32,"time":1753886060501,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build team\\detail\\index.html"}
{"level":32,"time":1753886060503,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build personal-center\\index.html"}
{"level":32,"time":1753886060504,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build user-manage\\index.html"}
{"level":32,"time":1753886060505,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build subscription\\index.html"}
{"level":32,"time":1753886060505,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build user\\invitations\\index.html"}
{"level":32,"time":1753886060506,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build help\\index.html"}
{"level":32,"time":1753886060507,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build index.html"}
{"level":32,"time":1753886060508,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build 404.html"}
{"level":30,"time":1753886091665,"pid":15168,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] 如果要支持低版本浏览器，可尝试新出的 legacy 配置项，详见 https://umijs.org/blog/legacy-browser\u001b[39m"}
{"level":30,"time":1753886091667,"pid":15168,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753886091667,"pid":15168,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753886091668,"pid":15168,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753886092327,"pid":15168,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":30,"time":1753888757380,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] @umijs/max 是蚂蚁内网框架 Bigfish 的对外版本。\u001b[39m"}
{"level":30,"time":1753888757382,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753888757382,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753888757382,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753888758038,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":30,"time":1753888761997,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Memory Usage: 134.91 MB (RSS: 706.79 MB)"}
{"level":32,"time":1753888762021,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build user\\index.html"}
{"level":32,"time":1753888762022,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build user\\login\\index.html"}
{"level":32,"time":1753888762023,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build dashboard\\index.html"}
{"level":32,"time":1753888762023,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build team-management\\index.html"}
{"level":32,"time":1753888762024,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build team\\index.html"}
{"level":32,"time":1753888762025,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build team\\list\\index.html"}
{"level":32,"time":1753888762025,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build team\\detail\\index.html"}
{"level":32,"time":1753888762026,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build personal-center\\index.html"}
{"level":32,"time":1753888762028,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build user-manage\\index.html"}
{"level":32,"time":1753888762029,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build subscription\\index.html"}
{"level":32,"time":1753888762030,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build user\\invitations\\index.html"}
{"level":32,"time":1753888762030,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build help\\index.html"}
{"level":32,"time":1753888762031,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build index.html"}
{"level":32,"time":1753888762036,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build 404.html"}
