{"version": 3, "sources": ["src/pages/404.tsx"], "sourcesContent": ["import { history } from '@umijs/max';\nimport { <PERSON><PERSON>, Card, Result } from 'antd';\nimport React from 'react';\n\nconst NoFoundPage: React.FC = () => (\n  <Card variant=\"borderless\">\n    <Result\n      status=\"404\"\n      title=\"404\"\n      subTitle=\"抱歉，您访问的页面不存在。\"\n      extra={\n        <Button type=\"primary\" onClick={() => history.push('/')}>\n          返回首页\n        </Button>\n      }\n    />\n  </Card>\n);\n\nexport default NoFoundPage;\n"], "names": [], "mappings": "qQAmBA,+CAAA,8CAnBwB,2EAmBxB,EAf8B,IAC5B,UAAC,SAAI,EAAC,QAAQ,sBACZ,UAAC,SAAM,EACL,OAAO,MACP,MAAM,MACN,SAAS,iFACT,MACE,UAAC,SAAM,EAAC,KAAK,UAAU,QAAS,IAAM,SAAO,CAAC,IAAI,CAAC,cAAM"}