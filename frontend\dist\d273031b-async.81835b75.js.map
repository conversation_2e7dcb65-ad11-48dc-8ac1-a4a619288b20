{"version": 3, "sources": ["node_modules/@umijs/route-utils/es/sha265.js", "node_modules/@ant-design/pro-utils/es/useMediaQuery/query.js", "node_modules/@ant-design/pro-utils/es/useMediaQuery/index.js", "node_modules/@umijs/route-utils/es/transformRoute/transformRoute.js", "node_modules/@umijs/route-utils/es/getFlatMenus/getFlatMenus.js", "node_modules/@umijs/route-utils/es/getMatchMenu/getMatchMenu.js", "node_modules/@ant-design/pro-utils/es/components/ErrorBoundary/index.js", "node_modules/@ant-design/pro-layout/es/WrapContent.js", "node_modules/@ant-design/pro-layout/es/assert/Logo.js", "node_modules/@ant-design/pro-layout/es/utils/utils.js", "node_modules/@ant-design/pro-layout/es/components/SiderMenu/Arrow.js", "node_modules/@ant-design/pro-layout/es/components/CollapsedIcon/style.js", "node_modules/@ant-design/pro-layout/es/components/CollapsedIcon/index.js", "node_modules/@ant-design/icons/es/components/Icon.js", "node_modules/@ant-design/icons/es/components/IconFont.js", "node_modules/@ant-design/pro-layout/es/defaultSettings.js", "node_modules/@ant-design/pro-layout/es/components/SiderMenu/style/menu.js", "node_modules/@ant-design/pro-layout/es/components/SiderMenu/BaseMenu.js", "node_modules/@ant-design/pro-utils/es/isImg/index.js", "node_modules/@ant-design/pro-layout/es/components/SiderMenu/SiderMenu.js", "node_modules/@ant-design/pro-layout/es/components/SiderMenu/style/stylish.js", "node_modules/@ant-design/pro-utils/es/hooks/useRefFunction/index.js", "node_modules/@ant-design/pro-layout/es/components/GlobalHeader/rightContentStyle.js", "node_modules/@ant-design/pro-layout/es/components/GlobalHeader/ActionsContent.js", "node_modules/@ant-design/pro-utils/es/hooks/useDebounceFn/index.js", "node_modules/@ant-design/pro-layout/es/components/TopNavHeader/style.js", "node_modules/@ant-design/pro-layout/es/components/TopNavHeader/index.js", "node_modules/@ant-design/pro-layout/es/components/GlobalHeader/style.js", "node_modules/@ant-design/pro-layout/es/components/GlobalHeader/index.js", "node_modules/@ant-design/pro-layout/es/components/Header/style/header.js", "node_modules/@ant-design/pro-layout/es/components/Header/index.js", "node_modules/@ant-design/pro-layout/es/components/Header/style/stylish.js", "node_modules/rc-drawer/es/DrawerPanel.js", "node_modules/rc-drawer/es/util.js", "node_modules/rc-drawer/es/DrawerPopup.js", "node_modules/rc-drawer/es/Drawer.js", "node_modules/antd/es/drawer/DrawerPanel.js", "node_modules/antd/es/drawer/style/motion.js", "node_modules/antd/es/drawer/style/index.js", "node_modules/antd/es/drawer/index.js", "node_modules/@ant-design/pro-layout/es/components/SiderMenu/style/index.js", "node_modules/@ant-design/pro-layout/es/components/SiderMenu/index.js", "node_modules/@ant-design/pro-layout/es/getPageTitle.js", "node_modules/@ant-design/pro-layout/es/locales/en-US.js", "node_modules/@ant-design/pro-layout/es/locales/en-US/settingDrawer.js", "node_modules/@ant-design/pro-layout/es/locales/it-IT.js", "node_modules/@ant-design/pro-layout/es/locales/it-IT/settingDrawer.js", "node_modules/@ant-design/pro-layout/es/locales/ko-KR.js", "node_modules/@ant-design/pro-layout/es/locales/ko-KR/settingDrawer.js", "node_modules/@ant-design/pro-layout/es/locales/index.js", "node_modules/@ant-design/pro-layout/es/locales/zh-CN.js", "node_modules/@ant-design/pro-layout/es/locales/zh-CN/settingDrawer.js", "node_modules/@ant-design/pro-layout/es/locales/zh-TW.js", "node_modules/@ant-design/pro-layout/es/locales/zh-TW/settingDrawer.js", "node_modules/@ant-design/pro-layout/es/style/index.js", "node_modules/@ant-design/pro-layout/es/utils/getBreadcrumbProps.js", "node_modules/@ant-design/pro-layout/es/utils/pathTools.js", "node_modules/@ant-design/pro-layout/es/utils/getMenuData.js", "node_modules/@ant-design/pro-layout/es/utils/useCurrentMenuLayoutProps.js", "node_modules/@ant-design/pro-layout/es/ProLayout.js", "node_modules/@ant-design/pro-utils/es/hooks/useDocumentTitle/index.js", "src/.umi-production/plugin-layout/Logo.tsx", "src/.umi-production/plugin-layout/Exception.tsx", "node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/LogoutOutlined.js", "src/.umi-production/plugin-layout/Layout.tsx", "src/.umi-production/plugin-layout/rightRender.tsx"], "sourcesContent": ["/* eslint-disable no-redeclare */\n\n/* eslint-disable no-multi-assign */\n\n/* eslint-disable no-param-reassign */\n\n/* eslint-disable no-return-assign */\n\n/* eslint-disable no-new-wrappers */\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n\n/* eslint-disable no-var */\n\n/* eslint-disable no-plusplus */\n\n/* eslint-disable prefer-destructuring */\n\n/* eslint-disable @typescript-eslint/naming-convention */\n\n/* eslint-disable block-scoped-var */\n\n/* eslint-disable vars-on-top */\n\n/* eslint-disable no-bitwise */\n\n/* eslint-disable no-cond-assign */\n\n/*\n * A JavaScript implementation of the SHA256 hash function.\n *\n * FILE:\tsha256.js\n * VERSION:\t0.8\n * AUTHOR:\t<PERSON> <<EMAIL>>\n *\n * NOTE: This version is not tested thoroughly!\n *\n * Copyright (c) 2003, <PERSON>\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions\n * are met:\n * 1. Redistributions of source code must retain the above copyright\n *    notice, this list of conditions and the following disclaimer.\n * 2. Redistributions in binary form must reproduce the above copyright\n *    notice, this list of conditions and the following disclaimer in the\n *    documentation and/or other materials provided with the distribution.\n * 3. Neither the name of the copyright holder nor the names of contributors\n *    may be used to endorse or promote products derived from this software\n *    without specific prior written permission.\n *\n * ======================================================================\n *\n * THIS SOFTWARE IS PROVIDED BY THE AUTHORS ''AS IS'' AND ANY EXPRESS\n * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHORS OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR\n * BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE\n * OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,\n * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\n/* SHA256 logical functions */\nfunction rotateRight(n, x) {\n  return x >>> n | x << 32 - n;\n}\n\nfunction choice(x, y, z) {\n  return x & y ^ ~x & z;\n}\n\nfunction majority(x, y, z) {\n  return x & y ^ x & z ^ y & z;\n}\n\nfunction sha256_Sigma0(x) {\n  return rotateRight(2, x) ^ rotateRight(13, x) ^ rotateRight(22, x);\n}\n\nfunction sha256_Sigma1(x) {\n  return rotateRight(6, x) ^ rotateRight(11, x) ^ rotateRight(25, x);\n}\n\nfunction sha256_sigma0(x) {\n  return rotateRight(7, x) ^ rotateRight(18, x) ^ x >>> 3;\n}\n\nfunction sha256_sigma1(x) {\n  return rotateRight(17, x) ^ rotateRight(19, x) ^ x >>> 10;\n}\n\nfunction sha256_expand(W, j) {\n  return W[j & 0x0f] += sha256_sigma1(W[j + 14 & 0x0f]) + W[j + 9 & 0x0f] + sha256_sigma0(W[j + 1 & 0x0f]);\n}\n/* Hash constant words K: */\n\n\nvar K256 = [0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5, 0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174, 0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da, 0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967, 0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85, 0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070, 0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3, 0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2];\n/* global arrays */\n\nvar ihash;\nvar count;\nvar buffer;\nvar sha256_hex_digits = '0123456789abcdef';\n/* Add 32-bit integers with 16-bit operations (bug in some JS-interpreters:\noverflow) */\n\nfunction safe_add(x, y) {\n  var lsw = (x & 0xffff) + (y & 0xffff);\n  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n  return msw << 16 | lsw & 0xffff;\n}\n/* Initialise the SHA256 computation */\n\n\nfunction sha256_init() {\n  ihash = new Array(8);\n  count = new Array(2);\n  buffer = new Array(64);\n  count[0] = count[1] = 0;\n  ihash[0] = 0x6a09e667;\n  ihash[1] = 0xbb67ae85;\n  ihash[2] = 0x3c6ef372;\n  ihash[3] = 0xa54ff53a;\n  ihash[4] = 0x510e527f;\n  ihash[5] = 0x9b05688c;\n  ihash[6] = 0x1f83d9ab;\n  ihash[7] = 0x5be0cd19;\n}\n/* Transform a 512-bit message block */\n\n\nfunction sha256_transform() {\n  var a;\n  var b;\n  var c;\n  var d;\n  var e;\n  var f;\n  var g;\n  var h;\n  var T1;\n  var T2;\n  var W = new Array(16);\n  /* Initialize registers with the previous intermediate value */\n\n  a = ihash[0];\n  b = ihash[1];\n  c = ihash[2];\n  d = ihash[3];\n  e = ihash[4];\n  f = ihash[5];\n  g = ihash[6];\n  h = ihash[7];\n  /* make 32-bit words */\n\n  for (var i = 0; i < 16; i++) {\n    W[i] = buffer[(i << 2) + 3] | buffer[(i << 2) + 2] << 8 | buffer[(i << 2) + 1] << 16 | buffer[i << 2] << 24;\n  }\n\n  for (var j = 0; j < 64; j++) {\n    T1 = h + sha256_Sigma1(e) + choice(e, f, g) + K256[j];\n    if (j < 16) T1 += W[j];else T1 += sha256_expand(W, j);\n    T2 = sha256_Sigma0(a) + majority(a, b, c);\n    h = g;\n    g = f;\n    f = e;\n    e = safe_add(d, T1);\n    d = c;\n    c = b;\n    b = a;\n    a = safe_add(T1, T2);\n  }\n  /* Compute the current intermediate hash value */\n\n\n  ihash[0] += a;\n  ihash[1] += b;\n  ihash[2] += c;\n  ihash[3] += d;\n  ihash[4] += e;\n  ihash[5] += f;\n  ihash[6] += g;\n  ihash[7] += h;\n}\n/* Read the next chunk of data and update the SHA256 computation */\n\n\nfunction sha256_update(data, inputLen) {\n  var i;\n  var index;\n  var curpos = 0;\n  /* Compute number of bytes mod 64 */\n\n  index = count[0] >> 3 & 0x3f;\n  var remainder = inputLen & 0x3f;\n  /* Update number of bits */\n\n  if ((count[0] += inputLen << 3) < inputLen << 3) count[1]++;\n  count[1] += inputLen >> 29;\n  /* Transform as many times as possible */\n\n  for (i = 0; i + 63 < inputLen; i += 64) {\n    for (var j = index; j < 64; j++) {\n      buffer[j] = data.charCodeAt(curpos++);\n    }\n\n    sha256_transform();\n    index = 0;\n  }\n  /* Buffer remaining input */\n\n\n  for (var _j = 0; _j < remainder; _j++) {\n    buffer[_j] = data.charCodeAt(curpos++);\n  }\n}\n/* Finish the computation by operations such as padding */\n\n\nfunction sha256_final() {\n  var index = count[0] >> 3 & 0x3f;\n  buffer[index++] = 0x80;\n\n  if (index <= 56) {\n    for (var i = index; i < 56; i++) {\n      buffer[i] = 0;\n    }\n  } else {\n    for (var _i = index; _i < 64; _i++) {\n      buffer[_i] = 0;\n    }\n\n    sha256_transform();\n\n    for (var _i2 = 0; _i2 < 56; _i2++) {\n      buffer[_i2] = 0;\n    }\n  }\n\n  buffer[56] = count[1] >>> 24 & 0xff;\n  buffer[57] = count[1] >>> 16 & 0xff;\n  buffer[58] = count[1] >>> 8 & 0xff;\n  buffer[59] = count[1] & 0xff;\n  buffer[60] = count[0] >>> 24 & 0xff;\n  buffer[61] = count[0] >>> 16 & 0xff;\n  buffer[62] = count[0] >>> 8 & 0xff;\n  buffer[63] = count[0] & 0xff;\n  sha256_transform();\n}\n/* Split the internal hash values into an array of bytes */\n\n\nfunction sha256_encode_bytes() {\n  var j = 0;\n  var output = new Array(32);\n\n  for (var i = 0; i < 8; i++) {\n    output[j++] = ihash[i] >>> 24 & 0xff;\n    output[j++] = ihash[i] >>> 16 & 0xff;\n    output[j++] = ihash[i] >>> 8 & 0xff;\n    output[j++] = ihash[i] & 0xff;\n  }\n\n  return output;\n}\n/* Get the internal hash as a hex string */\n\n\nfunction sha256_encode_hex() {\n  var output = new String();\n\n  for (var i = 0; i < 8; i++) {\n    for (var j = 28; j >= 0; j -= 4) {\n      output += sha256_hex_digits.charAt(ihash[i] >>> j & 0x0f);\n    }\n  }\n\n  return output;\n}\n/* Main function: returns a hex string representing the SHA256 value of the\ngiven data */\n\n\nfunction digest(data) {\n  sha256_init();\n  sha256_update(data, data.length);\n  sha256_final();\n  return sha256_encode_hex();\n}\n\nexport default digest;", "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useLayoutEffect, useState } from 'react';\nexport default function useMediaQuery(mediaQuery) {\n  var isSsr = typeof window === 'undefined';\n  var _useState = useState(function () {\n      return isSsr ? false : window.matchMedia(mediaQuery).matches;\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    matches = _useState2[0],\n    setMatches = _useState2[1];\n  useLayoutEffect(function () {\n    if (isSsr) {\n      return;\n    }\n    var mediaQueryList = window.matchMedia(mediaQuery);\n    var listener = function listener(e) {\n      return setMatches(e.matches);\n    };\n    mediaQueryList.addListener(listener);\n    return function () {\n      return mediaQueryList.removeListener(listener);\n    };\n  }, [mediaQuery]);\n  return matches;\n}", "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEffect, useState } from 'react';\nimport useMediaQuery from \"./query\";\nexport var MediaQueryEnum = {\n  xs: {\n    maxWidth: 575,\n    matchMedia: '(max-width: 575px)'\n  },\n  sm: {\n    minWidth: 576,\n    maxWidth: 767,\n    matchMedia: '(min-width: 576px) and (max-width: 767px)'\n  },\n  md: {\n    minWidth: 768,\n    maxWidth: 991,\n    matchMedia: '(min-width: 768px) and (max-width: 991px)'\n  },\n  lg: {\n    minWidth: 992,\n    maxWidth: 1199,\n    matchMedia: '(min-width: 992px) and (max-width: 1199px)'\n  },\n  xl: {\n    minWidth: 1200,\n    maxWidth: 1599,\n    matchMedia: '(min-width: 1200px) and (max-width: 1599px)'\n  },\n  xxl: {\n    minWidth: 1600,\n    matchMedia: '(min-width: 1600px)'\n  }\n};\n/**\n * loop query screen className\n * Array.find will throw a error\n * `Rendered more hooks than during the previous render.`\n * So should use Array.forEach\n */\nexport var getScreenClassName = function getScreenClassName() {\n  var queryKey = undefined;\n  // support ssr\n  if (typeof window === 'undefined') {\n    return queryKey;\n  }\n  var mediaQueryKey = Object.keys(MediaQueryEnum).find(function (key) {\n    var matchMedia = MediaQueryEnum[key].matchMedia;\n    if (window.matchMedia(matchMedia).matches) {\n      return true;\n    }\n    return false;\n  });\n  queryKey = mediaQueryKey;\n  return queryKey;\n};\nvar useBreakpoint = function useBreakpoint() {\n  var isMd = useMediaQuery(MediaQueryEnum.md.matchMedia);\n  var isLg = useMediaQuery(MediaQueryEnum.lg.matchMedia);\n  var isXxl = useMediaQuery(MediaQueryEnum.xxl.matchMedia);\n  var isXl = useMediaQuery(MediaQueryEnum.xl.matchMedia);\n  var isSm = useMediaQuery(MediaQueryEnum.sm.matchMedia);\n  var isXs = useMediaQuery(MediaQueryEnum.xs.matchMedia);\n  var _useState = useState(getScreenClassName()),\n    _useState2 = _slicedToArray(_useState, 2),\n    colSpan = _useState2[0],\n    setColSpan = _useState2[1];\n  useEffect(function () {\n    if (process.env.NODE_ENV === 'TEST') {\n      setColSpan(process.env.USE_MEDIA || 'md');\n      return;\n    }\n    if (isXxl) {\n      setColSpan('xxl');\n      return;\n    }\n    if (isXl) {\n      setColSpan('xl');\n      return;\n    }\n    if (isLg) {\n      setColSpan('lg');\n      return;\n    }\n    if (isMd) {\n      setColSpan('md');\n      return;\n    }\n    if (isSm) {\n      setColSpan('sm');\n      return;\n    }\n    if (isXs) {\n      setColSpan('xs');\n      return;\n    }\n    setColSpan('md');\n  }, [isMd, isLg, isXxl, isXl, isSm, isXs]);\n  return colSpan;\n};\nexport { useBreakpoint };", "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nvar _excluded = [\"pro_layout_parentKeys\", \"children\", \"icon\", \"flatMenu\", \"indexRoute\", \"routes\"];\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e2) { throw _e2; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e3) { didErr = true; err = _e3; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nfunction _wrapNativeSuper(Class) { var _cache = typeof Map === \"function\" ? new Map() : undefined; _wrapNativeSuper = function _wrapNativeSuper(Class) { if (Class === null || !_isNativeFunction(Class)) return Class; if (typeof Class !== \"function\") { throw new TypeError(\"Super expression must either be null or a function\"); } if (typeof _cache !== \"undefined\") { if (_cache.has(Class)) return _cache.get(Class); _cache.set(Class, Wrapper); } function Wrapper() { return _construct(Class, arguments, _getPrototypeOf(this).constructor); } Wrapper.prototype = Object.create(Class.prototype, { constructor: { value: Wrapper, enumerable: false, writable: true, configurable: true } }); return _setPrototypeOf(Wrapper, Class); }; return _wrapNativeSuper(Class); }\n\nfunction _construct(Parent, args, Class) { if (_isNativeReflectConstruct()) { _construct = Reflect.construct.bind(); } else { _construct = function _construct(Parent, args, Class) { var a = [null]; a.push.apply(a, args); var Constructor = Function.bind.apply(Parent, a); var instance = new Constructor(); if (Class) _setPrototypeOf(instance, Class.prototype); return instance; }; } return _construct.apply(null, arguments); }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _isNativeFunction(fn) { return Function.toString.call(fn).indexOf(\"[native code]\") !== -1; }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//@ts-ignore\nimport { pathToRegexp } from '../path-to-regexp';\nimport sha265 from '../sha265';\nexport var childrenPropsName = 'routes';\nexport function stripQueryStringAndHashFromPath(url) {\n  return url.split('?')[0].split('#')[0];\n}\nexport var isUrl = function isUrl(path) {\n  if (!path.startsWith('http')) {\n    return false;\n  }\n\n  try {\n    var url = new URL(path);\n    return !!url;\n  } catch (error) {\n    return false;\n  }\n};\nexport var getKeyByPath = function getKeyByPath(item) {\n  var path = item.path;\n\n  if (!path || path === '/') {\n    // 如果还是没有，用对象的hash 生成一个\n    try {\n      return \"/\".concat(sha265(JSON.stringify(item)));\n    } catch (error) {// dom some thing\n    }\n  }\n\n  return path ? stripQueryStringAndHashFromPath(path) : path;\n};\n/**\n * 获取locale，增加了一个功能，如果 locale = false，将不使用国际化\n * @param item\n * @param parentName\n */\n\nvar getItemLocaleName = function getItemLocaleName(item, parentName) {\n  var name = item.name,\n      locale = item.locale; // 如果配置了 locale 并且 locale 为 false或 \"\"\n\n  if ('locale' in item && locale === false || !name) {\n    return false;\n  }\n\n  return item.locale || \"\".concat(parentName, \".\").concat(name);\n};\n/**\n * 如果不是 / 开头的和父节点做一下合并\n * 如果是 / 开头的不作任何处理\n * 如果是 url 也直接返回\n * @param path\n * @param parentPath\n */\n\n\nvar mergePath = function mergePath() {\n  var path = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  var parentPath = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '/';\n\n  if (path.endsWith('/*')) {\n    return path.replace('/*', '/');\n  }\n\n  if ((path || parentPath).startsWith('/')) {\n    return path;\n  }\n\n  if (isUrl(path)) {\n    return path;\n  }\n\n  return \"/\".concat(parentPath, \"/\").concat(path).replace(/\\/\\//g, '/').replace(/\\/\\//g, '/');\n}; // bigfish 的兼容准话\n\n\nvar bigfishCompatibleConversions = function bigfishCompatibleConversions(route, props) {\n  var _route$menu = route.menu,\n      menu = _route$menu === void 0 ? {} : _route$menu,\n      indexRoute = route.indexRoute,\n      _route$path = route.path,\n      path = _route$path === void 0 ? '' : _route$path;\n  var routerChildren = route.children || [];\n  var _menu$name = menu.name,\n      name = _menu$name === void 0 ? route.name : _menu$name,\n      _menu$icon = menu.icon,\n      icon = _menu$icon === void 0 ? route.icon : _menu$icon,\n      _menu$hideChildren = menu.hideChildren,\n      hideChildren = _menu$hideChildren === void 0 ? route.hideChildren : _menu$hideChildren,\n      _menu$flatMenu = menu.flatMenu,\n      flatMenu = _menu$flatMenu === void 0 ? route.flatMenu : _menu$flatMenu; // 兼容平铺式写法\n  // 拼接 childrenRoutes, 处理存在 indexRoute 时的逻辑\n\n  var childrenList = indexRoute && // 如果只有 redirect,不用处理的\n  Object.keys(indexRoute).join(',') !== 'redirect' ? [_objectSpread({\n    path: path,\n    menu: menu\n  }, indexRoute)].concat(routerChildren || []) : routerChildren; // 拼接返回的 menu 数据\n\n  var result = _objectSpread({}, route);\n\n  if (name) {\n    result.name = name;\n  }\n\n  if (icon) {\n    result.icon = icon;\n  }\n\n  if (childrenList && childrenList.length) {\n    /** 在菜单中隐藏子项 */\n    if (hideChildren) {\n      delete result.children;\n      return result;\n    } // 需要重新进行一次\n\n\n    var finalChildren = formatter(_objectSpread(_objectSpread({}, props), {}, {\n      data: childrenList\n    }), route);\n    /** 在菜单中只隐藏此项，子项往上提，仍旧展示 */\n\n    if (flatMenu) {\n      return finalChildren;\n    }\n\n    delete result[childrenPropsName];\n  }\n\n  return result;\n};\n\nvar notNullArray = function notNullArray(value) {\n  return Array.isArray(value) && value.length > 0;\n};\n/**\n *\n * @param props\n * @param parent\n */\n\n\nfunction formatter(props) {\n  var parent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    path: '/'\n  };\n  var data = props.data,\n      formatMessage = props.formatMessage,\n      parentName = props.parentName,\n      menuLocale = props.locale;\n\n  if (!data || !Array.isArray(data)) {\n    return [];\n  }\n\n  return data.filter(function (item) {\n    if (!item) return false;\n    if (notNullArray(item.children)) return true;\n    if (item.path) return true;\n    if (item.originPath) return true;\n    if (item.layout) return true; // 重定向\n\n    if (item.redirect) return false;\n    if (item.unaccessible) return false;\n    return false;\n  }).filter(function (item) {\n    var _item$menu, _item$menu2;\n\n    if ((item === null || item === void 0 ? void 0 : (_item$menu = item.menu) === null || _item$menu === void 0 ? void 0 : _item$menu.name) || (item === null || item === void 0 ? void 0 : item.flatMenu) || (item === null || item === void 0 ? void 0 : (_item$menu2 = item.menu) === null || _item$menu2 === void 0 ? void 0 : _item$menu2.flatMenu)) {\n      return true;\n    } // 显示指定在 menu 中隐藏该项\n    // layout 插件的功能，其实不应该存在的\n\n\n    if (item.menu === false) {\n      return false;\n    }\n\n    return true;\n  }).map(function (finallyItem) {\n    var item = _objectSpread(_objectSpread({}, finallyItem), {}, {\n      path: finallyItem.path || finallyItem.originPath\n    });\n\n    if (!item.children && item[childrenPropsName]) {\n      item.children = item[childrenPropsName];\n      delete item[childrenPropsName];\n    } // 是否没有权限查看\n    // 这样就不会显示，是一个兼容性的方式\n\n\n    if (item.unaccessible) {\n      // eslint-disable-next-line no-param-reassign\n      delete item.name;\n    }\n\n    if (item.path === '*') {\n      item.path = '.';\n    }\n\n    if (item.path === '/*') {\n      item.path = '.';\n    }\n\n    if (!item.path && item.originPath) {\n      item.path = item.originPath;\n    }\n\n    return item;\n  }).map(function () {\n    var item = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      path: '/'\n    };\n    var routerChildren = item.children || item[childrenPropsName] || [];\n    var path = mergePath(item.path, parent ? parent.path : '/');\n    var name = item.name;\n    var locale = getItemLocaleName(item, parentName || 'menu'); // if enableMenuLocale use item.name,\n    // close menu international\n\n    var localeName = locale !== false && menuLocale !== false && formatMessage && locale ? formatMessage({\n      id: locale,\n      defaultMessage: name\n    }) : name;\n\n    var _parent$pro_layout_pa = parent.pro_layout_parentKeys,\n        pro_layout_parentKeys = _parent$pro_layout_pa === void 0 ? [] : _parent$pro_layout_pa,\n        children = parent.children,\n        icon = parent.icon,\n        flatMenu = parent.flatMenu,\n        indexRoute = parent.indexRoute,\n        routes = parent.routes,\n        restParent = _objectWithoutProperties(parent, _excluded);\n\n    var item_pro_layout_parentKeys = new Set([].concat(_toConsumableArray(pro_layout_parentKeys), _toConsumableArray(item.parentKeys || [])));\n\n    if (parent.key) {\n      item_pro_layout_parentKeys.add(parent.key);\n    }\n\n    var finallyItem = _objectSpread(_objectSpread(_objectSpread({}, restParent), {}, {\n      menu: undefined\n    }, item), {}, {\n      path: path,\n      locale: locale,\n      key: item.key || getKeyByPath(_objectSpread(_objectSpread({}, item), {}, {\n        path: path\n      })),\n      pro_layout_parentKeys: Array.from(item_pro_layout_parentKeys).filter(function (key) {\n        return key && key !== '/';\n      })\n    });\n\n    if (localeName) {\n      finallyItem.name = localeName;\n    } else {\n      delete finallyItem.name;\n    }\n\n    if (finallyItem.menu === undefined) {\n      delete finallyItem.menu;\n    }\n\n    if (notNullArray(routerChildren)) {\n      var formatterChildren = formatter(_objectSpread(_objectSpread({}, props), {}, {\n        data: routerChildren,\n        parentName: locale || ''\n      }), finallyItem);\n\n      if (notNullArray(formatterChildren)) {\n        finallyItem.children = formatterChildren;\n      }\n    }\n\n    return bigfishCompatibleConversions(finallyItem, props);\n  }).flat(1);\n}\n/**\n * 删除 hideInMenu 和 item.name 不存在的\n */\n\n\nvar defaultFilterMenuData = function defaultFilterMenuData() {\n  var menuData = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  return menuData.filter(function (item) {\n    return item && (item.name || notNullArray(item.children)) && !item.hideInMenu && !item.redirect;\n  }).map(function (item) {\n    var newItem = _objectSpread({}, item);\n\n    var routerChildren = newItem.children || item[childrenPropsName] || [];\n    delete newItem[childrenPropsName];\n\n    if (notNullArray(routerChildren) && !newItem.hideChildrenInMenu && routerChildren.some(function (child) {\n      return child && !!child.name;\n    })) {\n      var newChildren = defaultFilterMenuData(routerChildren);\n      if (newChildren.length) return _objectSpread(_objectSpread({}, newItem), {}, {\n        children: newChildren\n      });\n    }\n\n    return _objectSpread({}, item);\n  }).filter(function (item) {\n    return item;\n  });\n};\n/**\n * support pathToRegexp get string\n */\n\n\nvar RouteListMap = /*#__PURE__*/function (_Map) {\n  _inherits(RouteListMap, _Map);\n\n  var _super = _createSuper(RouteListMap);\n\n  function RouteListMap() {\n    _classCallCheck(this, RouteListMap);\n\n    return _super.apply(this, arguments);\n  }\n\n  _createClass(RouteListMap, [{\n    key: \"get\",\n    value: function get(pathname) {\n      var routeValue;\n\n      try {\n        // eslint-disable-next-line no-restricted-syntax\n        var _iterator = _createForOfIteratorHelper(this.entries()),\n            _step;\n\n        try {\n          for (_iterator.s(); !(_step = _iterator.n()).done;) {\n            var _step$value = _slicedToArray(_step.value, 2),\n                key = _step$value[0],\n                value = _step$value[1];\n\n            var path = stripQueryStringAndHashFromPath(key);\n\n            if (!isUrl(key) && pathToRegexp(path, []).test(pathname)) {\n              routeValue = value;\n              break;\n            }\n          }\n        } catch (err) {\n          _iterator.e(err);\n        } finally {\n          _iterator.f();\n        }\n      } catch (error) {\n        routeValue = undefined;\n      }\n\n      return routeValue;\n    }\n  }]);\n\n  return RouteListMap;\n}( /*#__PURE__*/_wrapNativeSuper(Map));\n/**\n * 获取面包屑映射\n * @param MenuDataItem[] menuData 菜单配置\n */\n\n\nvar getBreadcrumbNameMap = function getBreadcrumbNameMap(menuData) {\n  // Map is used to ensure the order of keys\n  var routerMap = new RouteListMap();\n\n  var flattenMenuData = function flattenMenuData(data, parent) {\n    data.forEach(function (menuItem) {\n      var routerChildren = menuItem.children || menuItem[childrenPropsName] || [];\n\n      if (notNullArray(routerChildren)) {\n        flattenMenuData(routerChildren, menuItem);\n      } // Reduce memory usage\n\n\n      var path = mergePath(menuItem.path, parent ? parent.path : '/');\n      routerMap.set(stripQueryStringAndHashFromPath(path), menuItem);\n    });\n  };\n\n  flattenMenuData(menuData);\n  return routerMap;\n};\n\nvar clearChildren = function clearChildren() {\n  var menuData = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  return menuData.map(function (item) {\n    var routerChildren = item.children || item[childrenPropsName];\n\n    if (notNullArray(routerChildren)) {\n      var newChildren = clearChildren(routerChildren);\n      if (newChildren.length) return _objectSpread({}, item);\n    }\n\n    var finallyItem = _objectSpread({}, item);\n\n    delete finallyItem[childrenPropsName];\n    delete finallyItem.children;\n    return finallyItem;\n  }).filter(function (item) {\n    return item;\n  });\n};\n/**\n * @param routeList 路由配置\n * @param locale 是否使用国际化\n * @param formatMessage 国际化的程序\n * @param ignoreFilter 是否筛选掉不展示的 menuItem 项，plugin-layout需要所有项目来计算布局样式\n * @returns { breadcrumb, menuData}\n */\n\n\nvar transformRoute = function transformRoute(routeList, locale, formatMessage, ignoreFilter) {\n  var originalMenuData = formatter({\n    data: routeList,\n    formatMessage: formatMessage,\n    locale: locale\n  });\n  var menuData = ignoreFilter ? clearChildren(originalMenuData) : defaultFilterMenuData(originalMenuData); // Map type used for internal logic\n\n  var breadcrumb = getBreadcrumbNameMap(originalMenuData);\n  return {\n    breadcrumb: breadcrumb,\n    menuData: menuData\n  };\n};\n\nexport default transformRoute;", "function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { stripQueryStringAndHashFromPath, childrenPropsName } from '../transformRoute/transformRoute';\n/**\n * 获取打平的 menuData\n * 以 path 为 key\n * @param menuData\n */\n\nexport var getFlatMenus = function getFlatMenus() {\n  var menuData = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var menus = {};\n  menuData.forEach(function (mapItem) {\n    var item = _objectSpread({}, mapItem);\n\n    if (!item || !item.key) {\n      return;\n    }\n\n    if (!item.children && item[childrenPropsName]) {\n      item.children = item[childrenPropsName];\n      delete item[childrenPropsName];\n    }\n\n    var routerChildren = item.children || [];\n    menus[stripQueryStringAndHashFromPath(item.path || item.key || '/')] = _objectSpread({}, item);\n    menus[item.key || item.path || '/'] = _objectSpread({}, item);\n\n    if (routerChildren) {\n      menus = _objectSpread(_objectSpread({}, menus), getFlatMenus(routerChildren));\n    }\n  });\n  return menus;\n};\nexport default getFlatMenus;", "//@ts-ignore\nimport { pathToRegexp } from '../path-to-regexp';\nimport getFlatMenu from '../getFlatMenus/getFlatMenus';\nimport { isUrl, stripQueryStringAndHashFromPath } from '../transformRoute/transformRoute';\nexport var getMenuMatches = function getMenuMatches() {\n  var flatMenuKeys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var path = arguments.length > 1 ? arguments[1] : undefined;\n  var exact = arguments.length > 2 ? arguments[2] : undefined;\n  return flatMenuKeys.filter(function (item) {\n    if (item === '/' && path === '/') {\n      return true;\n    }\n\n    if (item !== '/' && item !== '/*' && item && !isUrl(item)) {\n      var pathKey = stripQueryStringAndHashFromPath(item);\n\n      try {\n        // exact\n        if (exact) {\n          if (pathToRegexp(\"\".concat(pathKey)).test(path)) {\n            return true;\n          }\n        } // /a\n\n\n        if (pathToRegexp(\"\".concat(pathKey), []).test(path)) {\n          return true;\n        } // /a/b/b\n\n\n        if (pathToRegexp(\"\".concat(pathKey, \"/(.*)\")).test(path)) {\n          return true;\n        }\n      } catch (error) {// console.log(error, path);\n      }\n    }\n\n    return false;\n  }).sort(function (a, b) {\n    // 如果完全匹配放到最后面\n    if (a === path) {\n      return 10;\n    }\n\n    if (b === path) {\n      return -10;\n    }\n\n    return a.substr(1).split('/').length - b.substr(1).split('/').length;\n  });\n};\n/**\n * 获取当前的选中菜单列表\n * @param pathname\n * @param menuData\n * @returns MenuDataItem[]\n */\n\nexport var getMatchMenu = function getMatchMenu(pathname, menuData,\n/**\n * 要不要展示全部的 key\n */\nfullKeys, exact) {\n  var flatMenus = getFlatMenu(menuData);\n  var flatMenuKeys = Object.keys(flatMenus);\n  var menuPathKeys = getMenuMatches(flatMenuKeys, pathname || '/', exact);\n\n  if (!menuPathKeys || menuPathKeys.length < 1) {\n    return [];\n  }\n\n  if (!fullKeys) {\n    menuPathKeys = [menuPathKeys[menuPathKeys.length - 1]];\n  }\n\n  return menuPathKeys.map(function (menuPathKey) {\n    var menuItem = flatMenus[menuPathKey] || {\n      pro_layout_parentKeys: '',\n      key: ''\n    }; // 去重\n\n    var map = new Map();\n    var parentItems = (menuItem.pro_layout_parentKeys || []).map(function (key) {\n      if (map.has(key)) {\n        return null;\n      }\n\n      map.set(key, true);\n      return flatMenus[key];\n    }).filter(function (item) {\n      return item;\n    });\n\n    if (menuItem.key) {\n      parentItems.push(menuItem);\n    }\n\n    return parentItems;\n  }).flat(1);\n};\nexport default getMatchMenu;", "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Result } from 'antd';\nimport React from 'react';\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar ErrorBoundary = /*#__PURE__*/function (_React$Component) {\n  _inherits(ErrorBoundary, _React$Component);\n  var _super = _createSuper(ErrorBoundary);\n  function ErrorBoundary() {\n    var _this;\n    _classCallCheck(this, ErrorBoundary);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      hasError: false,\n      errorInfo: ''\n    });\n    return _this;\n  }\n  _createClass(ErrorBoundary, [{\n    key: \"componentDidCatch\",\n    value: function componentDidCatch(error, errorInfo) {\n      // You can also log the error to an error reporting service\n      // eslint-disable-next-line no-console\n      console.log(error, errorInfo);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (this.state.hasError) {\n        // You can render any custom fallback UI\n        return /*#__PURE__*/_jsx(Result, {\n          status: \"error\",\n          title: \"Something went wrong.\",\n          extra: this.state.errorInfo\n        });\n      }\n      return this.props.children;\n    }\n  }], [{\n    key: \"getDerivedStateFromError\",\n    value: function getDerivedStateFromError(error) {\n      return {\n        hasError: true,\n        errorInfo: error.message\n      };\n    }\n  }]);\n  return ErrorBoundary;\n}(React.Component);\nexport { ErrorBoundary };", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { ProProvider } from '@ant-design/pro-provider';\nimport { ErrorBoundary } from '@ant-design/pro-utils';\nimport { Layout } from 'antd';\nimport classNames from 'classnames';\nimport React, { useContext } from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar WrapContent = function WrapContent(props) {\n  var _useContext = useContext(ProProvider),\n    hashId = _useContext.hashId;\n  var style = props.style,\n    prefixCls = props.prefixCls,\n    children = props.children,\n    _props$hasPageContain = props.hasPageContainer,\n    hasPageContainer = _props$hasPageContain === void 0 ? 0 : _props$hasPageContain;\n  var contentClassName = classNames(\"\".concat(prefixCls, \"-content\"), hashId, _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-has-header\"), props.hasHeader), \"\".concat(prefixCls, \"-content-has-page-container\"), hasPageContainer > 0));\n  var ErrorComponent = props.ErrorBoundary || ErrorBoundary;\n  return props.ErrorBoundary === false ? /*#__PURE__*/_jsx(Layout.Content, {\n    className: contentClassName,\n    style: style,\n    children: children\n  }) : /*#__PURE__*/_jsx(ErrorComponent, {\n    children: /*#__PURE__*/_jsx(Layout.Content, {\n      className: contentClassName,\n      style: style,\n      children: children\n    })\n  });\n};\nexport { WrapContent };", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport var Logo = function Logo() {\n  return /*#__PURE__*/_jsxs(\"svg\", {\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 200 200\",\n    children: [/*#__PURE__*/_jsxs(\"defs\", {\n      children: [/*#__PURE__*/_jsxs(\"linearGradient\", {\n        x1: \"62.1023273%\",\n        y1: \"0%\",\n        x2: \"108.19718%\",\n        y2: \"37.8635764%\",\n        id: \"linearGradient-1\",\n        children: [/*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#4285EB\",\n          offset: \"0%\"\n        }), /*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#2EC7FF\",\n          offset: \"100%\"\n        })]\n      }), /*#__PURE__*/_jsxs(\"linearGradient\", {\n        x1: \"69.644116%\",\n        y1: \"0%\",\n        x2: \"54.0428975%\",\n        y2: \"108.456714%\",\n        id: \"linearGradient-2\",\n        children: [/*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#29CDFF\",\n          offset: \"0%\"\n        }), /*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#148EFF\",\n          offset: \"37.8600687%\"\n        }), /*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#0A60FF\",\n          offset: \"100%\"\n        })]\n      }), /*#__PURE__*/_jsxs(\"linearGradient\", {\n        x1: \"69.6908165%\",\n        y1: \"-12.9743587%\",\n        x2: \"16.7228981%\",\n        y2: \"117.391248%\",\n        id: \"linearGradient-3\",\n        children: [/*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#FA816E\",\n          offset: \"0%\"\n        }), /*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#F74A5C\",\n          offset: \"41.472606%\"\n        }), /*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#F51D2C\",\n          offset: \"100%\"\n        })]\n      }), /*#__PURE__*/_jsxs(\"linearGradient\", {\n        x1: \"68.1279872%\",\n        y1: \"-35.6905737%\",\n        x2: \"30.4400914%\",\n        y2: \"114.942679%\",\n        id: \"linearGradient-4\",\n        children: [/*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#FA8E7D\",\n          offset: \"0%\"\n        }), /*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#F74A5C\",\n          offset: \"51.2635191%\"\n        }), /*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#F51D2C\",\n          offset: \"100%\"\n        })]\n      })]\n    }), /*#__PURE__*/_jsx(\"g\", {\n      stroke: \"none\",\n      strokeWidth: 1,\n      fill: \"none\",\n      fillRule: \"evenodd\",\n      children: /*#__PURE__*/_jsx(\"g\", {\n        transform: \"translate(-20.000000, -20.000000)\",\n        children: /*#__PURE__*/_jsx(\"g\", {\n          transform: \"translate(20.000000, 20.000000)\",\n          children: /*#__PURE__*/_jsxs(\"g\", {\n            children: [/*#__PURE__*/_jsxs(\"g\", {\n              fillRule: \"nonzero\",\n              children: [/*#__PURE__*/_jsxs(\"g\", {\n                children: [/*#__PURE__*/_jsx(\"path\", {\n                  d: \"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C99.2571609,26.9692191 101.032305,26.9692191 102.20193,28.1378823 L129.985225,55.8983314 C134.193707,60.1033528 141.017005,60.1033528 145.225487,55.8983314 C149.433969,51.69331 149.433969,44.8756232 145.225487,40.6706018 L108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z\",\n                  fill: \"url(#linearGradient-1)\"\n                }), /*#__PURE__*/_jsx(\"path\", {\n                  d: \"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C100.999864,25.6271836 105.751642,20.541824 112.729652,19.3524487 C117.915585,18.4685261 123.585219,20.4140239 129.738554,25.1889424 C125.624663,21.0784292 118.571995,14.0340304 108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z\",\n                  fill: \"url(#linearGradient-2)\"\n                })]\n              }), /*#__PURE__*/_jsx(\"path\", {\n                d: \"M153.685633,135.854579 C157.894115,140.0596 164.717412,140.0596 168.925894,135.854579 L195.959977,108.842726 C200.659183,104.147384 200.659183,96.5636133 195.960527,91.8688194 L168.690777,64.7181159 C164.472332,60.5180858 157.646868,60.5241425 153.435895,64.7316526 C149.227413,68.936674 149.227413,75.7543607 153.435895,79.9593821 L171.854035,98.3623765 C173.02366,99.5310396 173.02366,101.304724 171.854035,102.473387 L153.685633,120.626849 C149.47715,124.83187 149.47715,131.649557 153.685633,135.854579 Z\",\n                fill: \"url(#linearGradient-3)\"\n              })]\n            }), /*#__PURE__*/_jsx(\"ellipse\", {\n              fill: \"url(#linearGradient-4)\",\n              cx: \"100.519339\",\n              cy: \"100.436681\",\n              rx: \"23.6001926\",\n              ry: \"23.580786\"\n            })]\n          })\n        })\n      })\n    })]\n  });\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nexport var getOpenKeysFromMenuData = function getOpenKeysFromMenuData(menuData) {\n  return (menuData || []).reduce(function (pre, item) {\n    if (item.key) {\n      pre.push(item.key);\n    }\n    if (item.children || item.routes) {\n      var newArray = pre.concat(getOpenKeysFromMenuData(item.children || item.routes) || []);\n      return newArray;\n    }\n    return pre;\n  }, []);\n};\nvar themeConfig = {\n  techBlue: '#1677FF',\n  daybreak: '#1890ff',\n  dust: '#F5222D',\n  volcano: '#FA541C',\n  sunset: '#FAAD14',\n  cyan: '#13C2C2',\n  green: '#52C41A',\n  geekblue: '#2F54EB',\n  purple: '#722ED1'\n};\n/**\n * Daybreak-> #1890ff\n *\n * @param val\n */\nexport function genStringToTheme(val) {\n  return val && themeConfig[val] ? themeConfig[val] : val || '';\n}\nexport function clearMenuItem(menusData) {\n  return menusData.map(function (item) {\n    var children = item.children || [];\n    var finalItem = _objectSpread({}, item);\n    if (!finalItem.children && finalItem.routes) {\n      finalItem.children = finalItem.routes;\n    }\n    if (!finalItem.name || finalItem.hideInMenu) {\n      return null;\n    }\n    if (finalItem && finalItem !== null && finalItem !== void 0 && finalItem.children) {\n      if (!finalItem.hideChildrenInMenu && children.some(function (child) {\n        return child && child.name && !child.hideInMenu;\n      })) {\n        return _objectSpread(_objectSpread({}, item), {}, {\n          children: clearMenuItem(children)\n        });\n      }\n      // children 为空就直接删掉\n      delete finalItem.children;\n    }\n    delete finalItem.routes;\n    return finalItem;\n  }).filter(function (item) {\n    return item;\n  });\n}", "import { jsx as _jsx } from \"react/jsx-runtime\";\nfunction ArrowSvgIcon() {\n  return /*#__PURE__*/_jsx(\"svg\", {\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 12 12\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      d: \"M6.432 7.967a.448.448 0 01-.318.133h-.228a.46.46 0 01-.318-.133L2.488 4.85a.305.305 0 010-.43l.427-.43a.293.293 0 01.42 0L6 6.687l2.665-2.699a.299.299 0 01.426 0l.42.431a.305.305 0 010 .43L6.432 7.967z\"\n    })\n  });\n}\nexport { ArrowSvgIcon };", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nvar genSiderMenuStyle = function genSiderMenuStyle(token) {\n  var _token$layout, _token$layout2, _token$layout3;\n  return _defineProperty({}, token.componentCls, {\n    position: 'absolute',\n    insetBlockStart: '18px',\n    zIndex: '101',\n    width: '24px',\n    height: '24px',\n    fontSize: ['14px', '16px'],\n    textAlign: 'center',\n    borderRadius: '40px',\n    insetInlineEnd: '-13px',\n    transition: 'transform 0.3s',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    color: (_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.sider) === null || _token$layout === void 0 ? void 0 : _token$layout.colorTextCollapsedButton,\n    backgroundColor: (_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.sider) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.colorBgCollapsedButton,\n    boxShadow: '0 2px 8px -2px rgba(0,0,0,0.05), 0 1px 4px -1px rgba(25,15,15,0.07), 0 0 1px 0 rgba(0,0,0,0.08)',\n    '&:hover': {\n      color: (_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.sider) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorTextCollapsedButtonHover,\n      boxShadow: '0 4px 16px -4px rgba(0,0,0,0.05), 0 2px 8px -2px rgba(25,15,15,0.07), 0 1px 2px 0 rgba(0,0,0,0.08)'\n    },\n    '.anticon': {\n      fontSize: '14px'\n    },\n    '& > svg': {\n      transition: 'transform  0.3s',\n      transform: 'rotate(90deg)'\n    },\n    '&-collapsed': {\n      '& > svg': {\n        transform: 'rotate(-90deg)'\n      }\n    }\n  });\n};\nexport function useStyle(prefixCls) {\n  return useAntdStyle('SiderMenuCollapsedIcon', function (token) {\n    var siderMenuToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls)\n    });\n    return [genSiderMenuStyle(siderMenuToken)];\n  });\n}", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"isMobile\", \"collapsed\"];\nimport classNames from 'classnames';\nimport { ArrowSvgIcon } from \"../SiderMenu/Arrow\";\nimport { useStyle } from \"./style\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var CollapsedIcon = function CollapsedIcon(props) {\n  var isMobile = props.isMobile,\n    collapsed = props.collapsed,\n    rest = _objectWithoutProperties(props, _excluded);\n  var _useStyle = useStyle(props.className),\n    wrapSSR = _useStyle.wrapSSR,\n    hashId = _useStyle.hashId;\n  if (isMobile && collapsed) return null;\n  return wrapSSR( /*#__PURE__*/_jsx(\"div\", _objectSpread(_objectSpread({}, rest), {}, {\n    className: classNames(props.className, hashId, _defineProperty(_defineProperty({}, \"\".concat(props.className, \"-collapsed\"), collapsed), \"\".concat(props.className, \"-is-mobile\"), isMobile)),\n    children: /*#__PURE__*/_jsx(ArrowSvgIcon, {})\n  })));\n};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"component\", \"viewBox\", \"spin\", \"rotate\", \"tabIndex\", \"onClick\", \"children\"];\n// Seems this is used for iconFont\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useComposeRef } from \"rc-util/es/ref\";\nimport Context from \"./Context\";\nimport { svgBaseProps, warning, useInsertStyles } from \"../utils\";\nvar Icon = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var className = props.className,\n    Component = props.component,\n    viewBox = props.viewBox,\n    spin = props.spin,\n    rotate = props.rotate,\n    tabIndex = props.tabIndex,\n    onClick = props.onClick,\n    children = props.children,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var iconRef = React.useRef();\n  var mergedRef = useComposeRef(iconRef, ref);\n  warning(Boolean(Component || children), 'Should have `component` prop or `children`.');\n  useInsertStyles(iconRef);\n  var _React$useContext = React.useContext(Context),\n    _React$useContext$pre = _React$useContext.prefixCls,\n    prefixCls = _React$useContext$pre === void 0 ? 'anticon' : _React$useContext$pre,\n    rootClassName = _React$useContext.rootClassName;\n  var classString = classNames(rootClassName, prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-spin\"), !!spin && !!Component), className);\n  var svgClassString = classNames(_defineProperty({}, \"\".concat(prefixCls, \"-spin\"), !!spin));\n  var svgStyle = rotate ? {\n    msTransform: \"rotate(\".concat(rotate, \"deg)\"),\n    transform: \"rotate(\".concat(rotate, \"deg)\")\n  } : undefined;\n  var innerSvgProps = _objectSpread(_objectSpread({}, svgBaseProps), {}, {\n    className: svgClassString,\n    style: svgStyle,\n    viewBox: viewBox\n  });\n  if (!viewBox) {\n    delete innerSvgProps.viewBox;\n  }\n\n  // component > children\n  var renderInnerNode = function renderInnerNode() {\n    if (Component) {\n      return /*#__PURE__*/React.createElement(Component, innerSvgProps, children);\n    }\n    if (children) {\n      warning(Boolean(viewBox) || React.Children.count(children) === 1 && /*#__PURE__*/React.isValidElement(children) && React.Children.only(children).type === 'use', 'Make sure that you provide correct `viewBox`' + ' prop (default `0 0 1024 1024`) to the icon.');\n      return /*#__PURE__*/React.createElement(\"svg\", _extends({}, innerSvgProps, {\n        viewBox: viewBox\n      }), children);\n    }\n    return null;\n  };\n  var iconTabIndex = tabIndex;\n  if (iconTabIndex === undefined && onClick) {\n    iconTabIndex = -1;\n  }\n  return /*#__PURE__*/React.createElement(\"span\", _extends({\n    role: \"img\"\n  }, restProps, {\n    ref: mergedRef,\n    tabIndex: iconTabIndex,\n    onClick: onClick,\n    className: classString\n  }), renderInnerNode());\n});\nIcon.displayName = 'AntdIcon';\nexport default Icon;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"type\", \"children\"];\nimport * as React from 'react';\nimport Icon from \"./Icon\";\nvar customCache = new Set();\nfunction isValidCustomScriptUrl(scriptUrl) {\n  return Boolean(typeof scriptUrl === 'string' && scriptUrl.length && !customCache.has(scriptUrl));\n}\nfunction createScriptUrlElements(scriptUrls) {\n  var index = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var currentScriptUrl = scriptUrls[index];\n  if (isValidCustomScriptUrl(currentScriptUrl)) {\n    var script = document.createElement('script');\n    script.setAttribute('src', currentScriptUrl);\n    script.setAttribute('data-namespace', currentScriptUrl);\n    if (scriptUrls.length > index + 1) {\n      script.onload = function () {\n        createScriptUrlElements(scriptUrls, index + 1);\n      };\n      script.onerror = function () {\n        createScriptUrlElements(scriptUrls, index + 1);\n      };\n    }\n    customCache.add(currentScriptUrl);\n    document.body.appendChild(script);\n  }\n}\nexport default function create() {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var scriptUrl = options.scriptUrl,\n    _options$extraCommonP = options.extraCommonProps,\n    extraCommonProps = _options$extraCommonP === void 0 ? {} : _options$extraCommonP;\n\n  /**\n   * DOM API required.\n   * Make sure in browser environment.\n   * The Custom Icon will create a <script/>\n   * that loads SVG symbols and insert the SVG Element into the document body.\n   */\n  if (scriptUrl && typeof document !== 'undefined' && typeof window !== 'undefined' && typeof document.createElement === 'function') {\n    if (Array.isArray(scriptUrl)) {\n      // 因为iconfont资源会把svg插入before，所以前加载相同type会覆盖后加载，为了数组覆盖顺序，倒叙插入\n      createScriptUrlElements(scriptUrl.reverse());\n    } else {\n      createScriptUrlElements([scriptUrl]);\n    }\n  }\n  var Iconfont = /*#__PURE__*/React.forwardRef(function (props, ref) {\n    var type = props.type,\n      children = props.children,\n      restProps = _objectWithoutProperties(props, _excluded);\n\n    // children > type\n    var content = null;\n    if (props.type) {\n      content = /*#__PURE__*/React.createElement(\"use\", {\n        xlinkHref: \"#\".concat(type)\n      });\n    }\n    if (children) {\n      content = children;\n    }\n    return /*#__PURE__*/React.createElement(Icon, _extends({}, extraCommonProps, restProps, {\n      ref: ref\n    }), content);\n  });\n  Iconfont.displayName = 'Iconfont';\n  return Iconfont;\n}", "var defaultSettings = {\n  navTheme: 'light',\n  layout: 'side',\n  contentWidth: 'Fluid',\n  fixedHeader: false,\n  fixSiderbar: true,\n  iconfontUrl: '',\n  colorPrimary: '#1677FF',\n  splitMenus: false\n};\nexport { defaultSettings };", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nvar genProLayoutBaseMenuStyle = function genProLayoutBaseMenuStyle(token, mode) {\n  var _token$layout, _token$layout2;\n  var menuToken = mode.includes('horizontal') ? (_token$layout = token.layout) === null || _token$layout === void 0 ? void 0 : _token$layout.header : (_token$layout2 = token.layout) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.sider;\n  return _objectSpread(_objectSpread(_defineProperty({}, \"\".concat(token.componentCls), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({\n    background: 'transparent',\n    color: menuToken === null || menuToken === void 0 ? void 0 : menuToken.colorTextMenu,\n    border: 'none'\n  }, \"\".concat(token.componentCls, \"-menu-item\"), {\n    transition: 'none !important'\n  }), \"\".concat(token.componentCls, \"-submenu-has-icon\"), _defineProperty({}, \"> \".concat(token.antCls, \"-menu-sub\"), {\n    paddingInlineStart: 10\n  })), \"\".concat(token.antCls, \"-menu-title-content\"), {\n    width: '100%',\n    height: '100%',\n    display: 'inline-flex'\n  }), \"\".concat(token.antCls, \"-menu-title-content\"), {\n    '&:first-child': {\n      width: '100%'\n    }\n  }), \"\".concat(token.componentCls, \"-item-icon\"), {\n    display: 'flex',\n    alignItems: 'center'\n  }), \"&&-collapsed\", _defineProperty(_defineProperty(_defineProperty({}, \"\".concat(token.antCls, \"-menu-item, \\n        \").concat(token.antCls, \"-menu-item-group > \").concat(token.antCls, \"-menu-item-group-list > \").concat(token.antCls, \"-menu-item, \\n        \").concat(token.antCls, \"-menu-item-group > \").concat(token.antCls, \"-menu-item-group-list > \").concat(token.antCls, \"-menu-submenu > \").concat(token.antCls, \"-menu-submenu-title, \\n        \").concat(token.antCls, \"-menu-submenu > \").concat(token.antCls, \"-menu-submenu-title\"), {\n    paddingInline: '0 !important',\n    marginBlock: '4px !important'\n  }), \"\".concat(token.antCls, \"-menu-item-group > \").concat(token.antCls, \"-menu-item-group-list > \").concat(token.antCls, \"-menu-submenu-selected > \").concat(token.antCls, \"-menu-submenu-title, \\n        \").concat(token.antCls, \"-menu-submenu-selected > \").concat(token.antCls, \"-menu-submenu-title\"), {\n    backgroundColor: menuToken === null || menuToken === void 0 ? void 0 : menuToken.colorBgMenuItemSelected,\n    borderRadius: token.borderRadiusLG\n  }), \"\".concat(token.componentCls, \"-group\"), _defineProperty({}, \"\".concat(token.antCls, \"-menu-item-group-title\"), {\n    paddingInline: 0\n  }))), '&-item-title', _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({\n    display: 'flex',\n    flexDirection: 'row',\n    alignItems: 'center',\n    gap: token.marginXS\n  }, \"\".concat(token.componentCls, \"-item-text\"), {\n    maxWidth: '100%',\n    textOverflow: 'ellipsis',\n    overflow: 'hidden',\n    wordBreak: 'break-all',\n    whiteSpace: 'nowrap'\n  }), '&-collapsed', _defineProperty(_defineProperty({\n    minWidth: 40,\n    height: 40\n  }, \"\".concat(token.componentCls, \"-item-icon\"), {\n    height: '16px',\n    width: '16px',\n    lineHeight: '16px !important',\n    '.anticon': {\n      lineHeight: '16px !important',\n      height: '16px'\n    }\n  }), \"\".concat(token.componentCls, \"-item-text-has-icon\"), {\n    display: 'none !important'\n  })), '&-collapsed-level-0', {\n    flexDirection: 'column',\n    justifyContent: 'center'\n  }), \"&\".concat(token.componentCls, \"-group-item-title\"), {\n    gap: token.marginXS,\n    height: 18,\n    overflow: 'hidden'\n  }), \"&\".concat(token.componentCls, \"-item-collapsed-show-title\"), _defineProperty({\n    lineHeight: '16px',\n    gap: 0\n  }, \"&\".concat(token.componentCls, \"-item-title-collapsed\"), _defineProperty(_defineProperty({\n    display: 'flex'\n  }, \"\".concat(token.componentCls, \"-item-icon\"), {\n    height: '16px',\n    width: '16px',\n    lineHeight: '16px !important',\n    '.anticon': {\n      lineHeight: '16px!important',\n      height: '16px'\n    }\n  }), \"\".concat(token.componentCls, \"-item-text\"), {\n    opacity: '1 !important',\n    display: 'inline !important',\n    textAlign: 'center',\n    fontSize: 12,\n    height: 12,\n    lineHeight: '12px',\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    whiteSpace: 'nowrap',\n    width: '100%',\n    margin: 0,\n    padding: 0,\n    marginBlockStart: 4\n  })))), '&-group', _defineProperty({}, \"\".concat(token.antCls, \"-menu-item-group-title\"), {\n    fontSize: 12,\n    color: token.colorTextLabel,\n    '.anticon': {\n      marginInlineEnd: 8\n    }\n  })), '&-group-divider', {\n    color: token.colorTextSecondary,\n    fontSize: 12,\n    lineHeight: 20\n  })), mode.includes('horizontal') ? {} : _defineProperty({}, \"\".concat(token.antCls, \"-menu-submenu\").concat(token.antCls, \"-menu-submenu-popup\"), _defineProperty({}, \"\".concat(token.componentCls, \"-item-title\"), {\n    alignItems: 'flex-start'\n  }))), {}, _defineProperty({}, \"\".concat(token.antCls, \"-menu-submenu-popup\"), {\n    backgroundColor: 'rgba(255, 255, 255, 0.42)',\n    '-webkit-backdrop-filter': 'blur(8px)',\n    backdropFilter: 'blur(8px)'\n  }));\n};\nexport function useStyle(prefixCls, mode) {\n  return useAntdStyle('ProLayoutBaseMenu' + mode, function (token) {\n    var proLayoutMenuToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls)\n    });\n    return [genProLayoutBaseMenuStyle(proLayoutMenuToken, mode || 'inline')];\n  });\n}", "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { createFromIconfontCN } from '@ant-design/icons';\nimport { ProProvider } from '@ant-design/pro-provider';\nimport { isImg, isUrl, useMountMergeState } from '@ant-design/pro-utils';\nimport { Menu, Skeleton, Tooltip } from 'antd';\nimport classNames from 'classnames';\nimport React, { useContext, useEffect, useMemo, useRef, useState } from 'react';\nimport { defaultSettings } from \"../../defaultSettings\";\nimport { getOpenKeysFromMenuData } from \"../../utils/utils\";\nimport { useStyle } from \"./style/menu\";\n\n// todo\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { createElement as _createElement } from \"react\";\nvar MenuItemTooltip = function MenuItemTooltip(props) {\n  var _useState = useState(props.collapsed),\n    _useState2 = _slicedToArray(_useState, 2),\n    collapsed = _useState2[0],\n    setCollapsed = _useState2[1];\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    open = _useState4[0],\n    setOpen = _useState4[1];\n  useEffect(function () {\n    setOpen(false);\n    setTimeout(function () {\n      setCollapsed(props.collapsed);\n    }, 400);\n  }, [props.collapsed]);\n  if (props.disable) {\n    return props.children;\n  }\n  return /*#__PURE__*/_jsx(Tooltip, {\n    title: props.title,\n    open: collapsed && props.collapsed ? open : false,\n    placement: \"right\",\n    onOpenChange: setOpen,\n    children: props.children\n  });\n};\nvar IconFont = createFromIconfontCN({\n  scriptUrl: defaultSettings.iconfontUrl\n});\n\n// Allow menu.js config icon as string or ReactNode\n//   icon: 'setting',\n//   icon: 'icon-geren' #For Iconfont ,\n//   icon: 'http://demo.com/icon.png',\n//   icon: '/favicon.png',\n//   icon: <Icon type=\"setting\" />,\nvar getIcon = function getIcon(icon) {\n  var iconPrefixes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'icon-';\n  var className = arguments.length > 2 ? arguments[2] : undefined;\n  if (typeof icon === 'string' && icon !== '') {\n    if (isUrl(icon) || isImg(icon)) {\n      return /*#__PURE__*/_jsx(\"img\", {\n        width: 16,\n        src: icon,\n        alt: \"icon\",\n        className: className\n      }, icon);\n    }\n    if (icon.startsWith(iconPrefixes)) {\n      return /*#__PURE__*/_jsx(IconFont, {\n        type: icon\n      });\n    }\n  }\n  return icon;\n};\nvar getMenuTitleSymbol = function getMenuTitleSymbol(title) {\n  if (title && typeof title === 'string') {\n    var symbol = title.substring(0, 1).toUpperCase();\n    return symbol;\n  }\n  return null;\n};\nvar MenuUtil = /*#__PURE__*/_createClass(function MenuUtil(props) {\n  var _this = this;\n  _classCallCheck(this, MenuUtil);\n  _defineProperty(this, \"props\", void 0);\n  _defineProperty(this, \"getNavMenuItems\", function () {\n    var menusData = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    var level = arguments.length > 1 ? arguments[1] : undefined;\n    var noGroupLevel = arguments.length > 2 ? arguments[2] : undefined;\n    return menusData.map(function (item) {\n      return _this.getSubMenuOrItem(item, level, noGroupLevel);\n    }).filter(function (item) {\n      return item;\n    }).flat(1);\n  });\n  /** Get SubMenu or Item */\n  _defineProperty(this, \"getSubMenuOrItem\", function (item, level, noGroupLevel) {\n    var _this$props = _this.props,\n      subMenuItemRender = _this$props.subMenuItemRender,\n      baseClassName = _this$props.baseClassName,\n      prefixCls = _this$props.prefixCls,\n      collapsed = _this$props.collapsed,\n      menu = _this$props.menu,\n      iconPrefixes = _this$props.iconPrefixes,\n      layout = _this$props.layout;\n    var isGroup = (menu === null || menu === void 0 ? void 0 : menu.type) === 'group' && layout !== 'top';\n    var designToken = _this.props.token;\n    var name = _this.getIntlName(item);\n    var children = (item === null || item === void 0 ? void 0 : item.children) || (item === null || item === void 0 ? void 0 : item.routes);\n    var menuType = isGroup && level === 0 ? 'group' : undefined;\n    if (Array.isArray(children) && children.length > 0) {\n      var _this$props2, _this$props3, _this$props4, _this$props5, _designToken$layout;\n      /** Menu 第一级可以有icon，或者 isGroup 时第二级别也要有 */\n      var shouldHasIcon = level === 0 || isGroup && level === 1;\n\n      //  get defaultTitle by menuItemRender\n      var iconDom = getIcon(item.icon, iconPrefixes, \"\".concat(baseClassName, \"-icon \").concat((_this$props2 = _this.props) === null || _this$props2 === void 0 ? void 0 : _this$props2.hashId));\n      /**\n       * 如果没有icon在收起的时候用首字母代替\n       */\n      var defaultIcon = collapsed && shouldHasIcon ? getMenuTitleSymbol(name) : null;\n      var defaultTitle = /*#__PURE__*/_jsxs(\"div\", {\n        className: classNames(\"\".concat(baseClassName, \"-item-title\"), (_this$props3 = _this.props) === null || _this$props3 === void 0 ? void 0 : _this$props3.hashId, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-item-title-collapsed\"), collapsed), \"\".concat(baseClassName, \"-item-title-collapsed-level-\").concat(noGroupLevel), collapsed), \"\".concat(baseClassName, \"-group-item-title\"), menuType === 'group'), \"\".concat(baseClassName, \"-item-collapsed-show-title\"), (menu === null || menu === void 0 ? void 0 : menu.collapsedShowTitle) && collapsed)),\n        children: [menuType === 'group' && collapsed ? null : shouldHasIcon && iconDom ? /*#__PURE__*/_jsx(\"span\", {\n          className: \"\".concat(baseClassName, \"-item-icon \").concat((_this$props4 = _this.props) === null || _this$props4 === void 0 ? void 0 : _this$props4.hashId).trim(),\n          children: iconDom\n        }) : defaultIcon, /*#__PURE__*/_jsx(\"span\", {\n          className: classNames(\"\".concat(baseClassName, \"-item-text\"), (_this$props5 = _this.props) === null || _this$props5 === void 0 ? void 0 : _this$props5.hashId, _defineProperty({}, \"\".concat(baseClassName, \"-item-text-has-icon\"), menuType !== 'group' && shouldHasIcon && (iconDom || defaultIcon))),\n          children: name\n        })]\n      });\n\n      // subMenu only title render\n      var title = subMenuItemRender ? subMenuItemRender(_objectSpread(_objectSpread({}, item), {}, {\n        isUrl: false\n      }), defaultTitle, _this.props) : defaultTitle;\n\n      // 如果收起来，没有子菜单了，就不需要展示 group，所以 level 不增加\n      if (isGroup && level === 0 && _this.props.collapsed && !menu.collapsedShowGroupTitle) {\n        return _this.getNavMenuItems(children, level + 1, level);\n      }\n      var childrenList = _this.getNavMenuItems(children, level + 1, isGroup && level === 0 && _this.props.collapsed ? level : level + 1);\n      return [{\n        type: menuType,\n        key: item.key || item.path,\n        label: title,\n        onClick: isGroup ? undefined : item.onTitleClick,\n        children: childrenList,\n        className: classNames(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-group\"), menuType === 'group'), \"\".concat(baseClassName, \"-submenu\"), menuType !== 'group'), \"\".concat(baseClassName, \"-submenu-has-icon\"), menuType !== 'group' && shouldHasIcon && iconDom))\n      }, isGroup && level === 0 ? {\n        type: 'divider',\n        prefixCls: prefixCls,\n        className: \"\".concat(baseClassName, \"-divider\"),\n        key: (item.key || item.path) + '-group-divider',\n        style: {\n          padding: 0,\n          borderBlockEnd: 0,\n          margin: _this.props.collapsed ? '4px' : '6px 16px',\n          marginBlockStart: _this.props.collapsed ? 4 : 8,\n          borderColor: designToken === null || designToken === void 0 || (_designToken$layout = designToken.layout) === null || _designToken$layout === void 0 || (_designToken$layout = _designToken$layout.sider) === null || _designToken$layout === void 0 ? void 0 : _designToken$layout.colorMenuItemDivider\n        }\n      } : undefined].filter(Boolean);\n    }\n    return {\n      className: \"\".concat(baseClassName, \"-menu-item\"),\n      disabled: item.disabled,\n      key: item.key || item.path,\n      onClick: item.onTitleClick,\n      // eslint-disable-next-line react/no-is-mounted\n      label: _this.getMenuItemPath(item, level, noGroupLevel)\n    };\n  });\n  _defineProperty(this, \"getIntlName\", function (item) {\n    var name = item.name,\n      locale = item.locale;\n    var _this$props6 = _this.props,\n      menu = _this$props6.menu,\n      formatMessage = _this$props6.formatMessage;\n    var finalName = name;\n    if (locale && (menu === null || menu === void 0 ? void 0 : menu.locale) !== false) {\n      finalName = formatMessage === null || formatMessage === void 0 ? void 0 : formatMessage({\n        id: locale,\n        defaultMessage: name\n      });\n    }\n    if (_this.props.menuTextRender) {\n      return _this.props.menuTextRender(item, finalName, _this.props);\n    }\n    return finalName;\n  });\n  /**\n   * 判断是否是http链接.返回 Link 或 a Judge whether it is http link.return a or Link\n   *\n   * @memberof SiderMenu\n   */\n  _defineProperty(this, \"getMenuItemPath\", function (item, level, noGroupLevel) {\n    var _this$props9, _this$props10, _this$props11, _this$props12;\n    var itemPath = _this.conversionPath(item.path || '/');\n    var _this$props7 = _this.props,\n      _this$props7$location = _this$props7.location,\n      location = _this$props7$location === void 0 ? {\n        pathname: '/'\n      } : _this$props7$location,\n      isMobile = _this$props7.isMobile,\n      onCollapse = _this$props7.onCollapse,\n      menuItemRender = _this$props7.menuItemRender,\n      iconPrefixes = _this$props7.iconPrefixes;\n\n    // if local is true formatMessage all name。\n    var menuItemTitle = _this.getIntlName(item);\n    var _this$props8 = _this.props,\n      baseClassName = _this$props8.baseClassName,\n      menu = _this$props8.menu,\n      collapsed = _this$props8.collapsed;\n    var isGroup = (menu === null || menu === void 0 ? void 0 : menu.type) === 'group';\n    /** Menu 第一级可以有icon，或者 isGroup 时第二级别也要有 */\n    var hasIcon = level === 0 || isGroup && level === 1;\n    var icon = !hasIcon ? null : getIcon(item.icon, iconPrefixes, \"\".concat(baseClassName, \"-icon \").concat((_this$props9 = _this.props) === null || _this$props9 === void 0 ? void 0 : _this$props9.hashId));\n\n    // 如果没有 icon 在收起的时候用首字母代替\n    var defaultIcon = collapsed && hasIcon ? getMenuTitleSymbol(menuItemTitle) : null;\n    var defaultItem = /*#__PURE__*/_jsxs(\"div\", {\n      className: classNames(\"\".concat(baseClassName, \"-item-title\"), (_this$props10 = _this.props) === null || _this$props10 === void 0 ? void 0 : _this$props10.hashId, _defineProperty(_defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-item-title-collapsed\"), collapsed), \"\".concat(baseClassName, \"-item-title-collapsed-level-\").concat(noGroupLevel), collapsed), \"\".concat(baseClassName, \"-item-collapsed-show-title\"), (menu === null || menu === void 0 ? void 0 : menu.collapsedShowTitle) && collapsed)),\n      children: [/*#__PURE__*/_jsx(\"span\", {\n        className: \"\".concat(baseClassName, \"-item-icon \").concat((_this$props11 = _this.props) === null || _this$props11 === void 0 ? void 0 : _this$props11.hashId).trim(),\n        style: {\n          display: defaultIcon === null && !icon ? 'none' : ''\n        },\n        children: icon || /*#__PURE__*/_jsx(\"span\", {\n          className: \"anticon\",\n          children: defaultIcon\n        })\n      }), /*#__PURE__*/_jsx(\"span\", {\n        className: classNames(\"\".concat(baseClassName, \"-item-text\"), (_this$props12 = _this.props) === null || _this$props12 === void 0 ? void 0 : _this$props12.hashId, _defineProperty({}, \"\".concat(baseClassName, \"-item-text-has-icon\"), hasIcon && (icon || defaultIcon))),\n        children: menuItemTitle\n      })]\n    }, itemPath);\n    var isHttpUrl = isUrl(itemPath);\n\n    // Is it a http link\n    if (isHttpUrl) {\n      var _this$props13, _this$props14, _this$props15;\n      defaultItem = /*#__PURE__*/_jsxs(\"span\", {\n        onClick: function onClick() {\n          var _window, _window$open;\n          (_window = window) === null || _window === void 0 || (_window$open = _window.open) === null || _window$open === void 0 || _window$open.call(_window, itemPath, '_blank');\n        },\n        className: classNames(\"\".concat(baseClassName, \"-item-title\"), (_this$props13 = _this.props) === null || _this$props13 === void 0 ? void 0 : _this$props13.hashId, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-item-title-collapsed\"), collapsed), \"\".concat(baseClassName, \"-item-title-collapsed-level-\").concat(noGroupLevel), collapsed), \"\".concat(baseClassName, \"-item-link\"), true), \"\".concat(baseClassName, \"-item-collapsed-show-title\"), (menu === null || menu === void 0 ? void 0 : menu.collapsedShowTitle) && collapsed)),\n        children: [/*#__PURE__*/_jsx(\"span\", {\n          className: \"\".concat(baseClassName, \"-item-icon \").concat((_this$props14 = _this.props) === null || _this$props14 === void 0 ? void 0 : _this$props14.hashId).trim(),\n          style: {\n            display: defaultIcon === null && !icon ? 'none' : ''\n          },\n          children: icon || /*#__PURE__*/_jsx(\"span\", {\n            className: \"anticon\",\n            children: defaultIcon\n          })\n        }), /*#__PURE__*/_jsx(\"span\", {\n          className: classNames(\"\".concat(baseClassName, \"-item-text\"), (_this$props15 = _this.props) === null || _this$props15 === void 0 ? void 0 : _this$props15.hashId, _defineProperty({}, \"\".concat(baseClassName, \"-item-text-has-icon\"), hasIcon && (icon || defaultIcon))),\n          children: menuItemTitle\n        })]\n      }, itemPath);\n    }\n    if (menuItemRender) {\n      var renderItemProps = _objectSpread(_objectSpread({}, item), {}, {\n        isUrl: isHttpUrl,\n        itemPath: itemPath,\n        isMobile: isMobile,\n        replace: itemPath === location.pathname,\n        onClick: function onClick() {\n          return onCollapse && onCollapse(true);\n        },\n        children: undefined\n      });\n      return level === 0 ? /*#__PURE__*/_jsx(MenuItemTooltip, {\n        collapsed: collapsed,\n        title: menuItemTitle,\n        disable: item.disabledTooltip,\n        children: menuItemRender(renderItemProps, defaultItem, _this.props)\n      }) : menuItemRender(renderItemProps, defaultItem, _this.props);\n    }\n    return level === 0 ? /*#__PURE__*/_jsx(MenuItemTooltip, {\n      collapsed: collapsed,\n      title: menuItemTitle,\n      disable: item.disabledTooltip,\n      children: defaultItem\n    }) : defaultItem;\n  });\n  _defineProperty(this, \"conversionPath\", function (path) {\n    if (path && path.indexOf('http') === 0) {\n      return path;\n    }\n    return \"/\".concat(path || '').replace(/\\/+/g, '/');\n  });\n  this.props = props;\n});\n/**\n * 生成openKeys 的对象，因为设置了openKeys 就会变成受控，所以需要一个空对象\n *\n * @param BaseMenuProps\n */\nvar getOpenKeysProps = function getOpenKeysProps(openKeys, _ref) {\n  var layout = _ref.layout,\n    collapsed = _ref.collapsed;\n  var openKeysProps = {};\n  if (openKeys && !collapsed && ['side', 'mix'].includes(layout || 'mix')) {\n    openKeysProps = {\n      openKeys: openKeys\n    };\n  }\n  return openKeysProps;\n};\nvar BaseMenu = function BaseMenu(props) {\n  var mode = props.mode,\n    className = props.className,\n    handleOpenChange = props.handleOpenChange,\n    style = props.style,\n    menuData = props.menuData,\n    prefixCls = props.prefixCls,\n    menu = props.menu,\n    matchMenuKeys = props.matchMenuKeys,\n    iconfontUrl = props.iconfontUrl,\n    propsSelectedKeys = props.selectedKeys,\n    onSelect = props.onSelect,\n    menuRenderType = props.menuRenderType,\n    propsOpenKeys = props.openKeys;\n  var _useContext = useContext(ProProvider),\n    dark = _useContext.dark,\n    designToken = _useContext.token;\n  var baseClassName = \"\".concat(prefixCls, \"-base-menu-\").concat(mode);\n  // 用于减少 defaultOpenKeys 计算的组件\n  var defaultOpenKeysRef = useRef([]);\n  var _useMountMergeState = useMountMergeState(menu === null || menu === void 0 ? void 0 : menu.defaultOpenAll),\n    _useMountMergeState2 = _slicedToArray(_useMountMergeState, 2),\n    defaultOpenAll = _useMountMergeState2[0],\n    setDefaultOpenAll = _useMountMergeState2[1];\n  var _useMountMergeState3 = useMountMergeState(function () {\n      if (menu !== null && menu !== void 0 && menu.defaultOpenAll) {\n        return getOpenKeysFromMenuData(menuData) || [];\n      }\n      if (propsOpenKeys === false) {\n        return false;\n      }\n      return [];\n    }, {\n      value: propsOpenKeys === false ? undefined : propsOpenKeys,\n      onChange: handleOpenChange\n    }),\n    _useMountMergeState4 = _slicedToArray(_useMountMergeState3, 2),\n    openKeys = _useMountMergeState4[0],\n    setOpenKeys = _useMountMergeState4[1];\n  var _useMountMergeState5 = useMountMergeState([], {\n      value: propsSelectedKeys,\n      onChange: onSelect ? function (keys) {\n        if (onSelect && keys) {\n          onSelect(keys);\n        }\n      } : undefined\n    }),\n    _useMountMergeState6 = _slicedToArray(_useMountMergeState5, 2),\n    selectedKeys = _useMountMergeState6[0],\n    setSelectedKeys = _useMountMergeState6[1];\n  useEffect(function () {\n    if (menu !== null && menu !== void 0 && menu.defaultOpenAll || propsOpenKeys === false) {\n      return;\n    }\n    if (matchMenuKeys) {\n      setOpenKeys(matchMenuKeys);\n      setSelectedKeys(matchMenuKeys);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [matchMenuKeys.join('-')]);\n  useEffect(function () {\n    // reset IconFont\n    if (iconfontUrl) {\n      IconFont = createFromIconfontCN({\n        scriptUrl: iconfontUrl\n      });\n    }\n  }, [iconfontUrl]);\n  useEffect(function () {\n    // if pathname can't match, use the nearest parent's key\n    if (matchMenuKeys.join('-') !== (selectedKeys || []).join('-')) {\n      setSelectedKeys(matchMenuKeys);\n    }\n    if (!defaultOpenAll && propsOpenKeys !== false && matchMenuKeys.join('-') !== (openKeys || []).join('-')) {\n      var newKeys = matchMenuKeys;\n      // 如果不自动关闭，我需要把 openKeys 放进去\n      if ((menu === null || menu === void 0 ? void 0 : menu.autoClose) === false) {\n        newKeys = Array.from(new Set([].concat(_toConsumableArray(matchMenuKeys), _toConsumableArray(openKeys || []))));\n      }\n      setOpenKeys(newKeys);\n    } else if (menu !== null && menu !== void 0 && menu.ignoreFlatMenu && defaultOpenAll) {\n      // 忽略用户手动折叠过的菜单状态，折叠按钮切换之后也可实现默认展开所有菜单\n      setOpenKeys(getOpenKeysFromMenuData(menuData));\n    } else {\n      setDefaultOpenAll(false);\n    }\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [matchMenuKeys.join('-')]);\n  var openKeysProps = useMemo(function () {\n    return getOpenKeysProps(openKeys, props);\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [openKeys && openKeys.join(','), props.layout, props.collapsed]);\n  var _useStyle = useStyle(baseClassName, mode),\n    wrapSSR = _useStyle.wrapSSR,\n    hashId = _useStyle.hashId;\n  var menuUtils = useMemo(function () {\n    return new MenuUtil(_objectSpread(_objectSpread({}, props), {}, {\n      token: designToken,\n      menuRenderType: menuRenderType,\n      baseClassName: baseClassName,\n      hashId: hashId\n    }));\n  }, [props, designToken, menuRenderType, baseClassName, hashId]);\n  if (menu !== null && menu !== void 0 && menu.loading) {\n    return /*#__PURE__*/_jsx(\"div\", {\n      style: mode !== null && mode !== void 0 && mode.includes('inline') ? {\n        padding: 24\n      } : {\n        marginBlockStart: 16\n      },\n      children: /*#__PURE__*/_jsx(Skeleton, {\n        active: true,\n        title: false,\n        paragraph: {\n          rows: mode !== null && mode !== void 0 && mode.includes('inline') ? 6 : 1\n        }\n      })\n    });\n  }\n\n  // 这次 openKeys === false 的时候的情况，这种情况下帮用户选中一次\n  // 第二此不会使用，所以用了 defaultOpenKeys\n  // 这里返回 null，是为了让 defaultOpenKeys 生效\n  if (props.openKeys === false && !props.handleOpenChange) {\n    defaultOpenKeysRef.current = matchMenuKeys;\n  }\n  var finallyData = props.postMenuData ? props.postMenuData(menuData) : menuData;\n  if (finallyData && (finallyData === null || finallyData === void 0 ? void 0 : finallyData.length) < 1) {\n    return null;\n  }\n  return wrapSSR( /*#__PURE__*/_createElement(Menu, _objectSpread(_objectSpread({}, openKeysProps), {}, {\n    _internalDisableMenuItemTitleTooltip: true,\n    key: \"Menu\",\n    mode: mode,\n    inlineIndent: 16,\n    defaultOpenKeys: defaultOpenKeysRef.current,\n    theme: dark ? 'dark' : 'light',\n    selectedKeys: selectedKeys,\n    style: _objectSpread({\n      backgroundColor: 'transparent',\n      border: 'none'\n    }, style),\n    className: classNames(className, hashId, baseClassName, _defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-horizontal\"), mode === 'horizontal'), \"\".concat(baseClassName, \"-collapsed\"), props.collapsed)),\n    items: menuUtils.getNavMenuItems(finallyData, 0, 0),\n    onOpenChange: function onOpenChange(_openKeys) {\n      if (!props.collapsed) {\n        setOpenKeys(_openKeys);\n      }\n    }\n  }, props.menuProps)));\n};\nexport { BaseMenu };", "/** 判断是否是图片链接 */\nexport function isImg(path) {\n  return /\\w.(png|jpg|jpeg|svg|webp|gif|bmp)$/i.test(path);\n}", "import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"title\", \"render\"];\nimport { ProProvider } from '@ant-design/pro-provider';\nimport { Avatar, Layout, Menu, Space, version } from 'antd';\nimport classNames from 'classnames';\nimport React, { useContext, useMemo } from 'react';\nimport { AppsLogoComponents, defaultRenderLogo } from \"../AppsLogoComponents\";\nimport { CollapsedIcon } from \"../CollapsedIcon\";\nimport { BaseMenu } from \"./BaseMenu\";\nimport { useStylish } from \"./style/stylish\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { createElement as _createElement } from \"react\";\nvar _SafetyWarningProvider = /*#__PURE__*/React.memo(function (props) {\n  if (process.env.NODE_ENV !== 'production') {\n    console.warn(\"[pro-layout] SiderMenu required antd@^4.24.15 || antd@^5.11.2 for access the menu context, please upgrade your antd version (current \".concat(version, \").\"));\n  }\n  return /*#__PURE__*/_jsx(_Fragment, {\n    children: props.children\n  });\n});\nvar Sider = Layout.Sider,\n  _Layout$_InternalSide = Layout._InternalSiderContext,\n  SiderContext = _Layout$_InternalSide === void 0 ? {\n    Provider: _SafetyWarningProvider\n  } : _Layout$_InternalSide;\n/**\n * 渲染 title 和 logo\n *\n * @param props\n * @param renderKey\n * @returns\n */\nexport var renderLogoAndTitle = function renderLogoAndTitle(props) {\n  var renderKey = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'menuHeaderRender';\n  var logo = props.logo,\n    title = props.title,\n    layout = props.layout;\n  var renderFunction = props[renderKey];\n  if (renderFunction === false) {\n    return null;\n  }\n  var logoDom = defaultRenderLogo(logo);\n  var titleDom = /*#__PURE__*/_jsx(\"h1\", {\n    children: title !== null && title !== void 0 ? title : 'Ant Design Pro'\n  });\n  if (renderFunction) {\n    // when collapsed, no render title\n    return renderFunction(logoDom, props.collapsed ? null : titleDom, props);\n  }\n\n  /**\n   * 收起来时候直接不显示\n   */\n  if (props.isMobile) {\n    return null;\n  }\n  if (layout === 'mix' && renderKey === 'menuHeaderRender') return false;\n  if (props.collapsed) {\n    return /*#__PURE__*/_jsx(\"a\", {\n      children: logoDom\n    }, \"title\");\n  }\n  return /*#__PURE__*/_jsxs(\"a\", {\n    children: [logoDom, titleDom]\n  }, \"title\");\n};\nvar SiderMenu = function SiderMenu(props) {\n  var _props$menu2;\n  var collapsed = props.collapsed,\n    originCollapsed = props.originCollapsed,\n    fixSiderbar = props.fixSiderbar,\n    menuFooterRender = props.menuFooterRender,\n    _onCollapse = props.onCollapse,\n    theme = props.theme,\n    siderWidth = props.siderWidth,\n    isMobile = props.isMobile,\n    onMenuHeaderClick = props.onMenuHeaderClick,\n    _props$breakpoint = props.breakpoint,\n    breakpoint = _props$breakpoint === void 0 ? 'lg' : _props$breakpoint,\n    style = props.style,\n    layout = props.layout,\n    _props$menuExtraRende = props.menuExtraRender,\n    menuExtraRender = _props$menuExtraRende === void 0 ? false : _props$menuExtraRende,\n    links = props.links,\n    menuContentRender = props.menuContentRender,\n    collapsedButtonRender = props.collapsedButtonRender,\n    prefixCls = props.prefixCls,\n    avatarProps = props.avatarProps,\n    rightContentRender = props.rightContentRender,\n    actionsRender = props.actionsRender,\n    onOpenChange = props.onOpenChange,\n    stylish = props.stylish,\n    logoStyle = props.logoStyle;\n  var _useContext = useContext(ProProvider),\n    hashId = _useContext.hashId;\n  var showSiderExtraDom = useMemo(function () {\n    if (isMobile) return false;\n    if (layout === 'mix') return false;\n    return true;\n  }, [isMobile, layout]);\n  var baseClassName = \"\".concat(prefixCls, \"-sider\");\n\n  // 收起的宽度\n  var collapsedWidth = 64;\n\n  // 之所以这样写是为了提升样式优先级，不然会被sider 自带的覆盖掉\n  var stylishClassName = useStylish(\"\".concat(baseClassName, \".\").concat(baseClassName, \"-stylish\"), {\n    stylish: stylish,\n    proLayoutCollapsedWidth: collapsedWidth\n  });\n  var siderClassName = classNames(\"\".concat(baseClassName), hashId, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-fixed\"), fixSiderbar), \"\".concat(baseClassName, \"-fixed-mix\"), layout === 'mix' && !isMobile && fixSiderbar), \"\".concat(baseClassName, \"-collapsed\"), props.collapsed), \"\".concat(baseClassName, \"-layout-\").concat(layout), layout && !isMobile), \"\".concat(baseClassName, \"-light\"), theme !== 'dark'), \"\".concat(baseClassName, \"-mix\"), layout === 'mix' && !isMobile), \"\".concat(baseClassName, \"-stylish\"), !!stylish));\n  var headerDom = renderLogoAndTitle(props);\n  var extraDom = menuExtraRender && menuExtraRender(props);\n  var menuDom = useMemo(function () {\n    return menuContentRender !== false && /*#__PURE__*/_createElement(BaseMenu, _objectSpread(_objectSpread({}, props), {}, {\n      key: \"base-menu\",\n      mode: collapsed && !isMobile ? 'vertical' : 'inline',\n      handleOpenChange: onOpenChange,\n      style: {\n        width: '100%'\n      },\n      className: \"\".concat(baseClassName, \"-menu \").concat(hashId).trim()\n    }));\n  }, [baseClassName, hashId, menuContentRender, onOpenChange, props]);\n  var linksMenuItems = (links || []).map(function (node, index) {\n    return {\n      className: \"\".concat(baseClassName, \"-link\"),\n      label: node,\n      key: index\n    };\n  });\n  var menuRenderDom = useMemo(function () {\n    return menuContentRender ? menuContentRender(props, menuDom) : menuDom;\n  }, [menuContentRender, menuDom, props]);\n  var avatarDom = useMemo(function () {\n    if (!avatarProps) return null;\n    var title = avatarProps.title,\n      render = avatarProps.render,\n      rest = _objectWithoutProperties(avatarProps, _excluded);\n    var dom = /*#__PURE__*/_jsxs(\"div\", {\n      className: \"\".concat(baseClassName, \"-actions-avatar\"),\n      children: [rest !== null && rest !== void 0 && rest.src || rest !== null && rest !== void 0 && rest.srcSet || rest.icon || rest.children ? /*#__PURE__*/_jsx(Avatar, _objectSpread({\n        size: 28\n      }, rest)) : null, avatarProps.title && !collapsed && /*#__PURE__*/_jsx(\"span\", {\n        children: title\n      })]\n    });\n    if (render) {\n      return render(avatarProps, dom, props);\n    }\n    return dom;\n  }, [avatarProps, baseClassName, collapsed]);\n  var actionsDom = useMemo(function () {\n    if (!actionsRender) return null;\n    return /*#__PURE__*/_jsx(Space, {\n      align: \"center\",\n      size: 4,\n      direction: collapsed ? 'vertical' : 'horizontal',\n      className: classNames([\"\".concat(baseClassName, \"-actions-list\"), collapsed && \"\".concat(baseClassName, \"-actions-list-collapsed\"), hashId]),\n      children: [actionsRender === null || actionsRender === void 0 ? void 0 : actionsRender(props)].flat(1).map(function (item, index) {\n        return /*#__PURE__*/_jsx(\"div\", {\n          className: \"\".concat(baseClassName, \"-actions-list-item \").concat(hashId).trim(),\n          children: item\n        }, index);\n      })\n    });\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [actionsRender, baseClassName, collapsed]);\n  var appsDom = useMemo(function () {\n    return /*#__PURE__*/_jsx(AppsLogoComponents, {\n      onItemClick: props.itemClick,\n      appListRender: props.appListRender,\n      appList: props.appList,\n      prefixCls: props.prefixCls\n    });\n  }, [props.appList, props.appListRender, props.prefixCls]);\n  var collapsedDom = useMemo(function () {\n    if (collapsedButtonRender === false) return null;\n    var dom = /*#__PURE__*/_jsx(CollapsedIcon, {\n      isMobile: isMobile,\n      collapsed: originCollapsed,\n      className: \"\".concat(baseClassName, \"-collapsed-button\"),\n      onClick: function onClick() {\n        _onCollapse === null || _onCollapse === void 0 || _onCollapse(!originCollapsed);\n      }\n    });\n    if (collapsedButtonRender) return collapsedButtonRender(collapsed, dom);\n    return dom;\n  }, [collapsedButtonRender, isMobile, originCollapsed, baseClassName, collapsed, _onCollapse]);\n\n  /** 操作区域的dom */\n  var actionAreaDom = useMemo(function () {\n    if (!avatarDom && !actionsDom) return null;\n    return /*#__PURE__*/_jsxs(\"div\", {\n      className: classNames(\"\".concat(baseClassName, \"-actions\"), hashId, collapsed && \"\".concat(baseClassName, \"-actions-collapsed\")),\n      children: [avatarDom, actionsDom]\n    });\n  }, [actionsDom, avatarDom, baseClassName, collapsed, hashId]);\n\n  /* Using the useMemo hook to create a CSS class that will hide the menu when the menu is collapsed. */\n  var hideMenuWhenCollapsedClassName = useMemo(function () {\n    var _props$menu;\n    // 收起时完全隐藏菜单\n    if (props !== null && props !== void 0 && (_props$menu = props.menu) !== null && _props$menu !== void 0 && _props$menu.hideMenuWhenCollapsed && collapsed) {\n      return \"\".concat(baseClassName, \"-hide-menu-collapsed\");\n    }\n    return null;\n  }, [baseClassName, collapsed, props === null || props === void 0 || (_props$menu2 = props.menu) === null || _props$menu2 === void 0 ? void 0 : _props$menu2.hideMenuWhenCollapsed]);\n  var menuFooterDom = menuFooterRender && (menuFooterRender === null || menuFooterRender === void 0 ? void 0 : menuFooterRender(props));\n  var menuDomItems = /*#__PURE__*/_jsxs(_Fragment, {\n    children: [headerDom && /*#__PURE__*/_jsxs(\"div\", {\n      className: classNames([classNames(\"\".concat(baseClassName, \"-logo\"), hashId, _defineProperty({}, \"\".concat(baseClassName, \"-logo-collapsed\"), collapsed))]),\n      onClick: showSiderExtraDom ? onMenuHeaderClick : undefined,\n      id: \"logo\",\n      style: logoStyle,\n      children: [headerDom, appsDom]\n    }), extraDom && /*#__PURE__*/_jsx(\"div\", {\n      className: classNames([\"\".concat(baseClassName, \"-extra\"), !headerDom && \"\".concat(baseClassName, \"-extra-no-logo\"), hashId]),\n      children: extraDom\n    }), /*#__PURE__*/_jsx(\"div\", {\n      style: {\n        flex: 1,\n        overflowY: 'auto',\n        overflowX: 'hidden'\n      },\n      children: menuRenderDom\n    }), /*#__PURE__*/_jsxs(SiderContext.Provider, {\n      value: {},\n      children: [links ? /*#__PURE__*/_jsx(\"div\", {\n        className: \"\".concat(baseClassName, \"-links \").concat(hashId).trim(),\n        children: /*#__PURE__*/_jsx(Menu, {\n          inlineIndent: 16,\n          className: \"\".concat(baseClassName, \"-link-menu \").concat(hashId).trim(),\n          selectedKeys: [],\n          openKeys: [],\n          theme: theme,\n          mode: \"inline\",\n          items: linksMenuItems\n        })\n      }) : null, showSiderExtraDom && /*#__PURE__*/_jsxs(_Fragment, {\n        children: [actionAreaDom, !actionsDom && rightContentRender ? /*#__PURE__*/_jsx(\"div\", {\n          className: classNames(\"\".concat(baseClassName, \"-actions\"), hashId, _defineProperty({}, \"\".concat(baseClassName, \"-actions-collapsed\"), collapsed)),\n          children: rightContentRender === null || rightContentRender === void 0 ? void 0 : rightContentRender(props)\n        }) : null]\n      }), menuFooterDom && /*#__PURE__*/_jsx(\"div\", {\n        className: classNames([\"\".concat(baseClassName, \"-footer\"), hashId, _defineProperty({}, \"\".concat(baseClassName, \"-footer-collapsed\"), collapsed)]),\n        children: menuFooterDom\n      })]\n    })]\n  });\n  return stylishClassName.wrapSSR( /*#__PURE__*/_jsxs(_Fragment, {\n    children: [fixSiderbar && !isMobile && !hideMenuWhenCollapsedClassName && /*#__PURE__*/_jsx(\"div\", {\n      style: _objectSpread({\n        width: collapsed ? collapsedWidth : siderWidth,\n        overflow: 'hidden',\n        flex: \"0 0 \".concat(collapsed ? collapsedWidth : siderWidth, \"px\"),\n        maxWidth: collapsed ? collapsedWidth : siderWidth,\n        minWidth: collapsed ? collapsedWidth : siderWidth,\n        transition: 'all 0.2s ease 0s'\n      }, style)\n    }), /*#__PURE__*/_jsxs(Sider, {\n      collapsible: true,\n      trigger: null,\n      collapsed: collapsed,\n      breakpoint: breakpoint === false ? undefined : breakpoint,\n      onCollapse: function onCollapse(collapse) {\n        if (isMobile) return;\n        _onCollapse === null || _onCollapse === void 0 || _onCollapse(collapse);\n      },\n      collapsedWidth: collapsedWidth,\n      style: style,\n      theme: theme,\n      width: siderWidth,\n      className: classNames(siderClassName, hashId, hideMenuWhenCollapsedClassName),\n      children: [hideMenuWhenCollapsedClassName ? /*#__PURE__*/_jsx(\"div\", {\n        className: \"\".concat(baseClassName, \"-hide-when-collapsed \").concat(hashId).trim(),\n        style: {\n          height: '100%',\n          width: '100%',\n          opacity: hideMenuWhenCollapsedClassName ? 0 : 1\n        },\n        children: menuDomItems\n      }) : menuDomItems, collapsedDom]\n    })]\n  }));\n};\nexport { SiderMenu };", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nexport function useStylish(prefixCls, _ref) {\n  var stylish = _ref.stylish,\n    proLayoutCollapsedWidth = _ref.proLayoutCollapsedWidth;\n  return useAntdStyle('ProLayoutSiderMenuStylish', function (token) {\n    var siderMenuToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls),\n      proLayoutCollapsedWidth: proLayoutCollapsedWidth\n    });\n    if (!stylish) return [];\n    return [_defineProperty({}, \"div\".concat(token.proComponentsCls, \"-layout\"), _defineProperty({}, \"\".concat(siderMenuToken.componentCls), stylish === null || stylish === void 0 ? void 0 : stylish(siderMenuToken)))];\n  });\n}", "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { useCallback, useRef } from 'react';\nvar useRefFunction = function useRefFunction(reFunction) {\n  var ref = useRef(null);\n  ref.current = reFunction;\n  return useCallback(function () {\n    var _ref$current;\n    for (var _len = arguments.length, rest = new Array(_len), _key = 0; _key < _len; _key++) {\n      rest[_key] = arguments[_key];\n    }\n    return (_ref$current = ref.current) === null || _ref$current === void 0 ? void 0 : _ref$current.call.apply(_ref$current, [ref].concat(_toConsumableArray(rest)));\n  }, []);\n};\nexport { useRefFunction };", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-utils';\nvar genTopNavHeaderStyle = function genTopNavHeaderStyle(token) {\n  var _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout5;\n  return _defineProperty({}, token.componentCls, {\n    '&-header-actions': {\n      display: 'flex',\n      height: '100%',\n      alignItems: 'center',\n      '&-item': {\n        display: 'inline-flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        paddingBlock: 0,\n        paddingInline: 2,\n        color: (_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.colorTextRightActionsItem,\n        fontSize: '16px',\n        cursor: 'pointer',\n        borderRadius: token.borderRadius,\n        '> *': {\n          paddingInline: 6,\n          paddingBlock: 6,\n          borderRadius: token.borderRadius,\n          '&:hover': {\n            backgroundColor: (_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.header) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.colorBgRightActionsItemHover\n          }\n        }\n      },\n      '&-avatar': {\n        display: 'inline-flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        paddingInlineStart: token.padding,\n        paddingInlineEnd: token.padding,\n        cursor: 'pointer',\n        color: (_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.header) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorTextRightActionsItem,\n        '> div': {\n          height: '44px',\n          color: (_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.header) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorTextRightActionsItem,\n          paddingInline: 8,\n          paddingBlock: 8,\n          cursor: 'pointer',\n          display: 'flex',\n          alignItems: 'center',\n          lineHeight: '44px',\n          borderRadius: token.borderRadius,\n          '&:hover': {\n            backgroundColor: (_token$layout5 = token.layout) === null || _token$layout5 === void 0 || (_token$layout5 = _token$layout5.header) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.colorBgRightActionsItemHover\n          }\n        }\n      }\n    }\n  });\n};\nexport function useStyle(prefixCls) {\n  return useAntdStyle('ProLayoutRightContent', function (token) {\n    var proToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls)\n    });\n    return [genTopNavHeaderStyle(proToken)];\n  });\n}", "import _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"rightContentRender\", \"avatarProps\", \"actionsRender\", \"headerContentRender\"],\n  _excluded2 = [\"title\", \"render\"];\nimport { useDebounceFn } from '@ant-design/pro-utils';\nimport { Avatar, ConfigProvider } from 'antd';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport React, { useContext, useMemo, useState } from 'react';\nimport { useStyle } from \"./rightContentStyle\";\n/**\n * 抽离出来是为了防止 rightSize 经常改变导致菜单 render\n *\n * @param param0\n */\nimport { createElement as _createElement } from \"react\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport var ActionsContent = function ActionsContent(_ref) {\n  var rightContentRender = _ref.rightContentRender,\n    avatarProps = _ref.avatarProps,\n    actionsRender = _ref.actionsRender,\n    headerContentRender = _ref.headerContentRender,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var _useContext = useContext(ConfigProvider.ConfigContext),\n    getPrefixCls = _useContext.getPrefixCls;\n  var prefixCls = \"\".concat(getPrefixCls(), \"-pro-global-header\");\n  var _useStyle = useStyle(prefixCls),\n    wrapSSR = _useStyle.wrapSSR,\n    hashId = _useStyle.hashId;\n  var _useState = useState('auto'),\n    _useState2 = _slicedToArray(_useState, 2),\n    rightSize = _useState2[0],\n    setRightSize = _useState2[1];\n  var avatarDom = useMemo(function () {\n    if (!avatarProps) return null;\n    var title = avatarProps.title,\n      render = avatarProps.render,\n      rest = _objectWithoutProperties(avatarProps, _excluded2);\n    var domList = [rest !== null && rest !== void 0 && rest.src || rest !== null && rest !== void 0 && rest.srcSet || rest.icon || rest.children ? /*#__PURE__*/_createElement(Avatar, _objectSpread(_objectSpread({}, rest), {}, {\n      size: 28,\n      key: \"avatar\"\n    })) : null, title ? /*#__PURE__*/_jsx(\"span\", {\n      style: {\n        marginInlineStart: 8\n      },\n      children: title\n    }, \"name\") : undefined];\n    if (render) {\n      return render(avatarProps, /*#__PURE__*/_jsx(\"div\", {\n        children: domList\n      }), props);\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      children: domList\n    });\n  }, [avatarProps]);\n  var rightActionsRender = actionsRender || avatarDom ? function (restParams) {\n    var doms = actionsRender && (actionsRender === null || actionsRender === void 0 ? void 0 : actionsRender(restParams));\n    if (!doms && !avatarDom) return null;\n    if (!Array.isArray(doms)) return wrapSSR( /*#__PURE__*/_jsxs(\"div\", {\n      className: \"\".concat(prefixCls, \"-header-actions \").concat(hashId).trim(),\n      children: [doms, avatarDom && /*#__PURE__*/_jsx(\"span\", {\n        className: \"\".concat(prefixCls, \"-header-actions-avatar \").concat(hashId).trim(),\n        children: avatarDom\n      })]\n    }));\n    return wrapSSR( /*#__PURE__*/_jsxs(\"div\", {\n      className: \"\".concat(prefixCls, \"-header-actions \").concat(hashId).trim(),\n      children: [doms.filter(Boolean).map(function (dom, index) {\n        var hideHover = false;\n        // 如果配置了 hideHover 就不展示 hover 效果了\n        if ( /*#__PURE__*/React.isValidElement(dom)) {\n          var _dom$props;\n          hideHover = !!(dom !== null && dom !== void 0 && (_dom$props = dom.props) !== null && _dom$props !== void 0 && _dom$props['aria-hidden']);\n        }\n        return /*#__PURE__*/_jsx(\"div\", {\n          className: classNames(\"\".concat(prefixCls, \"-header-actions-item \").concat(hashId), _defineProperty({}, \"\".concat(prefixCls, \"-header-actions-hover\"), !hideHover)),\n          children: dom\n        }, index);\n      }), avatarDom && /*#__PURE__*/_jsx(\"span\", {\n        className: \"\".concat(prefixCls, \"-header-actions-avatar \").concat(hashId).trim(),\n        children: avatarDom\n      })]\n    }));\n  } : undefined;\n  /** 减少一下渲染的次数 */\n  var setRightSizeDebounceFn = useDebounceFn( /*#__PURE__*/function () {\n    var _ref2 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(width) {\n      return _regeneratorRuntime().wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            setRightSize(width);\n          case 1:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee);\n    }));\n    return function (_x) {\n      return _ref2.apply(this, arguments);\n    };\n  }(), 160);\n  var contentRender = rightActionsRender || rightContentRender;\n  return /*#__PURE__*/_jsx(\"div\", {\n    className: \"\".concat(prefixCls, \"-right-content \").concat(hashId).trim(),\n    style: {\n      minWidth: rightSize,\n      height: '100%'\n    },\n    children: /*#__PURE__*/_jsx(\"div\", {\n      style: {\n        height: '100%'\n      },\n      children: /*#__PURE__*/_jsx(ResizeObserver, {\n        onResize: function onResize(_ref3) {\n          var width = _ref3.width;\n          setRightSizeDebounceFn.run(width);\n        },\n        children: contentRender ? /*#__PURE__*/_jsx(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            height: '100%',\n            justifyContent: 'flex-end'\n          },\n          children: contentRender(_objectSpread(_objectSpread({}, props), {}, {\n            // 测试专用\n            //@ts-ignore\n            rightContentSize: rightSize\n          }))\n        }) : null\n      })\n    })\n  });\n};", "import _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport { useCallback, useEffect, useRef } from 'react';\nimport { useRefFunction } from \"../useRefFunction\";\n/**\n * 一个去抖的 hook，传入一个 function，返回一个去抖后的 function\n * @param  {(...args:T) => Promise<any>} fn\n * @param  {number} wait?\n */\nexport function useDebounceFn(fn, wait) {\n  var callback = useRefFunction(fn);\n  var timer = useRef();\n  var cancel = useCallback(function () {\n    if (timer.current) {\n      clearTimeout(timer.current);\n      timer.current = null;\n    }\n  }, []);\n  var run = useCallback( /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n    var _len,\n      args,\n      _key,\n      _args2 = arguments;\n    return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n      while (1) switch (_context2.prev = _context2.next) {\n        case 0:\n          for (_len = _args2.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n            args[_key] = _args2[_key];\n          }\n          if (!(wait === 0 || wait === undefined)) {\n            _context2.next = 3;\n            break;\n          }\n          return _context2.abrupt(\"return\", callback.apply(void 0, args));\n        case 3:\n          cancel();\n          return _context2.abrupt(\"return\", new Promise(function (resolve) {\n            timer.current = setTimeout( /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n              return _regeneratorRuntime().wrap(function _callee$(_context) {\n                while (1) switch (_context.prev = _context.next) {\n                  case 0:\n                    _context.t0 = resolve;\n                    _context.next = 3;\n                    return callback.apply(void 0, args);\n                  case 3:\n                    _context.t1 = _context.sent;\n                    (0, _context.t0)(_context.t1);\n                    return _context.abrupt(\"return\");\n                  case 6:\n                  case \"end\":\n                    return _context.stop();\n                }\n              }, _callee);\n            })), wait);\n          }));\n        case 5:\n        case \"end\":\n          return _context2.stop();\n      }\n    }, _callee2);\n  })), [callback, cancel, wait]);\n  useEffect(function () {\n    return cancel;\n  }, [cancel]);\n  return {\n    run: run,\n    cancel: cancel\n  };\n}", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nvar genTopNavHeaderStyle = function genTopNavHeaderStyle(token) {\n  var _token$layout, _token$layout2;\n  return _defineProperty({}, token.componentCls, {\n    position: 'relative',\n    width: '100%',\n    height: '100%',\n    backgroundColor: 'transparent',\n    '.anticon': {\n      color: 'inherit'\n    },\n    '&-main': {\n      display: 'flex',\n      height: '100%',\n      paddingInlineStart: '16px',\n      '&-left': _defineProperty({\n        display: 'flex',\n        alignItems: 'center'\n      }, \"\".concat(token.proComponentsCls, \"-layout-apps-icon\"), {\n        marginInlineEnd: 16,\n        marginInlineStart: -8\n      })\n    },\n    '&-wide': {\n      maxWidth: 1152,\n      margin: '0 auto'\n    },\n    '&-logo': {\n      position: 'relative',\n      display: 'flex',\n      height: '100%',\n      alignItems: 'center',\n      overflow: 'hidden',\n      '> *:first-child': {\n        display: 'flex',\n        alignItems: 'center',\n        minHeight: '22px',\n        fontSize: '22px'\n      },\n      '> *:first-child > img': {\n        display: 'inline-block',\n        height: '32px',\n        verticalAlign: 'middle'\n      },\n      '> *:first-child > h1': {\n        display: 'inline-block',\n        marginBlock: 0,\n        marginInline: 0,\n        lineHeight: '24px',\n        marginInlineStart: 6,\n        fontWeight: '600',\n        fontSize: '16px',\n        color: (_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.colorHeaderTitle,\n        verticalAlign: 'top'\n      }\n    },\n    '&-menu': {\n      minWidth: 0,\n      display: 'flex',\n      alignItems: 'center',\n      paddingInline: 6,\n      paddingBlock: 6,\n      lineHeight: \"\".concat(Math.max((((_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.header) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.heightLayoutHeader) || 56) - 12, 40), \"px\")\n    }\n  });\n};\nexport function useStyle(prefixCls) {\n  return useAntdStyle('ProLayoutTopNavHeader', function (token) {\n    var topNavHeaderToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls)\n    });\n    return [genTopNavHeaderStyle(topNavHeaderToken)];\n  });\n}", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { isNeedOpenHash, ProProvider } from '@ant-design/pro-provider';\nimport { coverToNewToken } from '@ant-design/pro-utils';\nimport { ConfigProvider } from 'antd';\nimport classNames from 'classnames';\nimport React, { useContext, useMemo, useRef } from 'react';\nimport { AppsLogoComponents } from \"../AppsLogoComponents\";\nimport { ActionsContent } from \"../GlobalHeader/ActionsContent\";\nimport { BaseMenu } from \"../SiderMenu/BaseMenu\";\nimport { renderLogoAndTitle } from \"../SiderMenu/SiderMenu\";\nimport { useStyle } from \"./style\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nvar TopNavHeader = function TopNavHeader(props) {\n  var _token$layout13, _token$layout14, _token$layout15, _token$layout16, _token$layout17, _token$layout18, _token$layout19;\n  var ref = useRef(null);\n  var onMenuHeaderClick = props.onMenuHeaderClick,\n    contentWidth = props.contentWidth,\n    rightContentRender = props.rightContentRender,\n    propsClassName = props.className,\n    style = props.style,\n    headerContentRender = props.headerContentRender,\n    layout = props.layout,\n    actionsRender = props.actionsRender;\n  var _useContext = useContext(ConfigProvider.ConfigContext),\n    getPrefixCls = _useContext.getPrefixCls;\n  var _useContext2 = useContext(ProProvider),\n    dark = _useContext2.dark;\n  var prefixCls = \"\".concat(props.prefixCls || getPrefixCls('pro'), \"-top-nav-header\");\n  var _useStyle = useStyle(prefixCls),\n    wrapSSR = _useStyle.wrapSSR,\n    hashId = _useStyle.hashId;\n  var renderKey = undefined;\n  if (props.menuHeaderRender !== undefined) {\n    renderKey = 'menuHeaderRender';\n  } else if (layout === 'mix' || layout === 'top') {\n    renderKey = 'headerTitleRender';\n  }\n  var headerDom = renderLogoAndTitle(_objectSpread(_objectSpread({}, props), {}, {\n    collapsed: false\n  }), renderKey);\n  var _useContext3 = useContext(ProProvider),\n    token = _useContext3.token;\n  var contentDom = useMemo(function () {\n    var _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout5, _token$layout6, _token$layout7, _token$layout8, _token$layout9, _token$layout10, _token$layout11, _token$layout12, _props$menuProps;\n    var defaultDom = /*#__PURE__*/_jsx(ConfigProvider // @ts-ignore\n    , {\n      theme: {\n        hashed: isNeedOpenHash(),\n        components: {\n          Layout: {\n            headerBg: 'transparent',\n            bodyBg: 'transparent'\n          },\n          Menu: _objectSpread({}, coverToNewToken({\n            colorItemBg: ((_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.colorBgHeader) || 'transparent',\n            colorSubItemBg: ((_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.header) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.colorBgHeader) || 'transparent',\n            radiusItem: token.borderRadius,\n            colorItemBgSelected: ((_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.header) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorBgMenuItemSelected) || (token === null || token === void 0 ? void 0 : token.colorBgTextHover),\n            itemHoverBg: ((_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.header) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorBgMenuItemHover) || (token === null || token === void 0 ? void 0 : token.colorBgTextHover),\n            colorItemBgSelectedHorizontal: ((_token$layout5 = token.layout) === null || _token$layout5 === void 0 || (_token$layout5 = _token$layout5.header) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.colorBgMenuItemSelected) || (token === null || token === void 0 ? void 0 : token.colorBgTextHover),\n            colorActiveBarWidth: 0,\n            colorActiveBarHeight: 0,\n            colorActiveBarBorderSize: 0,\n            colorItemText: ((_token$layout6 = token.layout) === null || _token$layout6 === void 0 || (_token$layout6 = _token$layout6.header) === null || _token$layout6 === void 0 ? void 0 : _token$layout6.colorTextMenu) || (token === null || token === void 0 ? void 0 : token.colorTextSecondary),\n            colorItemTextHoverHorizontal: ((_token$layout7 = token.layout) === null || _token$layout7 === void 0 || (_token$layout7 = _token$layout7.header) === null || _token$layout7 === void 0 ? void 0 : _token$layout7.colorTextMenuActive) || (token === null || token === void 0 ? void 0 : token.colorText),\n            colorItemTextSelectedHorizontal: ((_token$layout8 = token.layout) === null || _token$layout8 === void 0 || (_token$layout8 = _token$layout8.header) === null || _token$layout8 === void 0 ? void 0 : _token$layout8.colorTextMenuSelected) || (token === null || token === void 0 ? void 0 : token.colorTextBase),\n            horizontalItemBorderRadius: 4,\n            colorItemTextHover: ((_token$layout9 = token.layout) === null || _token$layout9 === void 0 || (_token$layout9 = _token$layout9.header) === null || _token$layout9 === void 0 ? void 0 : _token$layout9.colorTextMenuActive) || 'rgba(0, 0, 0, 0.85)',\n            horizontalItemHoverBg: ((_token$layout10 = token.layout) === null || _token$layout10 === void 0 || (_token$layout10 = _token$layout10.header) === null || _token$layout10 === void 0 ? void 0 : _token$layout10.colorBgMenuItemHover) || 'rgba(0, 0, 0, 0.04)',\n            colorItemTextSelected: ((_token$layout11 = token.layout) === null || _token$layout11 === void 0 || (_token$layout11 = _token$layout11.header) === null || _token$layout11 === void 0 ? void 0 : _token$layout11.colorTextMenuSelected) || 'rgba(0, 0, 0, 1)',\n            popupBg: token === null || token === void 0 ? void 0 : token.colorBgElevated,\n            subMenuItemBg: token === null || token === void 0 ? void 0 : token.colorBgElevated,\n            darkSubMenuItemBg: 'transparent',\n            darkPopupBg: token === null || token === void 0 ? void 0 : token.colorBgElevated\n          }))\n        },\n        token: {\n          colorBgElevated: ((_token$layout12 = token.layout) === null || _token$layout12 === void 0 || (_token$layout12 = _token$layout12.header) === null || _token$layout12 === void 0 ? void 0 : _token$layout12.colorBgHeader) || 'transparent'\n        }\n      },\n      children: /*#__PURE__*/_jsx(BaseMenu, _objectSpread(_objectSpread(_objectSpread({\n        theme: dark ? 'dark' : 'light'\n      }, props), {}, {\n        className: \"\".concat(prefixCls, \"-base-menu \").concat(hashId).trim()\n      }, props.menuProps), {}, {\n        style: _objectSpread({\n          width: '100%'\n        }, (_props$menuProps = props.menuProps) === null || _props$menuProps === void 0 ? void 0 : _props$menuProps.style),\n        collapsed: false,\n        menuRenderType: \"header\",\n        mode: \"horizontal\"\n      }))\n    });\n    if (headerContentRender) {\n      return headerContentRender(props, defaultDom);\n    }\n    return defaultDom;\n  }, [(_token$layout13 = token.layout) === null || _token$layout13 === void 0 || (_token$layout13 = _token$layout13.header) === null || _token$layout13 === void 0 ? void 0 : _token$layout13.colorBgHeader, (_token$layout14 = token.layout) === null || _token$layout14 === void 0 || (_token$layout14 = _token$layout14.header) === null || _token$layout14 === void 0 ? void 0 : _token$layout14.colorBgMenuItemSelected, (_token$layout15 = token.layout) === null || _token$layout15 === void 0 || (_token$layout15 = _token$layout15.header) === null || _token$layout15 === void 0 ? void 0 : _token$layout15.colorBgMenuItemHover, (_token$layout16 = token.layout) === null || _token$layout16 === void 0 || (_token$layout16 = _token$layout16.header) === null || _token$layout16 === void 0 ? void 0 : _token$layout16.colorTextMenu, (_token$layout17 = token.layout) === null || _token$layout17 === void 0 || (_token$layout17 = _token$layout17.header) === null || _token$layout17 === void 0 ? void 0 : _token$layout17.colorTextMenuActive, (_token$layout18 = token.layout) === null || _token$layout18 === void 0 || (_token$layout18 = _token$layout18.header) === null || _token$layout18 === void 0 ? void 0 : _token$layout18.colorTextMenuSelected, (_token$layout19 = token.layout) === null || _token$layout19 === void 0 || (_token$layout19 = _token$layout19.header) === null || _token$layout19 === void 0 ? void 0 : _token$layout19.colorBgMenuElevated, token.borderRadius, token === null || token === void 0 ? void 0 : token.colorBgTextHover, token === null || token === void 0 ? void 0 : token.colorTextSecondary, token === null || token === void 0 ? void 0 : token.colorText, token === null || token === void 0 ? void 0 : token.colorTextBase, token.colorBgElevated, dark, props, prefixCls, hashId, headerContentRender]);\n  return wrapSSR( /*#__PURE__*/_jsx(\"div\", {\n    className: classNames(prefixCls, hashId, propsClassName, _defineProperty({}, \"\".concat(prefixCls, \"-light\"), true)),\n    style: style,\n    children: /*#__PURE__*/_jsxs(\"div\", {\n      ref: ref,\n      className: classNames(\"\".concat(prefixCls, \"-main\"), hashId, _defineProperty({}, \"\".concat(prefixCls, \"-wide\"), contentWidth === 'Fixed' && layout === 'top')),\n      children: [headerDom && /*#__PURE__*/_jsxs(\"div\", {\n        className: classNames(\"\".concat(prefixCls, \"-main-left \").concat(hashId)),\n        onClick: onMenuHeaderClick,\n        children: [/*#__PURE__*/_jsx(AppsLogoComponents, _objectSpread({}, props)), /*#__PURE__*/_jsx(\"div\", {\n          className: \"\".concat(prefixCls, \"-logo \").concat(hashId).trim(),\n          id: \"logo\",\n          children: headerDom\n        }, \"logo\")]\n      }), /*#__PURE__*/_jsx(\"div\", {\n        style: {\n          flex: 1\n        },\n        className: \"\".concat(prefixCls, \"-menu \").concat(hashId).trim(),\n        children: contentDom\n      }), (rightContentRender || actionsRender || props.avatarProps) && /*#__PURE__*/_jsx(ActionsContent, _objectSpread(_objectSpread({\n        rightContentRender: rightContentRender\n      }, props), {}, {\n        prefixCls: prefixCls\n      }))]\n    })\n  }));\n};\nexport { TopNavHeader };", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nvar genGlobalHeaderStyle = function genGlobalHeaderStyle(token) {\n  var _token$layout, _token$layout2, _token$layout3;\n  return _defineProperty({}, token.componentCls, _defineProperty(_defineProperty(_defineProperty(_defineProperty({\n    position: 'relative',\n    background: 'transparent',\n    display: 'flex',\n    alignItems: 'center',\n    marginBlock: 0,\n    marginInline: 16,\n    height: ((_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.heightLayoutHeader) || 56,\n    boxSizing: 'border-box',\n    '> a': {\n      height: '100%'\n    }\n  }, \"\".concat(token.proComponentsCls, \"-layout-apps-icon\"), {\n    marginInlineEnd: 16\n  }), '&-collapsed-button', {\n    minHeight: '22px',\n    color: (_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.header) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.colorHeaderTitle,\n    fontSize: '18px',\n    marginInlineEnd: '16px'\n  }), '&-logo', {\n    position: 'relative',\n    marginInlineEnd: '16px',\n    a: {\n      display: 'flex',\n      alignItems: 'center',\n      height: '100%',\n      minHeight: '22px',\n      fontSize: '20px'\n    },\n    img: {\n      height: '28px'\n    },\n    h1: {\n      height: '32px',\n      marginBlock: 0,\n      marginInline: 0,\n      marginInlineStart: 8,\n      fontWeight: '600',\n      color: ((_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.header) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorHeaderTitle) || token.colorTextHeading,\n      fontSize: '18px',\n      lineHeight: '32px'\n    },\n    '&-mix': {\n      display: 'flex',\n      alignItems: 'center'\n    }\n  }), '&-logo-mobile', {\n    minWidth: '24px',\n    marginInlineEnd: 0\n  }));\n};\nexport function useStyle(prefixCls) {\n  return useAntdStyle('ProLayoutGlobalHeader', function (token) {\n    var GlobalHeaderToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls)\n    });\n    return [genGlobalHeaderStyle(GlobalHeaderToken)];\n  });\n}", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { MenuOutlined } from '@ant-design/icons';\nimport { ConfigProvider } from 'antd';\nimport classNames from 'classnames';\nimport React, { useContext } from 'react';\nimport { clearMenuItem } from \"../../utils/utils\";\nimport { AppsLogoComponents, defaultRenderLogo } from \"../AppsLogoComponents\";\nimport { renderLogoAndTitle } from \"../SiderMenu/SiderMenu\";\nimport { TopNavHeader } from \"../TopNavHeader\";\nimport { ActionsContent } from \"./ActionsContent\";\nimport { useStyle } from \"./style\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nvar renderLogo = function renderLogo(menuHeaderRender, logoDom) {\n  if (menuHeaderRender === false) {\n    return null;\n  }\n  if (menuHeaderRender) {\n    return menuHeaderRender(logoDom, null);\n  }\n  return logoDom;\n};\nvar GlobalHeader = function GlobalHeader(props) {\n  var isMobile = props.isMobile,\n    logo = props.logo,\n    collapsed = props.collapsed,\n    onCollapse = props.onCollapse,\n    rightContentRender = props.rightContentRender,\n    menuHeaderRender = props.menuHeaderRender,\n    onMenuHeaderClick = props.onMenuHeaderClick,\n    propClassName = props.className,\n    style = props.style,\n    layout = props.layout,\n    children = props.children,\n    splitMenus = props.splitMenus,\n    menuData = props.menuData,\n    prefixCls = props.prefixCls;\n  var _useContext = useContext(ConfigProvider.ConfigContext),\n    getPrefixCls = _useContext.getPrefixCls,\n    direction = _useContext.direction;\n  var baseClassName = \"\".concat(prefixCls || getPrefixCls('pro'), \"-global-header\");\n  var _useStyle = useStyle(baseClassName),\n    wrapSSR = _useStyle.wrapSSR,\n    hashId = _useStyle.hashId;\n  var className = classNames(propClassName, baseClassName, hashId);\n  if (layout === 'mix' && !isMobile && splitMenus) {\n    var noChildrenMenuData = (menuData || []).map(function (item) {\n      return _objectSpread(_objectSpread({}, item), {}, {\n        children: undefined,\n        routes: undefined\n      });\n    });\n    var clearMenuData = clearMenuItem(noChildrenMenuData);\n    return /*#__PURE__*/_jsx(TopNavHeader, _objectSpread(_objectSpread({\n      mode: \"horizontal\"\n    }, props), {}, {\n      splitMenus: false,\n      menuData: clearMenuData\n    }));\n  }\n  var logoClassNames = classNames(\"\".concat(baseClassName, \"-logo\"), hashId, _defineProperty(_defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-logo-rtl\"), direction === 'rtl'), \"\".concat(baseClassName, \"-logo-mix\"), layout === 'mix'), \"\".concat(baseClassName, \"-logo-mobile\"), isMobile));\n  var logoDom = /*#__PURE__*/_jsx(\"span\", {\n    className: logoClassNames,\n    children: /*#__PURE__*/_jsx(\"a\", {\n      children: defaultRenderLogo(logo)\n    })\n  }, \"logo\");\n  return wrapSSR( /*#__PURE__*/_jsxs(\"div\", {\n    className: className,\n    style: _objectSpread({}, style),\n    children: [isMobile && /*#__PURE__*/_jsx(\"span\", {\n      className: \"\".concat(baseClassName, \"-collapsed-button \").concat(hashId).trim(),\n      onClick: function onClick() {\n        onCollapse === null || onCollapse === void 0 || onCollapse(!collapsed);\n      },\n      children: /*#__PURE__*/_jsx(MenuOutlined, {})\n    }), isMobile && renderLogo(menuHeaderRender, logoDom), layout === 'mix' && !isMobile && /*#__PURE__*/_jsxs(_Fragment, {\n      children: [/*#__PURE__*/_jsx(AppsLogoComponents, _objectSpread({}, props)), /*#__PURE__*/_jsx(\"div\", {\n        className: logoClassNames,\n        onClick: onMenuHeaderClick,\n        children: renderLogoAndTitle(_objectSpread(_objectSpread({}, props), {}, {\n          collapsed: false\n        }), 'headerTitleRender')\n      })]\n    }), /*#__PURE__*/_jsx(\"div\", {\n      style: {\n        flex: 1\n      },\n      children: children\n    }), (rightContentRender || props.actionsRender || props.avatarProps) && /*#__PURE__*/_jsx(ActionsContent, _objectSpread({\n      rightContentRender: rightContentRender\n    }, props))]\n  }));\n};\nexport { GlobalHeader };", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nvar genProLayoutHeaderStyle = function genProLayoutHeaderStyle(token) {\n  var _token$layout, _token$layout2, _token$layout3, _token$layout4;\n  return _defineProperty({}, \"\".concat(token.proComponentsCls, \"-layout\"), _defineProperty({}, \"\".concat(token.antCls, \"-layout-header\").concat(token.componentCls), {\n    height: ((_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.heightLayoutHeader) || 56,\n    lineHeight: \"\".concat(((_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.header) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.heightLayoutHeader) || 56, \"px\"),\n    // hitu 用了这个属性，不能删除哦 @南取\n    zIndex: 19,\n    width: '100%',\n    paddingBlock: 0,\n    paddingInline: 0,\n    borderBlockEnd: \"1px solid \".concat(token.colorSplit),\n    backgroundColor: ((_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.header) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorBgHeader) || 'rgba(255, 255, 255, 0.4)',\n    WebkitBackdropFilter: 'blur(8px)',\n    backdropFilter: 'blur(8px)',\n    transition: 'background-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)',\n    '&-fixed-header': {\n      position: 'fixed',\n      insetBlockStart: 0,\n      width: '100%',\n      zIndex: 100,\n      insetInlineEnd: 0\n    },\n    '&-fixed-header-scroll': {\n      backgroundColor: ((_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.header) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorBgScrollHeader) || 'rgba(255, 255, 255, 0.8)'\n    },\n    '&-header-actions': {\n      display: 'flex',\n      alignItems: 'center',\n      fontSize: '16',\n      cursor: 'pointer',\n      '& &-item': {\n        paddingBlock: 0,\n        paddingInline: 8,\n        '&:hover': {\n          color: token.colorText\n        }\n      }\n    },\n    '&-header-realDark': {\n      boxShadow: '0 2px 8px 0 rgba(0, 0, 0, 65%)'\n    },\n    '&-header-actions-header-action': {\n      transition: 'width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)'\n    }\n  }));\n};\nexport function useStyle(prefixCls) {\n  return useAntdStyle('ProLayoutHeader', function (token) {\n    var ProLayoutHeaderToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls)\n    });\n    return [genProLayoutHeaderStyle(ProLayoutHeaderToken)];\n  });\n}", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { isNeedOpenHash, ProProvider } from '@ant-design/pro-provider';\nimport { ConfigProvider, Layout } from 'antd';\nimport classNames from 'classnames';\nimport React, { useCallback, useContext, useEffect, useState } from 'react';\nimport { clearMenuItem } from \"../../utils/utils\";\nimport { GlobalHeader } from \"../GlobalHeader\";\nimport { TopNavHeader } from \"../TopNavHeader\";\nimport { useStyle } from \"./style/header\";\nimport { useStylish } from \"./style/stylish\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nvar Header = Layout.Header;\nvar DefaultHeader = function DefaultHeader(props) {\n  var _token$layout2, _token$layout3, _token$layout4;\n  var isMobile = props.isMobile,\n    fixedHeader = props.fixedHeader,\n    propsClassName = props.className,\n    style = props.style,\n    collapsed = props.collapsed,\n    prefixCls = props.prefixCls,\n    onCollapse = props.onCollapse,\n    layout = props.layout,\n    headerRender = props.headerRender,\n    headerContentRender = props.headerContentRender;\n  var _useContext = useContext(ProProvider),\n    token = _useContext.token;\n  var context = useContext(ConfigProvider.ConfigContext);\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    isFixedHeaderScroll = _useState2[0],\n    setIsFixedHeaderScroll = _useState2[1];\n  var needFixedHeader = fixedHeader || layout === 'mix';\n  var renderContent = useCallback(function () {\n    var isTop = layout === 'top';\n    var clearMenuData = clearMenuItem(props.menuData || []);\n    var defaultDom = /*#__PURE__*/_jsx(GlobalHeader, _objectSpread(_objectSpread({\n      onCollapse: onCollapse\n    }, props), {}, {\n      menuData: clearMenuData,\n      children: headerContentRender && headerContentRender(props, null)\n    }));\n    if (isTop && !isMobile) {\n      defaultDom = /*#__PURE__*/_jsx(TopNavHeader, _objectSpread(_objectSpread({\n        mode: \"horizontal\",\n        onCollapse: onCollapse\n      }, props), {}, {\n        menuData: clearMenuData\n      }));\n    }\n    if (headerRender && typeof headerRender === 'function') {\n      return headerRender(props, defaultDom);\n    }\n    return defaultDom;\n  }, [headerContentRender, headerRender, isMobile, layout, onCollapse, props]);\n  useEffect(function () {\n    var _context$getTargetCon;\n    var dom = (context === null || context === void 0 || (_context$getTargetCon = context.getTargetContainer) === null || _context$getTargetCon === void 0 ? void 0 : _context$getTargetCon.call(context)) || document.body;\n    var isFixedHeaderFn = function isFixedHeaderFn() {\n      var _token$layout;\n      var scrollTop = dom.scrollTop;\n      if (scrollTop > (((_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.heightLayoutHeader) || 56) && !isFixedHeaderScroll) {\n        setIsFixedHeaderScroll(true);\n        return true;\n      }\n      if (isFixedHeaderScroll) {\n        setIsFixedHeaderScroll(false);\n      }\n      return false;\n    };\n    if (!needFixedHeader) return;\n    if (typeof window === 'undefined') return;\n    dom.addEventListener('scroll', isFixedHeaderFn, {\n      passive: true\n    });\n    return function () {\n      dom.removeEventListener('scroll', isFixedHeaderFn);\n    };\n  }, [(_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.header) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.heightLayoutHeader, needFixedHeader, isFixedHeaderScroll]);\n  var isTop = layout === 'top';\n  var baseClassName = \"\".concat(prefixCls, \"-layout-header\");\n  var _useStyle = useStyle(baseClassName),\n    wrapSSR = _useStyle.wrapSSR,\n    hashId = _useStyle.hashId;\n  var stylish = useStylish(\"\".concat(baseClassName, \".\").concat(baseClassName, \"-stylish\"), {\n    proLayoutCollapsedWidth: 64,\n    stylish: props.stylish\n  });\n  var className = classNames(propsClassName, hashId, baseClassName, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-fixed-header\"), needFixedHeader), \"\".concat(baseClassName, \"-fixed-header-scroll\"), isFixedHeaderScroll), \"\".concat(baseClassName, \"-mix\"), layout === 'mix'), \"\".concat(baseClassName, \"-fixed-header-action\"), !collapsed), \"\".concat(baseClassName, \"-top-menu\"), isTop), \"\".concat(baseClassName, \"-header\"), true), \"\".concat(baseClassName, \"-stylish\"), !!props.stylish));\n  if (layout === 'side' && !isMobile) return null;\n  return stylish.wrapSSR(wrapSSR( /*#__PURE__*/_jsx(_Fragment, {\n    children: /*#__PURE__*/_jsxs(ConfigProvider\n    // @ts-ignore\n    , {\n      theme: {\n        hashed: isNeedOpenHash(),\n        components: {\n          Layout: {\n            headerBg: 'transparent',\n            bodyBg: 'transparent'\n          }\n        }\n      },\n      children: [needFixedHeader && /*#__PURE__*/_jsx(Header, {\n        style: _objectSpread({\n          height: ((_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.header) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.heightLayoutHeader) || 56,\n          lineHeight: \"\".concat(((_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.header) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.heightLayoutHeader) || 56, \"px\"),\n          backgroundColor: 'transparent',\n          zIndex: 19\n        }, style)\n      }), /*#__PURE__*/_jsx(Header, {\n        className: className,\n        style: style,\n        children: renderContent()\n      })]\n    })\n  })));\n};\nexport { DefaultHeader };", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nexport function useStylish(prefixCls, _ref) {\n  var stylish = _ref.stylish,\n    proLayoutCollapsedWidth = _ref.proLayoutCollapsedWidth;\n  return useAntdStyle('ProLayoutHeaderStylish', function (token) {\n    var stylishToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls),\n      proLayoutCollapsedWidth: proLayoutCollapsedWidth\n    });\n    if (!stylish) return [];\n    return [_defineProperty({}, \"div\".concat(token.proComponentsCls, \"-layout\"), _defineProperty({}, \"\".concat(stylishToken.componentCls), stylish === null || stylish === void 0 ? void 0 : stylish(stylishToken)))];\n  });\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"className\", \"containerRef\"];\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { RefContext } from \"./context\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { useComposeRef } from \"rc-util/es/ref\";\nvar DrawerPanel = function DrawerPanel(props) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    containerRef = props.containerRef,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(RefContext),\n    panelRef = _React$useContext.panel;\n  var mergedRef = useComposeRef(panelRef, containerRef);\n\n  // =============================== Render ===============================\n\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classNames(\"\".concat(prefixCls, \"-content\"), className),\n    role: \"dialog\",\n    ref: mergedRef\n  }, pickAttrs(props, {\n    aria: true\n  }), {\n    \"aria-modal\": \"true\"\n  }, restProps));\n};\nif (process.env.NODE_ENV !== 'production') {\n  DrawerPanel.displayName = 'DrawerPanel';\n}\nexport default DrawerPanel;", "import warning from \"rc-util/es/warning\";\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nexport function parseWidthHeight(value) {\n  if (typeof value === 'string' && String(Number(value)) === value) {\n    warning(false, 'Invalid value type of `width` or `height` which should be number type instead.');\n    return Number(value);\n  }\n  return value;\n}\nexport function warnCheck(props) {\n  warning(!('wrapperClassName' in props), \"'wrapperClassName' is removed. Please use 'rootClassName' instead.\");\n  warning(canUseDom() || !props.open, \"Drawer with 'open' in SSR is not work since no place to createPortal. Please move to 'useEffect' instead.\");\n}", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport * as React from 'react';\nimport DrawerContext from \"./context\";\nimport DrawerPanel from \"./DrawerPanel\";\nimport { parseWidthHeight } from \"./util\";\nvar sentinelStyle = {\n  width: 0,\n  height: 0,\n  overflow: 'hidden',\n  outline: 'none',\n  position: 'absolute'\n};\nfunction DrawerPopup(props, ref) {\n  var _ref, _pushConfig$distance, _pushConfig;\n  var prefixCls = props.prefixCls,\n    open = props.open,\n    placement = props.placement,\n    inline = props.inline,\n    push = props.push,\n    forceRender = props.forceRender,\n    autoFocus = props.autoFocus,\n    keyboard = props.keyboard,\n    drawerClassNames = props.classNames,\n    rootClassName = props.rootClassName,\n    rootStyle = props.rootStyle,\n    zIndex = props.zIndex,\n    className = props.className,\n    id = props.id,\n    style = props.style,\n    motion = props.motion,\n    width = props.width,\n    height = props.height,\n    children = props.children,\n    mask = props.mask,\n    maskClosable = props.maskClosable,\n    maskMotion = props.maskMotion,\n    maskClassName = props.maskClassName,\n    maskStyle = props.maskStyle,\n    afterOpenChange = props.afterOpenChange,\n    onClose = props.onClose,\n    onMouseEnter = props.onMouseEnter,\n    onMouseOver = props.onMouseOver,\n    onMouseLeave = props.onMouseLeave,\n    onClick = props.onClick,\n    onKeyDown = props.onKeyDown,\n    onKeyUp = props.onKeyUp,\n    styles = props.styles,\n    drawerRender = props.drawerRender;\n\n  // ================================ Refs ================================\n  var panelRef = React.useRef();\n  var sentinelStartRef = React.useRef();\n  var sentinelEndRef = React.useRef();\n  React.useImperativeHandle(ref, function () {\n    return panelRef.current;\n  });\n  var onPanelKeyDown = function onPanelKeyDown(event) {\n    var keyCode = event.keyCode,\n      shiftKey = event.shiftKey;\n    switch (keyCode) {\n      // Tab active\n      case KeyCode.TAB:\n        {\n          if (keyCode === KeyCode.TAB) {\n            if (!shiftKey && document.activeElement === sentinelEndRef.current) {\n              var _sentinelStartRef$cur;\n              (_sentinelStartRef$cur = sentinelStartRef.current) === null || _sentinelStartRef$cur === void 0 || _sentinelStartRef$cur.focus({\n                preventScroll: true\n              });\n            } else if (shiftKey && document.activeElement === sentinelStartRef.current) {\n              var _sentinelEndRef$curre;\n              (_sentinelEndRef$curre = sentinelEndRef.current) === null || _sentinelEndRef$curre === void 0 || _sentinelEndRef$curre.focus({\n                preventScroll: true\n              });\n            }\n          }\n          break;\n        }\n\n      // Close\n      case KeyCode.ESC:\n        {\n          if (onClose && keyboard) {\n            event.stopPropagation();\n            onClose(event);\n          }\n          break;\n        }\n    }\n  };\n\n  // ========================== Control ===========================\n  // Auto Focus\n  React.useEffect(function () {\n    if (open && autoFocus) {\n      var _panelRef$current;\n      (_panelRef$current = panelRef.current) === null || _panelRef$current === void 0 || _panelRef$current.focus({\n        preventScroll: true\n      });\n    }\n  }, [open]);\n\n  // ============================ Push ============================\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    pushed = _React$useState2[0],\n    setPushed = _React$useState2[1];\n  var parentContext = React.useContext(DrawerContext);\n\n  // Merge push distance\n  var pushConfig;\n  if (typeof push === 'boolean') {\n    pushConfig = push ? {} : {\n      distance: 0\n    };\n  } else {\n    pushConfig = push || {};\n  }\n  var pushDistance = (_ref = (_pushConfig$distance = (_pushConfig = pushConfig) === null || _pushConfig === void 0 ? void 0 : _pushConfig.distance) !== null && _pushConfig$distance !== void 0 ? _pushConfig$distance : parentContext === null || parentContext === void 0 ? void 0 : parentContext.pushDistance) !== null && _ref !== void 0 ? _ref : 180;\n  var mergedContext = React.useMemo(function () {\n    return {\n      pushDistance: pushDistance,\n      push: function push() {\n        setPushed(true);\n      },\n      pull: function pull() {\n        setPushed(false);\n      }\n    };\n  }, [pushDistance]);\n\n  // ========================= ScrollLock =========================\n  // Tell parent to push\n  React.useEffect(function () {\n    if (open) {\n      var _parentContext$push;\n      parentContext === null || parentContext === void 0 || (_parentContext$push = parentContext.push) === null || _parentContext$push === void 0 || _parentContext$push.call(parentContext);\n    } else {\n      var _parentContext$pull;\n      parentContext === null || parentContext === void 0 || (_parentContext$pull = parentContext.pull) === null || _parentContext$pull === void 0 || _parentContext$pull.call(parentContext);\n    }\n  }, [open]);\n\n  // Clean up\n  React.useEffect(function () {\n    return function () {\n      var _parentContext$pull2;\n      parentContext === null || parentContext === void 0 || (_parentContext$pull2 = parentContext.pull) === null || _parentContext$pull2 === void 0 || _parentContext$pull2.call(parentContext);\n    };\n  }, []);\n\n  // ============================ Mask ============================\n  var maskNode = /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    key: \"mask\"\n  }, maskMotion, {\n    visible: mask && open\n  }), function (_ref2, maskRef) {\n    var motionMaskClassName = _ref2.className,\n      motionMaskStyle = _ref2.style;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(\"\".concat(prefixCls, \"-mask\"), motionMaskClassName, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.mask, maskClassName),\n      style: _objectSpread(_objectSpread(_objectSpread({}, motionMaskStyle), maskStyle), styles === null || styles === void 0 ? void 0 : styles.mask),\n      onClick: maskClosable && open ? onClose : undefined,\n      ref: maskRef\n    });\n  });\n\n  // =========================== Panel ============================\n  var motionProps = typeof motion === 'function' ? motion(placement) : motion;\n  var wrapperStyle = {};\n  if (pushed && pushDistance) {\n    switch (placement) {\n      case 'top':\n        wrapperStyle.transform = \"translateY(\".concat(pushDistance, \"px)\");\n        break;\n      case 'bottom':\n        wrapperStyle.transform = \"translateY(\".concat(-pushDistance, \"px)\");\n        break;\n      case 'left':\n        wrapperStyle.transform = \"translateX(\".concat(pushDistance, \"px)\");\n        break;\n      default:\n        wrapperStyle.transform = \"translateX(\".concat(-pushDistance, \"px)\");\n        break;\n    }\n  }\n  if (placement === 'left' || placement === 'right') {\n    wrapperStyle.width = parseWidthHeight(width);\n  } else {\n    wrapperStyle.height = parseWidthHeight(height);\n  }\n  var eventHandlers = {\n    onMouseEnter: onMouseEnter,\n    onMouseOver: onMouseOver,\n    onMouseLeave: onMouseLeave,\n    onClick: onClick,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp\n  };\n  var panelNode = /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    key: \"panel\"\n  }, motionProps, {\n    visible: open,\n    forceRender: forceRender,\n    onVisibleChanged: function onVisibleChanged(nextVisible) {\n      afterOpenChange === null || afterOpenChange === void 0 || afterOpenChange(nextVisible);\n    },\n    removeOnLeave: false,\n    leavedClassName: \"\".concat(prefixCls, \"-content-wrapper-hidden\")\n  }), function (_ref3, motionRef) {\n    var motionClassName = _ref3.className,\n      motionStyle = _ref3.style;\n    var content = /*#__PURE__*/React.createElement(DrawerPanel, _extends({\n      id: id,\n      containerRef: motionRef,\n      prefixCls: prefixCls,\n      className: classNames(className, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.content),\n      style: _objectSpread(_objectSpread({}, style), styles === null || styles === void 0 ? void 0 : styles.content)\n    }, pickAttrs(props, {\n      aria: true\n    }), eventHandlers), children);\n    return /*#__PURE__*/React.createElement(\"div\", _extends({\n      className: classNames(\"\".concat(prefixCls, \"-content-wrapper\"), drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.wrapper, motionClassName),\n      style: _objectSpread(_objectSpread(_objectSpread({}, wrapperStyle), motionStyle), styles === null || styles === void 0 ? void 0 : styles.wrapper)\n    }, pickAttrs(props, {\n      data: true\n    })), drawerRender ? drawerRender(content) : content);\n  });\n\n  // =========================== Render ===========================\n  var containerStyle = _objectSpread({}, rootStyle);\n  if (zIndex) {\n    containerStyle.zIndex = zIndex;\n  }\n  return /*#__PURE__*/React.createElement(DrawerContext.Provider, {\n    value: mergedContext\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(placement), rootClassName, _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-open\"), open), \"\".concat(prefixCls, \"-inline\"), inline)),\n    style: containerStyle,\n    tabIndex: -1,\n    ref: panelRef,\n    onKeyDown: onPanelKeyDown\n  }, maskNode, /*#__PURE__*/React.createElement(\"div\", {\n    tabIndex: 0,\n    ref: sentinelStartRef,\n    style: sentinelStyle,\n    \"aria-hidden\": \"true\",\n    \"data-sentinel\": \"start\"\n  }), panelNode, /*#__PURE__*/React.createElement(\"div\", {\n    tabIndex: 0,\n    ref: sentinelEndRef,\n    style: sentinelStyle,\n    \"aria-hidden\": \"true\",\n    \"data-sentinel\": \"end\"\n  })));\n}\nvar RefDrawerPopup = /*#__PURE__*/React.forwardRef(DrawerPopup);\nif (process.env.NODE_ENV !== 'production') {\n  RefDrawerPopup.displayName = 'DrawerPopup';\n}\nexport default RefDrawerPopup;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport Portal from '@rc-component/portal';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { RefContext } from \"./context\";\nimport DrawerPopup from \"./DrawerPopup\";\nimport { warnCheck } from \"./util\";\nvar Drawer = function Drawer(props) {\n  var _props$open = props.open,\n    open = _props$open === void 0 ? false : _props$open,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-drawer' : _props$prefixCls,\n    _props$placement = props.placement,\n    placement = _props$placement === void 0 ? 'right' : _props$placement,\n    _props$autoFocus = props.autoFocus,\n    autoFocus = _props$autoFocus === void 0 ? true : _props$autoFocus,\n    _props$keyboard = props.keyboard,\n    keyboard = _props$keyboard === void 0 ? true : _props$keyboard,\n    _props$width = props.width,\n    width = _props$width === void 0 ? 378 : _props$width,\n    _props$mask = props.mask,\n    mask = _props$mask === void 0 ? true : _props$mask,\n    _props$maskClosable = props.maskClosable,\n    maskClosable = _props$maskClosable === void 0 ? true : _props$maskClosable,\n    getContainer = props.getContainer,\n    forceRender = props.forceRender,\n    afterOpenChange = props.afterOpenChange,\n    destroyOnClose = props.destroyOnClose,\n    onMouseEnter = props.onMouseEnter,\n    onMouseOver = props.onMouseOver,\n    onMouseLeave = props.onMouseLeave,\n    onClick = props.onClick,\n    onKeyDown = props.onKeyDown,\n    onKeyUp = props.onKeyUp,\n    panelRef = props.panelRef;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    animatedVisible = _React$useState2[0],\n    setAnimatedVisible = _React$useState2[1];\n\n  // ============================= Warn =============================\n  if (process.env.NODE_ENV !== 'production') {\n    warnCheck(props);\n  }\n\n  // ============================= Open =============================\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    mounted = _React$useState4[0],\n    setMounted = _React$useState4[1];\n  useLayoutEffect(function () {\n    setMounted(true);\n  }, []);\n  var mergedOpen = mounted ? open : false;\n\n  // ============================ Focus =============================\n  var popupRef = React.useRef();\n  var lastActiveRef = React.useRef();\n  useLayoutEffect(function () {\n    if (mergedOpen) {\n      lastActiveRef.current = document.activeElement;\n    }\n  }, [mergedOpen]);\n\n  // ============================= Open =============================\n  var internalAfterOpenChange = function internalAfterOpenChange(nextVisible) {\n    var _popupRef$current;\n    setAnimatedVisible(nextVisible);\n    afterOpenChange === null || afterOpenChange === void 0 || afterOpenChange(nextVisible);\n    if (!nextVisible && lastActiveRef.current && !((_popupRef$current = popupRef.current) !== null && _popupRef$current !== void 0 && _popupRef$current.contains(lastActiveRef.current))) {\n      var _lastActiveRef$curren;\n      (_lastActiveRef$curren = lastActiveRef.current) === null || _lastActiveRef$curren === void 0 || _lastActiveRef$curren.focus({\n        preventScroll: true\n      });\n    }\n  };\n\n  // =========================== Context ============================\n  var refContext = React.useMemo(function () {\n    return {\n      panel: panelRef\n    };\n  }, [panelRef]);\n\n  // ============================ Render ============================\n  if (!forceRender && !animatedVisible && !mergedOpen && destroyOnClose) {\n    return null;\n  }\n  var eventHandlers = {\n    onMouseEnter: onMouseEnter,\n    onMouseOver: onMouseOver,\n    onMouseLeave: onMouseLeave,\n    onClick: onClick,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp\n  };\n  var drawerPopupProps = _objectSpread(_objectSpread({}, props), {}, {\n    open: mergedOpen,\n    prefixCls: prefixCls,\n    placement: placement,\n    autoFocus: autoFocus,\n    keyboard: keyboard,\n    width: width,\n    mask: mask,\n    maskClosable: maskClosable,\n    inline: getContainer === false,\n    afterOpenChange: internalAfterOpenChange,\n    ref: popupRef\n  }, eventHandlers);\n  return /*#__PURE__*/React.createElement(RefContext.Provider, {\n    value: refContext\n  }, /*#__PURE__*/React.createElement(Portal, {\n    open: mergedOpen || forceRender || animatedVisible,\n    autoDestroy: false,\n    getContainer: getContainer,\n    autoLock: mask && (mergedOpen || animatedVisible)\n  }, /*#__PURE__*/React.createElement(DrawerPopup, drawerPopupProps)));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Drawer.displayName = 'Drawer';\n}\nexport default Drawer;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useClosable, { pickClosable } from '../_util/hooks/useClosable';\nimport { useComponentConfig } from '../config-provider/context';\nimport Skeleton from '../skeleton';\nconst DrawerPanel = props => {\n  var _a, _b;\n  const {\n    prefixCls,\n    title,\n    footer,\n    extra,\n    loading,\n    onClose,\n    headerStyle,\n    bodyStyle,\n    footerStyle,\n    children,\n    classNames: drawerClassNames,\n    styles: drawerStyles\n  } = props;\n  const drawerContext = useComponentConfig('drawer');\n  const customCloseIconRender = React.useCallback(icon => (/*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    onClick: onClose,\n    className: `${prefixCls}-close`\n  }, icon)), [onClose]);\n  const [mergedClosable, mergedCloseIcon] = useClosable(pickClosable(props), pickClosable(drawerContext), {\n    closable: true,\n    closeIconRender: customCloseIconRender\n  });\n  const headerNode = React.useMemo(() => {\n    var _a, _b;\n    if (!title && !mergedClosable) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(\"div\", {\n      style: Object.assign(Object.assign(Object.assign({}, (_a = drawerContext.styles) === null || _a === void 0 ? void 0 : _a.header), headerStyle), drawerStyles === null || drawerStyles === void 0 ? void 0 : drawerStyles.header),\n      className: classNames(`${prefixCls}-header`, {\n        [`${prefixCls}-header-close-only`]: mergedClosable && !title && !extra\n      }, (_b = drawerContext.classNames) === null || _b === void 0 ? void 0 : _b.header, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.header)\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-header-title`\n    }, mergedCloseIcon, title && /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-title`\n    }, title)), extra && /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-extra`\n    }, extra));\n  }, [mergedClosable, mergedCloseIcon, extra, headerStyle, prefixCls, title]);\n  const footerNode = React.useMemo(() => {\n    var _a, _b;\n    if (!footer) {\n      return null;\n    }\n    const footerClassName = `${prefixCls}-footer`;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(footerClassName, (_a = drawerContext.classNames) === null || _a === void 0 ? void 0 : _a.footer, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.footer),\n      style: Object.assign(Object.assign(Object.assign({}, (_b = drawerContext.styles) === null || _b === void 0 ? void 0 : _b.footer), footerStyle), drawerStyles === null || drawerStyles === void 0 ? void 0 : drawerStyles.footer)\n    }, footer);\n  }, [footer, footerStyle, prefixCls]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, headerNode, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${prefixCls}-body`, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.body, (_a = drawerContext.classNames) === null || _a === void 0 ? void 0 : _a.body),\n    style: Object.assign(Object.assign(Object.assign({}, (_b = drawerContext.styles) === null || _b === void 0 ? void 0 : _b.body), bodyStyle), drawerStyles === null || drawerStyles === void 0 ? void 0 : drawerStyles.body)\n  }, loading ? (/*#__PURE__*/React.createElement(Skeleton, {\n    active: true,\n    title: false,\n    paragraph: {\n      rows: 5\n    },\n    className: `${prefixCls}-body-skeleton`\n  })) : children), footerNode);\n};\nexport default DrawerPanel;", "const getMoveTranslate = direction => {\n  const value = '100%';\n  return {\n    left: `translateX(-${value})`,\n    right: `translateX(${value})`,\n    top: `translateY(-${value})`,\n    bottom: `translateY(${value})`\n  }[direction];\n};\nconst getEnterLeaveStyle = (startStyle, endStyle) => ({\n  '&-enter, &-appear': Object.assign(Object.assign({}, startStyle), {\n    '&-active': endStyle\n  }),\n  '&-leave': Object.assign(Object.assign({}, endStyle), {\n    '&-active': startStyle\n  })\n});\nconst getFadeStyle = (from, duration) => Object.assign({\n  '&-enter, &-appear, &-leave': {\n    '&-start': {\n      transition: 'none'\n    },\n    '&-active': {\n      transition: `all ${duration}`\n    }\n  }\n}, getEnterLeaveStyle({\n  opacity: from\n}, {\n  opacity: 1\n}));\nconst getPanelMotionStyles = (direction, duration) => [getFadeStyle(0.7, duration), getEnterLeaveStyle({\n  transform: getMoveTranslate(direction)\n}, {\n  transform: 'none'\n})];\nconst genMotionStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow\n  } = token;\n  return {\n    [componentCls]: {\n      // ======================== Mask ========================\n      [`${componentCls}-mask-motion`]: getFadeStyle(0, motionDurationSlow),\n      // ======================= Panel ========================\n      [`${componentCls}-panel-motion`]: ['left', 'right', 'top', 'bottom'].reduce((obj, direction) => Object.assign(Object.assign({}, obj), {\n        [`&-${direction}`]: getPanelMotionStyles(direction, motionDurationSlow)\n      }), {})\n    }\n  };\n};\nexport default genMotionStyle;", "import { unit } from '@ant-design/cssinjs';\nimport { genFocusStyle } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport genMotionStyle from './motion';\n// =============================== Base ===============================\nconst genDrawerStyle = token => {\n  const {\n    borderRadiusSM,\n    componentCls,\n    zIndexPopup,\n    colorBgMask,\n    colorBgElevated,\n    motionDurationSlow,\n    motionDurationMid,\n    paddingXS,\n    padding,\n    paddingLG,\n    fontSizeLG,\n    lineHeightLG,\n    lineWidth,\n    lineType,\n    colorSplit,\n    marginXS,\n    colorIcon,\n    colorIconHover,\n    colorBgTextHover,\n    colorBgTextActive,\n    colorText,\n    fontWeightStrong,\n    footerPaddingBlock,\n    footerPaddingInline,\n    calc\n  } = token;\n  const wrapperCls = `${componentCls}-content-wrapper`;\n  return {\n    [componentCls]: {\n      position: 'fixed',\n      inset: 0,\n      zIndex: zIndexPopup,\n      pointerEvents: 'none',\n      color: colorText,\n      '&-pure': {\n        position: 'relative',\n        background: colorBgElevated,\n        display: 'flex',\n        flexDirection: 'column',\n        [`&${componentCls}-left`]: {\n          boxShadow: token.boxShadowDrawerLeft\n        },\n        [`&${componentCls}-right`]: {\n          boxShadow: token.boxShadowDrawerRight\n        },\n        [`&${componentCls}-top`]: {\n          boxShadow: token.boxShadowDrawerUp\n        },\n        [`&${componentCls}-bottom`]: {\n          boxShadow: token.boxShadowDrawerDown\n        }\n      },\n      '&-inline': {\n        position: 'absolute'\n      },\n      // ====================== Mask ======================\n      [`${componentCls}-mask`]: {\n        position: 'absolute',\n        inset: 0,\n        zIndex: zIndexPopup,\n        background: colorBgMask,\n        pointerEvents: 'auto'\n      },\n      // ==================== Content =====================\n      [wrapperCls]: {\n        position: 'absolute',\n        zIndex: zIndexPopup,\n        maxWidth: '100vw',\n        transition: `all ${motionDurationSlow}`,\n        '&-hidden': {\n          display: 'none'\n        }\n      },\n      // Placement\n      [`&-left > ${wrapperCls}`]: {\n        top: 0,\n        bottom: 0,\n        left: {\n          _skip_check_: true,\n          value: 0\n        },\n        boxShadow: token.boxShadowDrawerLeft\n      },\n      [`&-right > ${wrapperCls}`]: {\n        top: 0,\n        right: {\n          _skip_check_: true,\n          value: 0\n        },\n        bottom: 0,\n        boxShadow: token.boxShadowDrawerRight\n      },\n      [`&-top > ${wrapperCls}`]: {\n        top: 0,\n        insetInline: 0,\n        boxShadow: token.boxShadowDrawerUp\n      },\n      [`&-bottom > ${wrapperCls}`]: {\n        bottom: 0,\n        insetInline: 0,\n        boxShadow: token.boxShadowDrawerDown\n      },\n      [`${componentCls}-content`]: {\n        display: 'flex',\n        flexDirection: 'column',\n        width: '100%',\n        height: '100%',\n        overflow: 'auto',\n        background: colorBgElevated,\n        pointerEvents: 'auto'\n      },\n      // Header\n      [`${componentCls}-header`]: {\n        display: 'flex',\n        flex: 0,\n        alignItems: 'center',\n        padding: `${unit(padding)} ${unit(paddingLG)}`,\n        fontSize: fontSizeLG,\n        lineHeight: lineHeightLG,\n        borderBottom: `${unit(lineWidth)} ${lineType} ${colorSplit}`,\n        '&-title': {\n          display: 'flex',\n          flex: 1,\n          alignItems: 'center',\n          minWidth: 0,\n          minHeight: 0\n        }\n      },\n      [`${componentCls}-extra`]: {\n        flex: 'none'\n      },\n      [`${componentCls}-close`]: Object.assign({\n        display: 'inline-flex',\n        width: calc(fontSizeLG).add(paddingXS).equal(),\n        height: calc(fontSizeLG).add(paddingXS).equal(),\n        borderRadius: borderRadiusSM,\n        justifyContent: 'center',\n        alignItems: 'center',\n        marginInlineEnd: marginXS,\n        color: colorIcon,\n        fontWeight: fontWeightStrong,\n        fontSize: fontSizeLG,\n        fontStyle: 'normal',\n        lineHeight: 1,\n        textAlign: 'center',\n        textTransform: 'none',\n        textDecoration: 'none',\n        background: 'transparent',\n        border: 0,\n        cursor: 'pointer',\n        transition: `all ${motionDurationMid}`,\n        textRendering: 'auto',\n        '&:hover': {\n          color: colorIconHover,\n          backgroundColor: colorBgTextHover,\n          textDecoration: 'none'\n        },\n        '&:active': {\n          backgroundColor: colorBgTextActive\n        }\n      }, genFocusStyle(token)),\n      [`${componentCls}-title`]: {\n        flex: 1,\n        margin: 0,\n        fontWeight: token.fontWeightStrong,\n        fontSize: fontSizeLG,\n        lineHeight: lineHeightLG\n      },\n      // Body\n      [`${componentCls}-body`]: {\n        flex: 1,\n        minWidth: 0,\n        minHeight: 0,\n        padding: paddingLG,\n        overflow: 'auto',\n        [`${componentCls}-body-skeleton`]: {\n          width: '100%',\n          height: '100%',\n          display: 'flex',\n          justifyContent: 'center'\n        }\n      },\n      // Footer\n      [`${componentCls}-footer`]: {\n        flexShrink: 0,\n        padding: `${unit(footerPaddingBlock)} ${unit(footerPaddingInline)}`,\n        borderTop: `${unit(lineWidth)} ${lineType} ${colorSplit}`\n      },\n      // ====================== RTL =======================\n      '&-rtl': {\n        direction: 'rtl'\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => ({\n  zIndexPopup: token.zIndexPopupBase,\n  footerPaddingBlock: token.paddingXS,\n  footerPaddingInline: token.padding\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Drawer', token => {\n  const drawerToken = mergeToken(token, {});\n  return [genDrawerStyle(drawerToken), genMotionStyle(drawerToken)];\n}, prepareComponentToken);", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport RcDrawer from 'rc-drawer';\nimport ContextIsolator from '../_util/ContextIsolator';\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { getTransitionName } from '../_util/motion';\nimport { devUseWarning } from '../_util/warning';\nimport zIndexContext from '../_util/zindexContext';\nimport { ConfigContext } from '../config-provider';\nimport { useComponentConfig } from '../config-provider/context';\nimport { usePanelRef } from '../watermark/context';\nimport DrawerPanel from './DrawerPanel';\nimport useStyle from './style';\nconst _SizeTypes = ['default', 'large'];\nconst defaultPushState = {\n  distance: 180\n};\nconst Drawer = props => {\n  var _a;\n  const {\n      rootClassName,\n      width,\n      height,\n      size = 'default',\n      mask = true,\n      push = defaultPushState,\n      open,\n      afterOpenChange,\n      onClose,\n      prefixCls: customizePrefixCls,\n      getContainer: customizeGetContainer,\n      style,\n      className,\n      // Deprecated\n      visible,\n      afterVisibleChange,\n      maskStyle,\n      drawerStyle,\n      contentWrapperStyle,\n      destroyOnClose,\n      destroyOnHidden\n    } = props,\n    rest = __rest(props, [\"rootClassName\", \"width\", \"height\", \"size\", \"mask\", \"push\", \"open\", \"afterOpenChange\", \"onClose\", \"prefixCls\", \"getContainer\", \"style\", \"className\", \"visible\", \"afterVisibleChange\", \"maskStyle\", \"drawerStyle\", \"contentWrapperStyle\", \"destroyOnClose\", \"destroyOnHidden\"]);\n  const {\n    getPopupContainer,\n    getPrefixCls,\n    direction,\n    className: contextClassName,\n    style: contextStyle,\n    classNames: contextClassNames,\n    styles: contextStyles\n  } = useComponentConfig('drawer');\n  const prefixCls = getPrefixCls('drawer', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const getContainer =\n  // 有可能为 false，所以不能直接判断\n  customizeGetContainer === undefined && getPopupContainer ? () => getPopupContainer(document.body) : customizeGetContainer;\n  const drawerClassName = classNames({\n    'no-mask': !mask,\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, rootClassName, hashId, cssVarCls);\n  // ========================== Warning ===========================\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Drawer');\n    [['visible', 'open'], ['afterVisibleChange', 'afterOpenChange'], ['headerStyle', 'styles.header'], ['bodyStyle', 'styles.body'], ['footerStyle', 'styles.footer'], ['contentWrapperStyle', 'styles.wrapper'], ['maskStyle', 'styles.mask'], ['drawerStyle', 'styles.content'], ['destroyInactivePanel', 'destroyOnHidden']].forEach(([deprecatedName, newName]) => {\n      warning.deprecated(!(deprecatedName in props), deprecatedName, newName);\n    });\n    if (getContainer !== undefined && ((_a = props.style) === null || _a === void 0 ? void 0 : _a.position) === 'absolute') {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'breaking', '`style` is replaced by `rootStyle` in v5. Please check that `position: absolute` is necessary.') : void 0;\n    }\n  }\n  // ============================ Size ============================\n  const mergedWidth = React.useMemo(() => width !== null && width !== void 0 ? width : size === 'large' ? 736 : 378, [width, size]);\n  const mergedHeight = React.useMemo(() => height !== null && height !== void 0 ? height : size === 'large' ? 736 : 378, [height, size]);\n  // =========================== Motion ===========================\n  const maskMotion = {\n    motionName: getTransitionName(prefixCls, 'mask-motion'),\n    motionAppear: true,\n    motionEnter: true,\n    motionLeave: true,\n    motionDeadline: 500\n  };\n  const panelMotion = motionPlacement => ({\n    motionName: getTransitionName(prefixCls, `panel-motion-${motionPlacement}`),\n    motionAppear: true,\n    motionEnter: true,\n    motionLeave: true,\n    motionDeadline: 500\n  });\n  // ============================ Refs ============================\n  // Select `ant-drawer-content` by `panelRef`\n  const panelRef = usePanelRef();\n  // ============================ zIndex ============================\n  const [zIndex, contextZIndex] = useZIndex('Drawer', rest.zIndex);\n  // =========================== Render ===========================\n  const {\n    classNames: propClassNames = {},\n    styles: propStyles = {}\n  } = rest;\n  return wrapCSSVar(/*#__PURE__*/React.createElement(ContextIsolator, {\n    form: true,\n    space: true\n  }, /*#__PURE__*/React.createElement(zIndexContext.Provider, {\n    value: contextZIndex\n  }, /*#__PURE__*/React.createElement(RcDrawer, Object.assign({\n    prefixCls: prefixCls,\n    onClose: onClose,\n    maskMotion: maskMotion,\n    motion: panelMotion\n  }, rest, {\n    classNames: {\n      mask: classNames(propClassNames.mask, contextClassNames.mask),\n      content: classNames(propClassNames.content, contextClassNames.content),\n      wrapper: classNames(propClassNames.wrapper, contextClassNames.wrapper)\n    },\n    styles: {\n      mask: Object.assign(Object.assign(Object.assign({}, propStyles.mask), maskStyle), contextStyles.mask),\n      content: Object.assign(Object.assign(Object.assign({}, propStyles.content), drawerStyle), contextStyles.content),\n      wrapper: Object.assign(Object.assign(Object.assign({}, propStyles.wrapper), contentWrapperStyle), contextStyles.wrapper)\n    },\n    open: open !== null && open !== void 0 ? open : visible,\n    mask: mask,\n    push: push,\n    width: mergedWidth,\n    height: mergedHeight,\n    style: Object.assign(Object.assign({}, contextStyle), style),\n    className: classNames(contextClassName, className),\n    rootClassName: drawerClassName,\n    getContainer: getContainer,\n    afterOpenChange: afterOpenChange !== null && afterOpenChange !== void 0 ? afterOpenChange : afterVisibleChange,\n    panelRef: panelRef,\n    zIndex: zIndex,\n    // TODO: In the future, destroyOnClose in rc-drawer needs to be upgrade to destroyOnHidden\n    destroyOnClose: destroyOnHidden !== null && destroyOnHidden !== void 0 ? destroyOnHidden : destroyOnClose\n  }), /*#__PURE__*/React.createElement(DrawerPanel, Object.assign({\n    prefixCls: prefixCls\n  }, rest, {\n    onClose: onClose\n  }))))));\n};\n/** @private Internal Component. Do not use in your production. */\nconst PurePanel = props => {\n  const {\n      prefixCls: customizePrefixCls,\n      style,\n      className,\n      placement = 'right'\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"style\", \"className\", \"placement\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('drawer', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const cls = classNames(prefixCls, `${prefixCls}-pure`, `${prefixCls}-${placement}`, hashId, cssVarCls, className);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: cls,\n    style: style\n  }, /*#__PURE__*/React.createElement(DrawerPanel, Object.assign({\n    prefixCls: prefixCls\n  }, restProps))));\n};\nDrawer._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nif (process.env.NODE_ENV !== 'production') {\n  Drawer.displayName = 'Drawer';\n}\nexport default Drawer;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Keyframes } from '@ant-design/cssinjs';\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nexport var proLayoutTitleHide = new Keyframes('antBadgeLoadingCircle', {\n  '0%': {\n    display: 'none',\n    opacity: 0,\n    overflow: 'hidden'\n  },\n  '80%': {\n    overflow: 'hidden'\n  },\n  '100%': {\n    display: 'unset',\n    opacity: 1\n  }\n});\nvar genSiderMenuStyle = function genSiderMenuStyle(token) {\n  var _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout5, _token$layout6, _token$layout7, _token$layout8, _token$layout9, _token$layout10, _token$layout11, _token$layout12;\n  return _defineProperty({}, \"\".concat(token.proComponentsCls, \"-layout\"), _defineProperty(_defineProperty(_defineProperty({}, \"\".concat(token.antCls, \"-layout-sider\").concat(token.componentCls), {\n    background: ((_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.sider) === null || _token$layout === void 0 ? void 0 : _token$layout.colorMenuBackground) || 'transparent'\n  }), token.componentCls, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({\n    position: 'relative',\n    boxSizing: 'border-box',\n    '&-menu': {\n      position: 'relative',\n      zIndex: 10,\n      minHeight: '100%'\n    }\n  }, \"& \".concat(token.antCls, \"-layout-sider-children\"), {\n    position: 'relative',\n    display: 'flex',\n    flexDirection: 'column',\n    height: '100%',\n    paddingInline: (_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.sider) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.paddingInlineLayoutMenu,\n    paddingBlock: (_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.sider) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.paddingBlockLayoutMenu,\n    borderInlineEnd: \"1px solid \".concat(token.colorSplit),\n    marginInlineEnd: -1\n  }), \"\".concat(token.antCls, \"-menu\"), _defineProperty(_defineProperty({}, \"\".concat(token.antCls, \"-menu-item-group-title\"), {\n    fontSize: token.fontSizeSM,\n    paddingBottom: 4\n  }), \"\".concat(token.antCls, \"-menu-item:not(\").concat(token.antCls, \"-menu-item-selected):hover\"), {\n    color: (_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.sider) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorTextMenuItemHover\n  })), '&-logo', {\n    position: 'relative',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    paddingInline: 12,\n    paddingBlock: 16,\n    color: (_token$layout5 = token.layout) === null || _token$layout5 === void 0 || (_token$layout5 = _token$layout5.sider) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.colorTextMenu,\n    cursor: 'pointer',\n    borderBlockEnd: \"1px solid \".concat((_token$layout6 = token.layout) === null || _token$layout6 === void 0 || (_token$layout6 = _token$layout6.sider) === null || _token$layout6 === void 0 ? void 0 : _token$layout6.colorMenuItemDivider),\n    '> a': {\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      minHeight: 22,\n      fontSize: 22,\n      '> img': {\n        display: 'inline-block',\n        height: 22,\n        verticalAlign: 'middle'\n      },\n      '> h1': {\n        display: 'inline-block',\n        height: 22,\n        marginBlock: 0,\n        marginInlineEnd: 0,\n        marginInlineStart: 6,\n        color: (_token$layout7 = token.layout) === null || _token$layout7 === void 0 || (_token$layout7 = _token$layout7.sider) === null || _token$layout7 === void 0 ? void 0 : _token$layout7.colorTextMenuTitle,\n        animationName: proLayoutTitleHide,\n        animationDuration: '.4s',\n        animationTimingFunction: 'ease',\n        fontWeight: 600,\n        fontSize: 16,\n        lineHeight: '22px',\n        verticalAlign: 'middle'\n      }\n    },\n    '&-collapsed': _defineProperty({\n      flexDirection: 'column-reverse',\n      margin: 0,\n      padding: 12\n    }, \"\".concat(token.proComponentsCls, \"-layout-apps-icon\"), {\n      marginBlockEnd: 8,\n      fontSize: 16,\n      transition: 'font-size 0.2s ease-in-out,color 0.2s ease-in-out'\n    })\n  }), '&-actions', {\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    marginBlock: 4,\n    marginInline: 0,\n    color: (_token$layout8 = token.layout) === null || _token$layout8 === void 0 || (_token$layout8 = _token$layout8.sider) === null || _token$layout8 === void 0 ? void 0 : _token$layout8.colorTextMenu,\n    '&-collapsed': {\n      flexDirection: 'column-reverse',\n      paddingBlock: 0,\n      paddingInline: 8,\n      fontSize: 16,\n      transition: 'font-size 0.3s ease-in-out'\n    },\n    '&-list': {\n      color: (_token$layout9 = token.layout) === null || _token$layout9 === void 0 || (_token$layout9 = _token$layout9.sider) === null || _token$layout9 === void 0 ? void 0 : _token$layout9.colorTextMenuSecondary,\n      '&-collapsed': {\n        marginBlockEnd: 8,\n        animationName: 'none'\n      },\n      '&-item': {\n        paddingInline: 6,\n        paddingBlock: 6,\n        lineHeight: '16px',\n        fontSize: 16,\n        cursor: 'pointer',\n        borderRadius: token.borderRadius,\n        '&:hover': {\n          background: token.colorBgTextHover\n        }\n      }\n    },\n    '&-avatar': {\n      fontSize: 14,\n      paddingInline: 8,\n      paddingBlock: 8,\n      display: 'flex',\n      alignItems: 'center',\n      gap: token.marginXS,\n      borderRadius: token.borderRadius,\n      '& *': {\n        cursor: 'pointer'\n      },\n      '&:hover': {\n        background: token.colorBgTextHover\n      }\n    }\n  }), '&-hide-menu-collapsed', {\n    insetInlineStart: \"-\".concat(token.proLayoutCollapsedWidth - 12, \"px\"),\n    position: 'absolute'\n  }), '&-extra', {\n    marginBlockEnd: 16,\n    marginBlock: 0,\n    marginInline: 16,\n    '&-no-logo': {\n      marginBlockStart: 16\n    }\n  }), '&-links', {\n    width: '100%',\n    ul: {\n      height: 'auto'\n    }\n  }), '&-link-menu', {\n    border: 'none',\n    boxShadow: 'none',\n    background: 'transparent'\n  }), '&-footer', {\n    color: (_token$layout10 = token.layout) === null || _token$layout10 === void 0 || (_token$layout10 = _token$layout10.sider) === null || _token$layout10 === void 0 ? void 0 : _token$layout10.colorTextMenuSecondary,\n    paddingBlockEnd: 16,\n    fontSize: token.fontSize,\n    animationName: proLayoutTitleHide,\n    animationDuration: '.4s',\n    animationTimingFunction: 'ease'\n  })), \"\".concat(token.componentCls).concat(token.componentCls, \"-fixed\"), {\n    position: 'fixed',\n    insetBlockStart: 0,\n    insetInlineStart: 0,\n    zIndex: '100',\n    height: '100%',\n    '&-mix': {\n      height: \"calc(100% - \".concat(((_token$layout11 = token.layout) === null || _token$layout11 === void 0 || (_token$layout11 = _token$layout11.header) === null || _token$layout11 === void 0 ? void 0 : _token$layout11.heightLayoutHeader) || 56, \"px)\"),\n      insetBlockStart: \"\".concat(((_token$layout12 = token.layout) === null || _token$layout12 === void 0 || (_token$layout12 = _token$layout12.header) === null || _token$layout12 === void 0 ? void 0 : _token$layout12.heightLayoutHeader) || 56, \"px\")\n    }\n  }));\n};\nexport function useStyle(prefixCls, _ref2) {\n  var proLayoutCollapsedWidth = _ref2.proLayoutCollapsedWidth;\n  return useAntdStyle('ProLayoutSiderMenu', function (token) {\n    var siderMenuToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls),\n      proLayoutCollapsedWidth: proLayoutCollapsedWidth\n    });\n    return [genSiderMenuStyle(siderMenuToken)];\n  });\n}", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { ProProvider } from '@ant-design/pro-provider';\nimport { openVisibleCompatible } from '@ant-design/pro-utils';\nimport { ConfigProvider, Drawer } from 'antd';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport React, { useContext, useEffect } from 'react';\nimport { SiderMenu } from \"./SiderMenu\";\nimport { useStyle } from \"./style/index\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar SiderMenuWrapper = function SiderMenuWrapper(props) {\n  var _token$layout;\n  var isMobile = props.isMobile,\n    siderWidth = props.siderWidth,\n    collapsed = props.collapsed,\n    onCollapse = props.onCollapse,\n    style = props.style,\n    className = props.className,\n    hide = props.hide,\n    prefixCls = props.prefixCls,\n    getContainer = props.getContainer;\n  var _useContext = useContext(ProProvider),\n    token = _useContext.token;\n  useEffect(function () {\n    if (isMobile === true) {\n      onCollapse === null || onCollapse === void 0 || onCollapse(true);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isMobile]);\n  var omitProps = omit(props, ['className', 'style']);\n  var _React$useContext = React.useContext(ConfigProvider.ConfigContext),\n    direction = _React$useContext.direction;\n  var _useStyle = useStyle(\"\".concat(prefixCls, \"-sider\"), {\n      proLayoutCollapsedWidth: 64\n    }),\n    wrapSSR = _useStyle.wrapSSR,\n    hashId = _useStyle.hashId;\n  var siderClassName = classNames(\"\".concat(prefixCls, \"-sider\"), className, hashId);\n  if (hide) {\n    return null;\n  }\n  var drawerOpenProps = openVisibleCompatible(!collapsed, function () {\n    return onCollapse === null || onCollapse === void 0 ? void 0 : onCollapse(true);\n  });\n  return wrapSSR(isMobile ? /*#__PURE__*/_jsx(Drawer, _objectSpread(_objectSpread({\n    placement: direction === 'rtl' ? 'right' : 'left',\n    className: classNames(\"\".concat(prefixCls, \"-drawer-sider\"), className)\n  }, drawerOpenProps), {}, {\n    style: _objectSpread({\n      padding: 0,\n      height: '100vh'\n    }, style),\n    onClose: function onClose() {\n      onCollapse === null || onCollapse === void 0 || onCollapse(true);\n    },\n    maskClosable: true,\n    closable: false,\n    getContainer: getContainer || false,\n    width: siderWidth,\n    styles: {\n      body: {\n        height: '100vh',\n        padding: 0,\n        display: 'flex',\n        flexDirection: 'row',\n        backgroundColor: (_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.sider) === null || _token$layout === void 0 ? void 0 : _token$layout.colorMenuBackground\n      }\n    },\n    children: /*#__PURE__*/_jsx(SiderMenu, _objectSpread(_objectSpread({}, omitProps), {}, {\n      isMobile: true,\n      className: siderClassName,\n      collapsed: isMobile ? false : collapsed,\n      splitMenus: false,\n      originCollapsed: collapsed\n    }))\n  })) : /*#__PURE__*/_jsx(SiderMenu, _objectSpread(_objectSpread({\n    className: siderClassName,\n    originCollapsed: collapsed\n  }, omitProps), {}, {\n    style: style\n  })));\n};\nexport { SiderMenuWrapper as SiderMenu };", "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { match } from 'path-to-regexp';\nexport var matchParamsPath = function matchParamsPath(pathname, breadcrumb, breadcrumbMap) {\n  // Internal logic use breadcrumbMap to ensure the order\n  // 内部逻辑使用 breadcrumbMap 来确保查询顺序\n  if (breadcrumbMap) {\n    var pathKey = _toConsumableArray(breadcrumbMap.keys()).find(function (key) {\n      try {\n        if (key.startsWith('http')) {\n          return false;\n        }\n        return match(key)(pathname);\n      } catch (error) {\n        console.log('key', key, error);\n        return false;\n      }\n    });\n    if (pathKey) {\n      return breadcrumbMap.get(pathKey);\n    }\n  }\n\n  // External uses use breadcrumb\n  // 外部用户使用 breadcrumb 参数\n  if (breadcrumb) {\n    var _pathKey = Object.keys(breadcrumb).find(function (key) {\n      try {\n        if (key !== null && key !== void 0 && key.startsWith('http')) {\n          return false;\n        }\n        return match(key)(pathname);\n      } catch (error) {\n        console.log('key', key, error);\n        return false;\n      }\n    });\n    if (_pathKey) {\n      return breadcrumb[_pathKey];\n    }\n  }\n  return {\n    path: ''\n  };\n};\n/**\n * 获取关于 pageTitle 的所有信息方便包装\n *\n * @param props\n * @param ignoreTitle\n */\nexport var getPageTitleInfo = function getPageTitleInfo(props, ignoreTitle) {\n  var _props$pathname = props.pathname,\n    pathname = _props$pathname === void 0 ? '/' : _props$pathname,\n    breadcrumb = props.breadcrumb,\n    breadcrumbMap = props.breadcrumbMap,\n    formatMessage = props.formatMessage,\n    title = props.title,\n    _props$menu = props.menu,\n    menu = _props$menu === void 0 ? {\n      locale: false\n    } : _props$menu;\n  var pageTitle = ignoreTitle ? '' : title || '';\n  var currRouterData = matchParamsPath(pathname, breadcrumb, breadcrumbMap);\n  if (!currRouterData) {\n    return {\n      title: pageTitle,\n      id: '',\n      pageName: pageTitle\n    };\n  }\n  var pageName = currRouterData.name;\n  if (menu.locale !== false && currRouterData.locale && formatMessage) {\n    pageName = formatMessage({\n      id: currRouterData.locale || '',\n      defaultMessage: currRouterData.name\n    });\n  }\n  if (!pageName) {\n    return {\n      title: pageTitle,\n      id: currRouterData.locale || '',\n      pageName: pageTitle\n    };\n  }\n  if (ignoreTitle || !title) {\n    return {\n      title: pageName,\n      id: currRouterData.locale || '',\n      pageName: pageName\n    };\n  }\n  return {\n    title: \"\".concat(pageName, \" - \").concat(title),\n    id: currRouterData.locale || '',\n    pageName: pageName\n  };\n};\nexport var getPageTitle = function getPageTitle(props, ignoreTitle) {\n  return getPageTitleInfo(props, ignoreTitle).title;\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport settingDrawer from \"./en-US/settingDrawer\";\nexport default _objectSpread({}, settingDrawer);", "export default {\n  'app.setting.pagestyle': 'Page style setting',\n  'app.setting.pagestyle.dark': 'Dark Menu style',\n  'app.setting.pagestyle.light': 'Light Menu style',\n  'app.setting.pagestyle.realdark': 'Dark style (Beta)',\n  'app.setting.content-width': 'Content Width',\n  'app.setting.content-width.fixed': 'Fixed',\n  'app.setting.content-width.fluid': 'Fluid',\n  'app.setting.themecolor': 'Theme Color',\n  'app.setting.themecolor.dust': 'Dust Red',\n  'app.setting.themecolor.volcano': 'Volcano',\n  'app.setting.themecolor.sunset': 'Sunset Orange',\n  'app.setting.themecolor.cyan': 'Cyan',\n  'app.setting.themecolor.green': 'Polar Green',\n  'app.setting.themecolor.techBlue': 'Tech Blue (default)',\n  'app.setting.themecolor.daybreak': 'Daybreak Blue',\n  'app.setting.themecolor.geekblue': 'Geek Blue',\n  'app.setting.themecolor.purple': 'Golden Purple',\n  'app.setting.sidermenutype': 'SideMenu Type',\n  'app.setting.sidermenutype-sub': 'Classic',\n  'app.setting.sidermenutype-group': 'Grouping',\n  'app.setting.navigationmode': 'Navigation Mode',\n  'app.setting.regionalsettings': 'Regional Settings',\n  'app.setting.regionalsettings.header': 'Header',\n  'app.setting.regionalsettings.menu': 'Menu',\n  'app.setting.regionalsettings.footer': 'Footer',\n  'app.setting.regionalsettings.menuHeader': 'Menu Header',\n  'app.setting.sidemenu': 'Side Menu Layout',\n  'app.setting.topmenu': 'Top Menu Layout',\n  'app.setting.mixmenu': 'Mix Menu Layout',\n  'app.setting.splitMenus': 'Split Menus',\n  'app.setting.fixedheader': 'Fixed Header',\n  'app.setting.fixedsidebar': 'Fixed Sidebar',\n  'app.setting.fixedsidebar.hint': 'Works on Side Menu Layout',\n  'app.setting.hideheader': 'Hidden Header when scrolling',\n  'app.setting.hideheader.hint': 'Works when Hidden Header is enabled',\n  'app.setting.othersettings': 'Other Settings',\n  'app.setting.weakmode': 'Weak Mode',\n  'app.setting.copy': 'Copy Setting',\n  'app.setting.loading': 'Loading theme',\n  'app.setting.copyinfo': 'copy success，please replace defaultSettings in src/models/setting.js',\n  'app.setting.production.hint': 'Setting panel shows in development environment only, please manually modify'\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport settingDrawer from \"./it-IT/settingDrawer\";\nexport default _objectSpread({}, settingDrawer);", "export default {\n  'app.setting.pagestyle': 'Impostazioni di stile',\n  'app.setting.pagestyle.dark': 'Tema scuro',\n  'app.setting.pagestyle.light': 'Tema chiaro',\n  'app.setting.content-width': 'Largezza contenuto',\n  'app.setting.content-width.fixed': 'Fissa',\n  'app.setting.content-width.fluid': 'Fluida',\n  'app.setting.themecolor': 'Colore del tema',\n  'app.setting.themecolor.dust': 'Rosso polvere',\n  'app.setting.themecolor.volcano': 'Vulcano',\n  'app.setting.themecolor.sunset': 'Arancione tramonto',\n  'app.setting.themecolor.cyan': '<PERSON><PERSON>',\n  'app.setting.themecolor.green': 'Verde polare',\n  'app.setting.themecolor.techBlue': 'Tech Blu (default)',\n  'app.setting.themecolor.daybreak': 'Blu cielo mattutino',\n  'app.setting.themecolor.geekblue': 'Blu geek',\n  'app.setting.themecolor.purple': 'Viola dorato',\n  'app.setting.navigationmode': 'Modalità di navigazione',\n  'app.setting.sidemenu': 'Menu laterale',\n  'app.setting.topmenu': 'Menu in testata',\n  'app.setting.mixmenu': 'Menu misto',\n  'app.setting.splitMenus': 'Menu divisi',\n  'app.setting.fixedheader': 'Testata fissa',\n  'app.setting.fixedsidebar': 'Menu laterale fisso',\n  'app.setting.fixedsidebar.hint': 'Solo se selezionato Menu laterale',\n  'app.setting.hideheader': 'Nascondi testata durante lo scorrimento',\n  'app.setting.hideheader.hint': 'Solo se abilitato Nascondi testata durante lo scorrimento',\n  'app.setting.othersettings': 'Altre impostazioni',\n  'app.setting.weakmode': 'Inverti colori',\n  'app.setting.copy': 'Copia impostazioni',\n  'app.setting.loading': 'Carico tema...',\n  'app.setting.copyinfo': 'Impostazioni copiate con successo! Incolla il contenuto in config/defaultSettings.js',\n  'app.setting.production.hint': 'Questo pannello è visibile solo durante lo sviluppo. Le impostazioni devono poi essere modificate manulamente'\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport settingDrawer from \"./ko-KR/settingDrawer\";\nexport default _objectSpread({}, settingDrawer);", "export default {\n  'app.setting.pagestyle': '스타일 설정',\n  'app.setting.pagestyle.dark': '다크 모드',\n  'app.setting.pagestyle.light': '라이트 모드',\n  'app.setting.content-width': '컨텐츠 너비',\n  'app.setting.content-width.fixed': '고정',\n  'app.setting.content-width.fluid': '흐름',\n  'app.setting.themecolor': '테마 색상',\n  'app.setting.themecolor.dust': 'Dust Red',\n  'app.setting.themecolor.volcano': 'Volcano',\n  'app.setting.themecolor.sunset': 'Sunset Orange',\n  'app.setting.themecolor.cyan': '<PERSON>an',\n  'app.setting.themecolor.green': 'Polar Green',\n  'app.setting.themecolor.techBlue': 'Tech Blu (default)',\n  'app.setting.themecolor.daybreak': 'Daybreak Blue',\n  'app.setting.themecolor.geekblue': 'Geek Blue',\n  'app.setting.themecolor.purple': 'Golden Purple',\n  'app.setting.navigationmode': '네비게이션 모드',\n  'app.setting.regionalsettings': '영역별 설정',\n  'app.setting.regionalsettings.header': '헤더',\n  'app.setting.regionalsettings.menu': '메뉴',\n  'app.setting.regionalsettings.footer': '바닥글',\n  'app.setting.regionalsettings.menuHeader': '메뉴 헤더',\n  'app.setting.sidemenu': '메뉴 사이드 배치',\n  'app.setting.topmenu': '메뉴 상단 배치',\n  'app.setting.mixmenu': '혼합형 배치',\n  'app.setting.splitMenus': '메뉴 분리',\n  'app.setting.fixedheader': '헤더 고정',\n  'app.setting.fixedsidebar': '사이드바 고정',\n  'app.setting.fixedsidebar.hint': \"'메뉴 사이드 배치'를 선택했을 때 동작함\",\n  'app.setting.hideheader': '스크롤 중 헤더 감추기',\n  'app.setting.hideheader.hint': \"'헤더 감추기 옵션'을 선택했을 때 동작함\",\n  'app.setting.othersettings': '다른 설정',\n  'app.setting.weakmode': '고대비 모드',\n  'app.setting.copy': '설정값 복사',\n  'app.setting.loading': '테마 로딩 중',\n  'app.setting.copyinfo': '복사 성공. src/models/settings.js에 있는 defaultSettings를 교체해 주세요.',\n  'app.setting.production.hint': '설정 판넬은 개발 환경에서만 보여집니다. 직접 수동으로 변경바랍니다.'\n};", "import { isBrowser } from '@ant-design/pro-utils';\nimport enUSLocal from \"./en-US\";\nimport itITLocal from \"./it-IT\";\nimport koKRLocal from \"./ko-KR\";\nimport zhLocal from \"./zh-CN\";\nimport zhTWLocal from \"./zh-TW\";\nvar locales = {\n  'zh-CN': zhLocal,\n  'zh-TW': zhTWLocal,\n  'en-US': enUSLocal,\n  'it-IT': itITLocal,\n  'ko-KR': koKRLocal\n};\nexport var getLanguage = function getLanguage() {\n  // support ssr\n  if (!isBrowser()) return 'zh-CN';\n  var lang = window.localStorage.getItem('umi_locale');\n  return lang || window.g_locale || navigator.language;\n};\nexport var gLocaleObject = function gLocaleObject() {\n  var gLocale = getLanguage();\n  return locales[gLocale] || locales['zh-CN'];\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport settingDrawer from \"./zh-CN/settingDrawer\";\nexport default _objectSpread({}, settingDrawer);", "export default {\n  'app.setting.pagestyle': '整体风格设置',\n  'app.setting.pagestyle.dark': '暗色菜单风格',\n  'app.setting.pagestyle.light': '亮色菜单风格',\n  'app.setting.pagestyle.realdark': '暗色风格(实验功能)',\n  'app.setting.content-width': '内容区域宽度',\n  'app.setting.content-width.fixed': '定宽',\n  'app.setting.content-width.fluid': '流式',\n  'app.setting.themecolor': '主题色',\n  'app.setting.themecolor.dust': '薄暮',\n  'app.setting.themecolor.volcano': '火山',\n  'app.setting.themecolor.sunset': '日暮',\n  'app.setting.themecolor.cyan': '明青',\n  'app.setting.themecolor.green': '极光绿',\n  'app.setting.themecolor.techBlue': '科技蓝（默认）',\n  'app.setting.themecolor.daybreak': '拂晓',\n  'app.setting.themecolor.geekblue': '极客蓝',\n  'app.setting.themecolor.purple': '酱紫',\n  'app.setting.navigationmode': '导航模式',\n  'app.setting.sidermenutype': '侧边菜单类型',\n  'app.setting.sidermenutype-sub': '经典模式',\n  'app.setting.sidermenutype-group': '分组模式',\n  'app.setting.regionalsettings': '内容区域',\n  'app.setting.regionalsettings.header': '顶栏',\n  'app.setting.regionalsettings.menu': '菜单',\n  'app.setting.regionalsettings.footer': '页脚',\n  'app.setting.regionalsettings.menuHeader': '菜单头',\n  'app.setting.sidemenu': '侧边菜单布局',\n  'app.setting.topmenu': '顶部菜单布局',\n  'app.setting.mixmenu': '混合菜单布局',\n  'app.setting.splitMenus': '自动分割菜单',\n  'app.setting.fixedheader': '固定 Header',\n  'app.setting.fixedsidebar': '固定侧边菜单',\n  'app.setting.fixedsidebar.hint': '侧边菜单布局时可配置',\n  'app.setting.hideheader': '下滑时隐藏 Header',\n  'app.setting.hideheader.hint': '固定 Header 时可配置',\n  'app.setting.othersettings': '其他设置',\n  'app.setting.weakmode': '色弱模式',\n  'app.setting.copy': '拷贝设置',\n  'app.setting.loading': '正在加载主题',\n  'app.setting.copyinfo': '拷贝成功，请到 src/defaultSettings.js 中替换默认配置',\n  'app.setting.production.hint': '配置栏只在开发环境用于预览，生产环境不会展现，请拷贝后手动修改配置文件'\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport settingDrawer from \"./zh-TW/settingDrawer\";\nexport default _objectSpread({}, settingDrawer);", "export default {\n  'app.setting.pagestyle': '整體風格設置',\n  'app.setting.pagestyle.dark': '暗色菜單風格',\n  'app.setting.pagestyle.realdark': '暗色風格(实验功能)',\n  'app.setting.pagestyle.light': '亮色菜單風格',\n  'app.setting.content-width': '內容區域寬度',\n  'app.setting.content-width.fixed': '定寬',\n  'app.setting.content-width.fluid': '流式',\n  'app.setting.themecolor': '主題色',\n  'app.setting.themecolor.dust': '薄暮',\n  'app.setting.themecolor.volcano': '火山',\n  'app.setting.themecolor.sunset': '日暮',\n  'app.setting.themecolor.cyan': '明青',\n  'app.setting.themecolor.green': '極光綠',\n  'app.setting.themecolor.techBlue': '科技蓝（默認）',\n  'app.setting.themecolor.daybreak': '拂曉藍',\n  'app.setting.themecolor.geekblue': '極客藍',\n  'app.setting.themecolor.purple': '醬紫',\n  'app.setting.navigationmode': '導航模式',\n  'app.setting.sidemenu': '側邊菜單布局',\n  'app.setting.topmenu': '頂部菜單布局',\n  'app.setting.mixmenu': '混合菜單布局',\n  'app.setting.splitMenus': '自动分割菜单',\n  'app.setting.fixedheader': '固定 Header',\n  'app.setting.fixedsidebar': '固定側邊菜單',\n  'app.setting.fixedsidebar.hint': '側邊菜單布局時可配置',\n  'app.setting.hideheader': '下滑時隱藏 Header',\n  'app.setting.hideheader.hint': '固定 Header 時可配置',\n  'app.setting.othersettings': '其他設置',\n  'app.setting.weakmode': '色弱模式',\n  'app.setting.copy': '拷貝設置',\n  'app.setting.loading': '正在加載主題',\n  'app.setting.copyinfo': '拷貝成功，請到 src/defaultSettings.js 中替換默認配置',\n  'app.setting.production.hint': '配置欄只在開發環境用於預覽，生產環境不會展現，請拷貝後手動修改配置文件'\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nimport { version } from 'antd';\nvar getVersion = function getVersion() {\n  var _process;\n  if (typeof process === 'undefined') return version;\n  return ((_process = process) === null || _process === void 0 || (_process = _process.env) === null || _process === void 0 ? void 0 : _process.ANTD_VERSION) || version;\n};\n\n/**\n * 主要区别：\n * 需要手动引入 import 'antd/dist/antd.css';\n * 需要重置 menu 的样式\n * @param token\n * @returns\n */\nvar compatibleStyle = function compatibleStyle(token) {\n  var _getVersion, _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout5, _token$layout6, _token$layout7, _token$layout8, _token$layout9, _token$layout10, _token$layout11, _token$layout12, _token$layout13, _token$layout14, _token$layout15, _token$layout16, _token$layout17, _token$layout18, _token$layout19, _$concat6, _token$layout20, _token$layout21, _token$layout22, _token$layout23, _token$layout24, _token$layout25, _token$layout26, _token$layout27, _token$layout28, _token$layout29, _token$layout30;\n  if ((_getVersion = getVersion()) !== null && _getVersion !== void 0 && _getVersion.startsWith('5')) {\n    return {};\n  }\n  return _defineProperty(_defineProperty(_defineProperty({}, token.componentCls, _defineProperty(_defineProperty({\n    width: '100%',\n    height: '100%'\n  }, \"\".concat(token.proComponentsCls, \"-base-menu\"), (_$concat6 = {\n    color: (_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.sider) === null || _token$layout === void 0 ? void 0 : _token$layout.colorTextMenu\n  }, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_$concat6, \"\".concat(token.antCls, \"-menu-sub\"), {\n    backgroundColor: 'transparent!important',\n    color: (_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.sider) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.colorTextMenu\n  }), \"& \".concat(token.antCls, \"-layout\"), {\n    backgroundColor: 'transparent',\n    width: '100%'\n  }), \"\".concat(token.antCls, \"-menu-submenu-expand-icon, \").concat(token.antCls, \"-menu-submenu-arrow\"), {\n    color: 'inherit'\n  }), \"&\".concat(token.antCls, \"-menu\"), _defineProperty(_defineProperty({\n    color: (_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.sider) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorTextMenu\n  }, \"\".concat(token.antCls, \"-menu-item\"), {\n    '*': {\n      transition: 'none !important'\n    }\n  }), \"\".concat(token.antCls, \"-menu-item a\"), {\n    color: 'inherit'\n  })), \"&\".concat(token.antCls, \"-menu-inline\"), _defineProperty({}, \"\".concat(token.antCls, \"-menu-selected::after,\").concat(token.antCls, \"-menu-item-selected::after\"), {\n    display: 'none'\n  })), \"\".concat(token.antCls, \"-menu-sub \").concat(token.antCls, \"-menu-inline\"), {\n    backgroundColor: 'transparent!important'\n  }), \"\".concat(token.antCls, \"-menu-item:active, \\n        \").concat(token.antCls, \"-menu-submenu-title:active\"), {\n    backgroundColor: 'transparent!important'\n  }), \"&\".concat(token.antCls, \"-menu-light\"), _defineProperty({}, \"\".concat(token.antCls, \"-menu-item:hover, \\n            \").concat(token.antCls, \"-menu-item-active,\\n            \").concat(token.antCls, \"-menu-submenu-active, \\n            \").concat(token.antCls, \"-menu-submenu-title:hover\"), _defineProperty({\n    color: (_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.sider) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorTextMenuActive,\n    borderRadius: token.borderRadius\n  }, \"\".concat(token.antCls, \"-menu-submenu-arrow\"), {\n    color: (_token$layout5 = token.layout) === null || _token$layout5 === void 0 || (_token$layout5 = _token$layout5.sider) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.colorTextMenuActive\n  }))), \"&\".concat(token.antCls, \"-menu:not(\").concat(token.antCls, \"-menu-horizontal)\"), _defineProperty(_defineProperty({}, \"\".concat(token.antCls, \"-menu-item-selected\"), {\n    backgroundColor: (_token$layout6 = token.layout) === null || _token$layout6 === void 0 || (_token$layout6 = _token$layout6.sider) === null || _token$layout6 === void 0 ? void 0 : _token$layout6.colorBgMenuItemSelected,\n    borderRadius: token.borderRadius\n  }), \"\".concat(token.antCls, \"-menu-item:hover, \\n            \").concat(token.antCls, \"-menu-item-active,\\n            \").concat(token.antCls, \"-menu-submenu-title:hover\"), _defineProperty({\n    color: (_token$layout7 = token.layout) === null || _token$layout7 === void 0 || (_token$layout7 = _token$layout7.sider) === null || _token$layout7 === void 0 ? void 0 : _token$layout7.colorTextMenuActive,\n    borderRadius: token.borderRadius,\n    backgroundColor: \"\".concat((_token$layout8 = token.layout) === null || _token$layout8 === void 0 || (_token$layout8 = _token$layout8.header) === null || _token$layout8 === void 0 ? void 0 : _token$layout8.colorBgMenuItemHover, \" !important\")\n  }, \"\".concat(token.antCls, \"-menu-submenu-arrow\"), {\n    color: (_token$layout9 = token.layout) === null || _token$layout9 === void 0 || (_token$layout9 = _token$layout9.sider) === null || _token$layout9 === void 0 ? void 0 : _token$layout9.colorTextMenuActive\n  }))), \"\".concat(token.antCls, \"-menu-item-selected\"), {\n    color: (_token$layout10 = token.layout) === null || _token$layout10 === void 0 || (_token$layout10 = _token$layout10.sider) === null || _token$layout10 === void 0 ? void 0 : _token$layout10.colorTextMenuSelected\n  }), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_$concat6, \"\".concat(token.antCls, \"-menu-submenu-selected\"), {\n    color: (_token$layout11 = token.layout) === null || _token$layout11 === void 0 || (_token$layout11 = _token$layout11.sider) === null || _token$layout11 === void 0 ? void 0 : _token$layout11.colorTextMenuSelected\n  }), \"&\".concat(token.antCls, \"-menu:not(\").concat(token.antCls, \"-menu-inline) \").concat(token.antCls, \"-menu-submenu-open\"), {\n    color: (_token$layout12 = token.layout) === null || _token$layout12 === void 0 || (_token$layout12 = _token$layout12.sider) === null || _token$layout12 === void 0 ? void 0 : _token$layout12.colorTextMenuSelected\n  }), \"&\".concat(token.antCls, \"-menu-vertical\"), _defineProperty({}, \"\".concat(token.antCls, \"-menu-submenu-selected\"), {\n    borderRadius: token.borderRadius,\n    color: (_token$layout13 = token.layout) === null || _token$layout13 === void 0 || (_token$layout13 = _token$layout13.sider) === null || _token$layout13 === void 0 ? void 0 : _token$layout13.colorTextMenuSelected\n  })), \"\".concat(token.antCls, \"-menu-submenu:hover > \").concat(token.antCls, \"-menu-submenu-title > \").concat(token.antCls, \"-menu-submenu-arrow\"), {\n    color: (_token$layout14 = token.layout) === null || _token$layout14 === void 0 || (_token$layout14 = _token$layout14.sider) === null || _token$layout14 === void 0 ? void 0 : _token$layout14.colorTextMenuActive\n  }), \"&\".concat(token.antCls, \"-menu-horizontal\"), _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(token.antCls, \"-menu-item:hover,\\n          \").concat(token.antCls, \"-menu-submenu:hover,\\n          \").concat(token.antCls, \"-menu-item-active,\\n          \").concat(token.antCls, \"-menu-submenu-active\"), {\n    borderRadius: 4,\n    transition: 'none',\n    color: (_token$layout15 = token.layout) === null || _token$layout15 === void 0 || (_token$layout15 = _token$layout15.header) === null || _token$layout15 === void 0 ? void 0 : _token$layout15.colorTextMenuActive,\n    backgroundColor: \"\".concat((_token$layout16 = token.layout) === null || _token$layout16 === void 0 || (_token$layout16 = _token$layout16.header) === null || _token$layout16 === void 0 ? void 0 : _token$layout16.colorBgMenuItemHover, \" !important\")\n  }), \"\".concat(token.antCls, \"-menu-item-open,\\n          \").concat(token.antCls, \"-menu-submenu-open,\\n          \").concat(token.antCls, \"-menu-item-selected,\\n          \").concat(token.antCls, \"-menu-submenu-selected\"), _defineProperty({\n    backgroundColor: (_token$layout17 = token.layout) === null || _token$layout17 === void 0 || (_token$layout17 = _token$layout17.header) === null || _token$layout17 === void 0 ? void 0 : _token$layout17.colorBgMenuItemSelected,\n    borderRadius: token.borderRadius,\n    transition: 'none',\n    color: \"\".concat((_token$layout18 = token.layout) === null || _token$layout18 === void 0 || (_token$layout18 = _token$layout18.header) === null || _token$layout18 === void 0 ? void 0 : _token$layout18.colorTextMenuSelected, \" !important\")\n  }, \"\".concat(token.antCls, \"-menu-submenu-arrow\"), {\n    color: \"\".concat((_token$layout19 = token.layout) === null || _token$layout19 === void 0 || (_token$layout19 = _token$layout19.header) === null || _token$layout19 === void 0 ? void 0 : _token$layout19.colorTextMenuSelected, \" !important\")\n  })), \"> \".concat(token.antCls, \"-menu-item, > \").concat(token.antCls, \"-menu-submenu\"), {\n    paddingInline: 16,\n    marginInline: 4\n  }), \"> \".concat(token.antCls, \"-menu-item::after, > \").concat(token.antCls, \"-menu-submenu::after\"), {\n    display: 'none'\n  })))), \"\".concat(token.proComponentsCls, \"-top-nav-header-base-menu\"), _defineProperty(_defineProperty({}, \"&\".concat(token.antCls, \"-menu\"), _defineProperty({\n    color: (_token$layout20 = token.layout) === null || _token$layout20 === void 0 || (_token$layout20 = _token$layout20.header) === null || _token$layout20 === void 0 ? void 0 : _token$layout20.colorTextMenu\n  }, \"\".concat(token.antCls, \"-menu-item a\"), {\n    color: 'inherit'\n  })), \"&\".concat(token.antCls, \"-menu-light\"), _defineProperty(_defineProperty({}, \"\".concat(token.antCls, \"-menu-item:hover, \\n            \").concat(token.antCls, \"-menu-item-active,\\n            \").concat(token.antCls, \"-menu-submenu-active, \\n            \").concat(token.antCls, \"-menu-submenu-title:hover\"), _defineProperty({\n    color: (_token$layout21 = token.layout) === null || _token$layout21 === void 0 || (_token$layout21 = _token$layout21.header) === null || _token$layout21 === void 0 ? void 0 : _token$layout21.colorTextMenuActive,\n    borderRadius: token.borderRadius,\n    transition: 'none',\n    backgroundColor: (_token$layout22 = token.layout) === null || _token$layout22 === void 0 || (_token$layout22 = _token$layout22.header) === null || _token$layout22 === void 0 ? void 0 : _token$layout22.colorBgMenuItemSelected\n  }, \"\".concat(token.antCls, \"-menu-submenu-arrow\"), {\n    color: (_token$layout23 = token.layout) === null || _token$layout23 === void 0 || (_token$layout23 = _token$layout23.header) === null || _token$layout23 === void 0 ? void 0 : _token$layout23.colorTextMenuActive\n  })), \"\".concat(token.antCls, \"-menu-item-selected\"), {\n    color: (_token$layout24 = token.layout) === null || _token$layout24 === void 0 || (_token$layout24 = _token$layout24.header) === null || _token$layout24 === void 0 ? void 0 : _token$layout24.colorTextMenuSelected,\n    borderRadius: token.borderRadius,\n    backgroundColor: (_token$layout25 = token.layout) === null || _token$layout25 === void 0 || (_token$layout25 = _token$layout25.header) === null || _token$layout25 === void 0 ? void 0 : _token$layout25.colorBgMenuItemSelected\n  })))), \"\".concat(token.antCls, \"-menu-sub\").concat(token.antCls, \"-menu-inline\"), {\n    backgroundColor: 'transparent!important'\n  }), \"\".concat(token.antCls, \"-menu-submenu-popup\"), _defineProperty(_defineProperty(_defineProperty(_defineProperty({\n    backgroundColor: 'rgba(255, 255, 255, 0.42)',\n    '-webkit-backdrop-filter': 'blur(8px)',\n    backdropFilter: 'blur(8px)'\n  }, \"\".concat(token.antCls, \"-menu\"), _defineProperty({\n    background: 'transparent !important',\n    backgroundColor: 'transparent !important'\n  }, \"\".concat(token.antCls, \"-menu-item:active, \\n        \").concat(token.antCls, \"-menu-submenu-title:active\"), {\n    backgroundColor: 'transparent!important'\n  })), \"\".concat(token.antCls, \"-menu-item-selected\"), {\n    color: (_token$layout26 = token.layout) === null || _token$layout26 === void 0 || (_token$layout26 = _token$layout26.sider) === null || _token$layout26 === void 0 ? void 0 : _token$layout26.colorTextMenuSelected\n  }), \"\".concat(token.antCls, \"-menu-submenu-selected\"), {\n    color: (_token$layout27 = token.layout) === null || _token$layout27 === void 0 || (_token$layout27 = _token$layout27.sider) === null || _token$layout27 === void 0 ? void 0 : _token$layout27.colorTextMenuSelected\n  }), \"\".concat(token.antCls, \"-menu:not(\").concat(token.antCls, \"-menu-horizontal)\"), _defineProperty(_defineProperty({}, \"\".concat(token.antCls, \"-menu-item-selected\"), {\n    backgroundColor: 'rgba(0, 0, 0, 0.04)',\n    borderRadius: token.borderRadius,\n    color: (_token$layout28 = token.layout) === null || _token$layout28 === void 0 || (_token$layout28 = _token$layout28.sider) === null || _token$layout28 === void 0 ? void 0 : _token$layout28.colorTextMenuSelected\n  }), \"\".concat(token.antCls, \"-menu-item:hover, \\n          \").concat(token.antCls, \"-menu-item-active,\\n          \").concat(token.antCls, \"-menu-submenu-title:hover\"), _defineProperty({\n    color: (_token$layout29 = token.layout) === null || _token$layout29 === void 0 || (_token$layout29 = _token$layout29.sider) === null || _token$layout29 === void 0 ? void 0 : _token$layout29.colorTextMenuActive,\n    borderRadius: token.borderRadius\n  }, \"\".concat(token.antCls, \"-menu-submenu-arrow\"), {\n    color: (_token$layout30 = token.layout) === null || _token$layout30 === void 0 || (_token$layout30 = _token$layout30.sider) === null || _token$layout30 === void 0 ? void 0 : _token$layout30.colorTextMenuActive\n  }))));\n};\nvar genProLayoutStyle = function genProLayoutStyle(token) {\n  var _token$layout31, _token$layout32, _token$layout33, _token$layout34;\n  return _defineProperty(_defineProperty({}, \"\".concat(token.antCls, \"-layout\"), {\n    backgroundColor: 'transparent !important'\n  }), token.componentCls, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"& \".concat(token.antCls, \"-layout\"), {\n    display: 'flex',\n    backgroundColor: 'transparent',\n    width: '100%'\n  }), \"\".concat(token.componentCls, \"-content\"), {\n    display: 'flex',\n    flexDirection: 'column',\n    width: '100%',\n    backgroundColor: ((_token$layout31 = token.layout) === null || _token$layout31 === void 0 || (_token$layout31 = _token$layout31.pageContainer) === null || _token$layout31 === void 0 ? void 0 : _token$layout31.colorBgPageContainer) || 'transparent',\n    position: 'relative',\n    paddingBlock: (_token$layout32 = token.layout) === null || _token$layout32 === void 0 || (_token$layout32 = _token$layout32.pageContainer) === null || _token$layout32 === void 0 ? void 0 : _token$layout32.paddingBlockPageContainerContent,\n    paddingInline: (_token$layout33 = token.layout) === null || _token$layout33 === void 0 || (_token$layout33 = _token$layout33.pageContainer) === null || _token$layout33 === void 0 ? void 0 : _token$layout33.paddingInlinePageContainerContent,\n    '&-has-page-container': {\n      padding: 0\n    }\n  }), \"\".concat(token.componentCls, \"-container\"), {\n    width: '100%',\n    display: 'flex',\n    flexDirection: 'column',\n    minWidth: 0,\n    minHeight: 0,\n    backgroundColor: 'transparent'\n  }), \"\".concat(token.componentCls, \"-bg-list\"), {\n    pointerEvents: 'none',\n    position: 'fixed',\n    overflow: 'hidden',\n    insetBlockStart: 0,\n    insetInlineStart: 0,\n    zIndex: 0,\n    height: '100%',\n    width: '100%',\n    background: (_token$layout34 = token.layout) === null || _token$layout34 === void 0 ? void 0 : _token$layout34.bgLayout\n  }));\n};\nexport function useStyle(prefixCls) {\n  return useAntdStyle('ProLayout', function (token) {\n    var proLayoutToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls)\n    });\n    return [genProLayoutStyle(proLayoutToken), compatibleStyle(proLayoutToken)];\n  });\n}", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { compareVersions } from '@ant-design/pro-utils';\nimport { version } from 'antd';\nimport { match } from 'path-to-regexp';\nimport { urlToList } from \"./pathTools\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var getVersion = function getVersion() {\n  var _process;\n  if (typeof process === 'undefined') return version;\n  return ((_process = process) === null || _process === void 0 || (_process = _process.env) === null || _process === void 0 ? void 0 : _process.ANTD_VERSION) || version;\n};\n// 渲染 Breadcrumb 子节点\n// Render the Breadcrumb child node\nvar defaultItemRender = function defaultItemRender(route, _, routes) {\n  var _ref = route,\n    breadcrumbName = _ref.breadcrumbName,\n    title = _ref.title,\n    path = _ref.path;\n  var last = routes.findIndex(function (i) {\n    return (\n      // @ts-ignore\n      i.linkPath === route.path\n    );\n  }) === routes.length - 1;\n  return last ? /*#__PURE__*/_jsx(\"span\", {\n    children: title || breadcrumbName\n  }) : /*#__PURE__*/_jsx(\"span\", {\n    onClick: path ? function () {\n      return location.href = path;\n    } : undefined,\n    children: title || breadcrumbName\n  });\n};\nvar renderItemLocal = function renderItemLocal(item, props) {\n  var formatMessage = props.formatMessage,\n    menu = props.menu;\n  if (item.locale && formatMessage && (menu === null || menu === void 0 ? void 0 : menu.locale) !== false) {\n    return formatMessage({\n      id: item.locale,\n      defaultMessage: item.name\n    });\n  }\n  return item.name;\n};\nexport var getBreadcrumb = function getBreadcrumb(breadcrumbMap, url) {\n  var breadcrumbItem = breadcrumbMap.get(url);\n  if (!breadcrumbItem) {\n    // Find the first matching path in the order defined by route config\n    // 按照 route config 定义的顺序找到第一个匹配的路径\n    var keys = Array.from(breadcrumbMap.keys()) || [];\n    var targetPath = keys.find(function (path) {\n      try {\n        if (path !== null && path !== void 0 && path.startsWith('http')) return false;\n        return match(path.replace('?', ''))(url);\n      } catch (error) {\n        console.log('path', path, error);\n        return false;\n      }\n    }\n    // remove ? ,不然会重复\n    );\n    if (targetPath) breadcrumbItem = breadcrumbMap.get(targetPath);\n  }\n  return breadcrumbItem || {\n    path: ''\n  };\n};\nexport var getBreadcrumbFromProps = function getBreadcrumbFromProps(props) {\n  var location = props.location,\n    breadcrumbMap = props.breadcrumbMap;\n  return {\n    location: location,\n    breadcrumbMap: breadcrumbMap\n  };\n};\nvar conversionFromLocation = function conversionFromLocation(routerLocation, breadcrumbMap, props) {\n  // Convertor the url to an array\n  var pathSnippets = urlToList(routerLocation === null || routerLocation === void 0 ? void 0 : routerLocation.pathname);\n  // Loop data mosaic routing\n  var extraBreadcrumbItems = pathSnippets.map(function (url) {\n    var currentBreadcrumb = getBreadcrumb(breadcrumbMap, url);\n    var name = renderItemLocal(currentBreadcrumb, props);\n    var hideInBreadcrumb = currentBreadcrumb.hideInBreadcrumb;\n    return name && !hideInBreadcrumb ? {\n      linkPath: url,\n      breadcrumbName: name,\n      title: name,\n      component: currentBreadcrumb.component\n    } : {\n      linkPath: '',\n      breadcrumbName: '',\n      title: ''\n    };\n  }).filter(function (item) {\n    return item && item.linkPath;\n  });\n  return extraBreadcrumbItems;\n};\n/** 将参数转化为面包屑 Convert parameters into breadcrumbs */\nexport var genBreadcrumbProps = function genBreadcrumbProps(props) {\n  var _getBreadcrumbFromPro = getBreadcrumbFromProps(props),\n    location = _getBreadcrumbFromPro.location,\n    breadcrumbMap = _getBreadcrumbFromPro.breadcrumbMap;\n\n  // 根据 location 生成 面包屑\n  // Generate breadcrumbs based on location\n  if (location && location.pathname && breadcrumbMap) {\n    return conversionFromLocation(location, breadcrumbMap, props);\n  }\n  return [];\n};\n\n// 声明一个导出函数，接收两个参数：BreadcrumbProps和ProLayoutProps，返回一个BreadcrumbListReturn类型的对象\nexport var getBreadcrumbProps = function getBreadcrumbProps(props, layoutPros // ProLayoutProps类型的layoutPros\n) {\n  // 解构赋值获取props中的breadcrumbRender和props中的itemRender，如果props中没有itemRender则使用默认的defaultItemRender函数\n  var breadcrumbRender = props.breadcrumbRender,\n    propsItemRender = props.itemRender;\n  // 解构赋值获取layoutPros.breadcrumbProps.minLenght的值，如果没有设置，则默认为2\n  var _ref2 = layoutPros.breadcrumbProps || {},\n    _ref2$minLength = _ref2.minLength,\n    minLength = _ref2$minLength === void 0 ? 2 : _ref2$minLength;\n  // 生成面包屑的路由数组，该数组中包含菜单项和面包屑项\n  var routesArray = genBreadcrumbProps(props);\n  // 如果props中有itemRender，则使用props中的itemRender，否则使用默认函数defaultItemRender\n  var itemRender = function itemRender(item) {\n    var renderFunction = propsItemRender || defaultItemRender;\n    for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      rest[_key - 1] = arguments[_key];\n    }\n    return renderFunction === null || renderFunction === void 0 ? void 0 : renderFunction.apply(void 0, [_objectSpread(_objectSpread({}, item), {}, {\n      // 如果item.linkPath存在，则使用item.linkPath，否则使用item.path\n      // @ts-ignore\n      path: item.linkPath || item.path\n    })].concat(rest));\n  };\n  var items = routesArray;\n  // 如果面包屑渲染函数breadcrumbRender存在，则使用其渲染数组items\n  if (breadcrumbRender) {\n    items = breadcrumbRender(items || []) || undefined;\n  }\n  // 如果items（渲染后的数组）的长度小于minLength或者breadcrumbRender为false，则items为undefined\n  if (items && items.length < minLength || breadcrumbRender === false) {\n    items = undefined;\n  }\n  // 如果当前 ant design 包的版本大于等于5.3.0，则返回一个对象{items,itemRender},否则返回一个对象{routes:item,itemRender}\n  return compareVersions(getVersion(), '5.3.0') > -1 ? {\n    items: items,\n    itemRender: itemRender\n  } : {\n    routes: items,\n    itemRender: itemRender\n  };\n};", "// /userInfo/2144/id => ['/userInfo','/userInfo/2144,'/userInfo/2144/id']\nexport function urlToList(url) {\n  if (!url || url === '/') {\n    return ['/'];\n  }\n  var urlList = url.split('/').filter(function (i) {\n    return i;\n  });\n  return urlList.map(function (urlItem, index) {\n    return \"/\".concat(urlList.slice(0, index + 1).join('/'));\n  });\n}", "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { transformRoute } from '@umijs/route-utils';\nfunction fromEntries(iterable) {\n  return _toConsumableArray(iterable).reduce(function (obj, _ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      key = _ref2[0],\n      val = _ref2[1];\n    // eslint-disable-next-line no-param-reassign\n    obj[key] = val;\n    return obj;\n  }, {});\n}\nvar getMenuData = function getMenuData(routes, menu, formatMessage, menuDataRender) {\n  var _transformRoute = transformRoute(routes, (menu === null || menu === void 0 ? void 0 : menu.locale) || false, formatMessage, true),\n    menuData = _transformRoute.menuData,\n    breadcrumb = _transformRoute.breadcrumb;\n  if (!menuDataRender) {\n    return {\n      breadcrumb: fromEntries(breadcrumb),\n      breadcrumbMap: breadcrumb,\n      menuData: menuData\n    };\n  }\n  return getMenuData(menuDataRender(menuData), menu, formatMessage, undefined);\n};\nexport { getMenuData };", "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { omitUndefined } from '@ant-design/pro-utils';\nimport { useEffect, useState } from 'react';\nvar useCurrentMenuLayoutProps = function useCurrentMenuLayoutProps(currentMenu) {\n  var _useState = useState({}),\n    _useState2 = _slicedToArray(_useState, 2),\n    currentMenuLayoutProps = _useState2[0],\n    setCurrentMenuLayoutProps = _useState2[1];\n  useEffect(function () {\n    setCurrentMenuLayoutProps(omitUndefined({\n      // 有时候会变成对象，是原来的方式\n      layout: _typeof(currentMenu.layout) !== 'object' ? currentMenu.layout : undefined,\n      navTheme: currentMenu.navTheme,\n      menuRender: currentMenu.menuRender,\n      footerRender: currentMenu.footerRender,\n      menuHeaderRender: currentMenu.menuHeaderRender,\n      headerRender: currentMenu.headerRender,\n      fixSiderbar: currentMenu.fixSiderbar\n    }));\n  }, [currentMenu.layout, currentMenu.navTheme, currentMenu.menuRender, currentMenu.footerRender, currentMenu.menuHeaderRender, currentMenu.headerRender, currentMenu.fixSiderbar]);\n  return currentMenuLayoutProps;\n};\nexport { useCurrentMenuLayoutProps };", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar _excluded = [\"id\", \"defaultMessage\"],\n  _excluded2 = [\"fixSiderbar\", \"navTheme\", \"layout\"];\nimport { ProConfigProvider, ProProvider, isNeedOpenHash } from '@ant-design/pro-provider';\nimport { coverToNewToken, isBrowser, useBreakpoint, useDocumentTitle, useMountMergeState } from '@ant-design/pro-utils';\nimport { getMatchMenu } from '@umijs/route-utils';\nimport { ConfigProvider, Layout } from 'antd';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport omit from \"rc-util/es/omit\";\nimport warning from \"rc-util/es/warning\";\nimport React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';\nimport useSWR, { useSWRConfig } from 'swr';\nimport { WrapContent } from \"./WrapContent\";\nimport { Logo } from \"./assert/Logo\";\nimport { DefaultFooter as Footer } from \"./components/Footer\";\nimport { DefaultHeader as Header } from \"./components/Header\";\nimport { PageLoading } from \"./components/PageLoading\";\nimport { SiderMenu } from \"./components/SiderMenu\";\nimport { RouteContext } from \"./context/RouteContext\";\nimport { defaultSettings } from \"./defaultSettings\";\nimport { getPageTitleInfo } from \"./getPageTitle\";\nimport { gLocaleObject } from \"./locales\";\nimport { useStyle } from \"./style\";\nimport { getBreadcrumbProps } from \"./utils/getBreadcrumbProps\";\nimport { getMenuData } from \"./utils/getMenuData\";\nimport { useCurrentMenuLayoutProps } from \"./utils/useCurrentMenuLayoutProps\";\nimport { clearMenuItem } from \"./utils/utils\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nvar layoutIndex = 0;\nvar headerRender = function headerRender(props, matchMenuKeys) {\n  var _props$stylish;\n  if (props.headerRender === false || props.pure) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(Header, _objectSpread(_objectSpread({\n    matchMenuKeys: matchMenuKeys\n  }, props), {}, {\n    stylish: (_props$stylish = props.stylish) === null || _props$stylish === void 0 ? void 0 : _props$stylish.header\n  }));\n};\nvar footerRender = function footerRender(props) {\n  if (props.footerRender === false || props.pure) {\n    return null;\n  }\n  if (props.footerRender) {\n    return props.footerRender(_objectSpread({}, props), /*#__PURE__*/_jsx(Footer, {}));\n  }\n  return null;\n};\nvar renderSiderMenu = function renderSiderMenu(props, matchMenuKeys) {\n  var _props$stylish3;\n  var layout = props.layout,\n    isMobile = props.isMobile,\n    selectedKeys = props.selectedKeys,\n    openKeys = props.openKeys,\n    splitMenus = props.splitMenus,\n    suppressSiderWhenMenuEmpty = props.suppressSiderWhenMenuEmpty,\n    menuRender = props.menuRender;\n  if (props.menuRender === false || props.pure) {\n    return null;\n  }\n  var menuData = props.menuData;\n\n  /** 如果是分割菜单模式，需要专门实现一下 */\n  if (splitMenus && (openKeys !== false || layout === 'mix') && !isMobile) {\n    var _ref = selectedKeys || matchMenuKeys,\n      _ref2 = _slicedToArray(_ref, 1),\n      key = _ref2[0];\n    if (key) {\n      var _props$menuData;\n      menuData = ((_props$menuData = props.menuData) === null || _props$menuData === void 0 || (_props$menuData = _props$menuData.find(function (item) {\n        return item.key === key;\n      })) === null || _props$menuData === void 0 ? void 0 : _props$menuData.children) || [];\n    } else {\n      menuData = [];\n    }\n  }\n  // 这里走了可以少一次循环\n  var clearMenuData = clearMenuItem(menuData || []);\n  if (clearMenuData && (clearMenuData === null || clearMenuData === void 0 ? void 0 : clearMenuData.length) < 1 && (splitMenus || suppressSiderWhenMenuEmpty)) {\n    return null;\n  }\n  if (layout === 'top' && !isMobile) {\n    var _props$stylish2;\n    return /*#__PURE__*/_jsx(SiderMenu, _objectSpread(_objectSpread({\n      matchMenuKeys: matchMenuKeys\n    }, props), {}, {\n      hide: true,\n      stylish: (_props$stylish2 = props.stylish) === null || _props$stylish2 === void 0 ? void 0 : _props$stylish2.sider\n    }));\n  }\n  var defaultDom = /*#__PURE__*/_jsx(SiderMenu, _objectSpread(_objectSpread({\n    matchMenuKeys: matchMenuKeys\n  }, props), {}, {\n    // 这里走了可以少一次循环\n    menuData: clearMenuData,\n    stylish: (_props$stylish3 = props.stylish) === null || _props$stylish3 === void 0 ? void 0 : _props$stylish3.sider\n  }));\n  if (menuRender) {\n    return menuRender(props, defaultDom);\n  }\n  return defaultDom;\n};\nvar defaultPageTitleRender = function defaultPageTitleRender(pageProps, props) {\n  var pageTitleRender = props.pageTitleRender;\n  var pageTitleInfo = getPageTitleInfo(pageProps);\n  if (pageTitleRender === false) {\n    return {\n      title: props.title || '',\n      id: '',\n      pageName: ''\n    };\n  }\n  if (pageTitleRender) {\n    var title = pageTitleRender(pageProps, pageTitleInfo.title, pageTitleInfo);\n    if (typeof title === 'string') {\n      return getPageTitleInfo(_objectSpread(_objectSpread({}, pageTitleInfo), {}, {\n        title: title\n      }));\n    }\n    warning(typeof title === 'string', 'pro-layout: renderPageTitle return value should be a string');\n  }\n  return pageTitleInfo;\n};\nvar getPaddingInlineStart = function getPaddingInlineStart(hasLeftPadding, collapsed, siderWidth) {\n  if (hasLeftPadding) {\n    return collapsed ? 64 : siderWidth;\n  }\n  return 0;\n};\n\n/**\n * 🌃 Powerful and easy to use beautiful layout 🏄‍ Support multiple topics and layout types\n *\n * @param props\n */\nvar BaseProLayout = function BaseProLayout(props) {\n  var _props$prefixCls, _location$pathname, _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout5, _token$layout6, _token$layout7, _token$layout8, _token$layout9, _token$layout10, _token$layout11, _token$layout12;\n  var _ref3 = props || {},\n    children = _ref3.children,\n    propsOnCollapse = _ref3.onCollapse,\n    _ref3$location = _ref3.location,\n    location = _ref3$location === void 0 ? {\n      pathname: '/'\n    } : _ref3$location,\n    contentStyle = _ref3.contentStyle,\n    route = _ref3.route,\n    defaultCollapsed = _ref3.defaultCollapsed,\n    style = _ref3.style,\n    propsSiderWidth = _ref3.siderWidth,\n    menu = _ref3.menu,\n    siderMenuType = _ref3.siderMenuType,\n    propsIsChildrenLayout = _ref3.isChildrenLayout,\n    menuDataRender = _ref3.menuDataRender,\n    actionRef = _ref3.actionRef,\n    bgLayoutImgList = _ref3.bgLayoutImgList,\n    propsFormatMessage = _ref3.formatMessage,\n    loading = _ref3.loading;\n  var siderWidth = useMemo(function () {\n    if (propsSiderWidth) return propsSiderWidth;\n    if (props.layout === 'mix') return 215;\n    return 256;\n  }, [props.layout, propsSiderWidth]);\n  var context = useContext(ConfigProvider.ConfigContext);\n  var prefixCls = (_props$prefixCls = props.prefixCls) !== null && _props$prefixCls !== void 0 ? _props$prefixCls : context.getPrefixCls('pro');\n  var _useMountMergeState = useMountMergeState(false, {\n      value: menu === null || menu === void 0 ? void 0 : menu.loading,\n      onChange: menu === null || menu === void 0 ? void 0 : menu.onLoadingChange\n    }),\n    _useMountMergeState2 = _slicedToArray(_useMountMergeState, 2),\n    menuLoading = _useMountMergeState2[0],\n    setMenuLoading = _useMountMergeState2[1];\n\n  // give a default key for swr\n  var _useState = useState(function () {\n      layoutIndex += 1;\n      return \"pro-layout-\".concat(layoutIndex);\n    }),\n    _useState2 = _slicedToArray(_useState, 1),\n    defaultId = _useState2[0];\n\n  /**\n   * 处理国际化相关 formatMessage\n   * 如果有用户配置的以用户为主\n   * 如果没有用自己实现的\n   */\n  var formatMessage = useCallback(function (_ref4) {\n    var id = _ref4.id,\n      defaultMessage = _ref4.defaultMessage,\n      restParams = _objectWithoutProperties(_ref4, _excluded);\n    if (propsFormatMessage) {\n      return propsFormatMessage(_objectSpread({\n        id: id,\n        defaultMessage: defaultMessage\n      }, restParams));\n    }\n    var locales = gLocaleObject();\n    return locales[id] ? locales[id] : defaultMessage;\n  }, [propsFormatMessage]);\n  var _useSWR = useSWR([defaultId, menu === null || menu === void 0 ? void 0 : menu.params], /*#__PURE__*/function () {\n      var _ref6 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(_ref5) {\n        var _menu$request;\n        var _ref7, params, menuDataItems;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _ref7 = _slicedToArray(_ref5, 2), params = _ref7[1];\n              setMenuLoading(true);\n              _context.next = 4;\n              return menu === null || menu === void 0 || (_menu$request = menu.request) === null || _menu$request === void 0 ? void 0 : _menu$request.call(menu, params || {}, (route === null || route === void 0 ? void 0 : route.children) || (route === null || route === void 0 ? void 0 : route.routes) || []);\n            case 4:\n              menuDataItems = _context.sent;\n              setMenuLoading(false);\n              return _context.abrupt(\"return\", menuDataItems);\n            case 7:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function (_x) {\n        return _ref6.apply(this, arguments);\n      };\n    }(), {\n      revalidateOnFocus: false,\n      shouldRetryOnError: false,\n      revalidateOnReconnect: false\n    }),\n    data = _useSWR.data,\n    mutate = _useSWR.mutate,\n    isLoading = _useSWR.isLoading;\n  useEffect(function () {\n    setMenuLoading(isLoading);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isLoading]);\n  var _useSWRConfig = useSWRConfig(),\n    cache = _useSWRConfig.cache;\n  useEffect(function () {\n    return function () {\n      if (cache instanceof Map) cache.delete(defaultId);\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  var menuInfoData = useMemo(function () {\n    return getMenuData(data || (route === null || route === void 0 ? void 0 : route.children) || (route === null || route === void 0 ? void 0 : route.routes) || [], menu, formatMessage, menuDataRender);\n  }, [formatMessage, menu, menuDataRender, data, route === null || route === void 0 ? void 0 : route.children, route === null || route === void 0 ? void 0 : route.routes]);\n  var _ref8 = menuInfoData || {},\n    breadcrumb = _ref8.breadcrumb,\n    breadcrumbMap = _ref8.breadcrumbMap,\n    _ref8$menuData = _ref8.menuData,\n    menuData = _ref8$menuData === void 0 ? [] : _ref8$menuData;\n  if (actionRef && menu !== null && menu !== void 0 && menu.request) {\n    actionRef.current = {\n      reload: function reload() {\n        mutate();\n      }\n    };\n  }\n  var matchMenus = useMemo(function () {\n    return getMatchMenu(location.pathname || '/', menuData || [], true);\n  }, [location.pathname, menuData]);\n  var matchMenuKeys = useMemo(function () {\n    return Array.from(new Set(matchMenus.map(function (item) {\n      return item.key || item.path || '';\n    })));\n  }, [matchMenus]);\n\n  // 当前选中的menu，一般不会为空\n  var currentMenu = matchMenus[matchMenus.length - 1] || {};\n  var currentMenuLayoutProps = useCurrentMenuLayoutProps(currentMenu);\n  var _props$currentMenuLay = _objectSpread(_objectSpread({}, props), currentMenuLayoutProps),\n    fixSiderbar = _props$currentMenuLay.fixSiderbar,\n    navTheme = _props$currentMenuLay.navTheme,\n    propsLayout = _props$currentMenuLay.layout,\n    rest = _objectWithoutProperties(_props$currentMenuLay, _excluded2);\n  var colSize = useBreakpoint();\n  var isMobile = useMemo(function () {\n    return (colSize === 'sm' || colSize === 'xs') && !props.disableMobile;\n  }, [colSize, props.disableMobile]);\n\n  // If it is a fix menu, calculate padding\n  // don't need padding in phone mode\n  /* Checking if the menu is loading and if it is, it will return a skeleton loading screen. */\n  var hasLeftPadding = propsLayout !== 'top' && !isMobile;\n  var _useMergedState = useMergedState(function () {\n      if (defaultCollapsed !== undefined) return defaultCollapsed;\n      if (process.env.NODE_ENV === 'TEST') return false;\n      if (isMobile) return true;\n      if (colSize === 'md') return true;\n      return false;\n    }, {\n      value: props.collapsed,\n      onChange: propsOnCollapse\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    collapsed = _useMergedState2[0],\n    onCollapse = _useMergedState2[1];\n\n  // Splicing parameters, adding menuData and formatMessage in props\n  var defaultProps = omit(_objectSpread(_objectSpread(_objectSpread({\n    prefixCls: prefixCls\n  }, props), {}, {\n    siderWidth: siderWidth\n  }, currentMenuLayoutProps), {}, {\n    formatMessage: formatMessage,\n    breadcrumb: breadcrumb,\n    menu: _objectSpread(_objectSpread({}, menu), {}, {\n      type: siderMenuType || (menu === null || menu === void 0 ? void 0 : menu.type),\n      loading: menuLoading\n    }),\n    layout: propsLayout\n  }), ['className', 'style', 'breadcrumbRender']);\n\n  // gen page title\n  var pageTitleInfo = defaultPageTitleRender(_objectSpread(_objectSpread({\n    pathname: location.pathname\n  }, defaultProps), {}, {\n    breadcrumbMap: breadcrumbMap\n  }), props);\n\n  // gen breadcrumbProps, parameter for pageHeader\n  var breadcrumbProps = getBreadcrumbProps(_objectSpread(_objectSpread({}, defaultProps), {}, {\n    breadcrumbRender: props.breadcrumbRender,\n    breadcrumbMap: breadcrumbMap\n  }), props);\n\n  // render sider dom\n  var siderMenuDom = renderSiderMenu(_objectSpread(_objectSpread({}, defaultProps), {}, {\n    menuData: menuData,\n    onCollapse: onCollapse,\n    isMobile: isMobile,\n    collapsed: collapsed\n  }), matchMenuKeys);\n\n  // render header dom\n  var headerDom = headerRender(_objectSpread(_objectSpread({}, defaultProps), {}, {\n    children: null,\n    hasSiderMenu: !!siderMenuDom,\n    menuData: menuData,\n    isMobile: isMobile,\n    collapsed: collapsed,\n    onCollapse: onCollapse\n  }), matchMenuKeys);\n\n  // render footer dom\n  var footerDom = footerRender(_objectSpread({\n    isMobile: isMobile,\n    collapsed: collapsed\n  }, defaultProps));\n  var _useContext = useContext(RouteContext),\n    contextIsChildrenLayout = _useContext.isChildrenLayout;\n\n  // 如果 props 中定义，以 props 为准\n  var isChildrenLayout = propsIsChildrenLayout !== undefined ? propsIsChildrenLayout : contextIsChildrenLayout;\n  var proLayoutClassName = \"\".concat(prefixCls, \"-layout\");\n  var _useStyle = useStyle(proLayoutClassName),\n    wrapSSR = _useStyle.wrapSSR,\n    hashId = _useStyle.hashId;\n\n  // gen className\n  var className = classNames(props.className, hashId, 'ant-design-pro', proLayoutClassName, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"screen-\".concat(colSize), colSize), \"\".concat(proLayoutClassName, \"-top-menu\"), propsLayout === 'top'), \"\".concat(proLayoutClassName, \"-is-children\"), isChildrenLayout), \"\".concat(proLayoutClassName, \"-fix-siderbar\"), fixSiderbar), \"\".concat(proLayoutClassName, \"-\").concat(propsLayout), propsLayout));\n\n  /** 计算 slider 的宽度 */\n  var leftSiderWidth = getPaddingInlineStart(!!hasLeftPadding, collapsed, siderWidth);\n\n  // siderMenuDom 为空的时候，不需要 padding\n  var genLayoutStyle = {\n    position: 'relative'\n  };\n\n  // if is some layout children, don't need min height\n  if (isChildrenLayout || contentStyle && contentStyle.minHeight) {\n    genLayoutStyle.minHeight = 0;\n  }\n\n  /** 页面切换的时候触发 */\n  useEffect(function () {\n    var _props$onPageChange;\n    (_props$onPageChange = props.onPageChange) === null || _props$onPageChange === void 0 || _props$onPageChange.call(props, props.location);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [location.pathname, (_location$pathname = location.pathname) === null || _location$pathname === void 0 ? void 0 : _location$pathname.search]);\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    hasFooterToolbar = _useState4[0],\n    setHasFooterToolbar = _useState4[1];\n  /**\n   * 使用number是因为多标签页的时候有多个 PageContainer，只有有任意一个就应该展示这个className\n   */\n  var _useState5 = useState(0),\n    _useState6 = _slicedToArray(_useState5, 2),\n    hasPageContainer = _useState6[0],\n    setHasPageContainer = _useState6[1];\n  useDocumentTitle(pageTitleInfo, props.title || false);\n  var _useContext2 = useContext(ProProvider),\n    token = _useContext2.token;\n  var bgImgStyleList = useMemo(function () {\n    if (bgLayoutImgList && bgLayoutImgList.length > 0) {\n      return bgLayoutImgList === null || bgLayoutImgList === void 0 ? void 0 : bgLayoutImgList.map(function (item, index) {\n        return /*#__PURE__*/_jsx(\"img\", {\n          src: item.src,\n          style: _objectSpread({\n            position: 'absolute'\n          }, item)\n        }, index);\n      });\n    }\n    return null;\n  }, [bgLayoutImgList]);\n  return wrapSSR( /*#__PURE__*/_jsx(RouteContext.Provider, {\n    value: _objectSpread(_objectSpread({}, defaultProps), {}, {\n      breadcrumb: breadcrumbProps,\n      menuData: menuData,\n      isMobile: isMobile,\n      collapsed: collapsed,\n      hasPageContainer: hasPageContainer,\n      setHasPageContainer: setHasPageContainer,\n      isChildrenLayout: true,\n      title: pageTitleInfo.pageName,\n      hasSiderMenu: !!siderMenuDom,\n      hasHeader: !!headerDom,\n      siderWidth: leftSiderWidth,\n      hasFooter: !!footerDom,\n      hasFooterToolbar: hasFooterToolbar,\n      setHasFooterToolbar: setHasFooterToolbar,\n      pageTitleInfo: pageTitleInfo,\n      matchMenus: matchMenus,\n      matchMenuKeys: matchMenuKeys,\n      currentMenu: currentMenu\n    }),\n    children: props.pure ? /*#__PURE__*/_jsx(_Fragment, {\n      children: children\n    }) : /*#__PURE__*/_jsxs(\"div\", {\n      className: className,\n      children: [bgImgStyleList || (_token$layout = token.layout) !== null && _token$layout !== void 0 && _token$layout.bgLayout ? /*#__PURE__*/_jsx(\"div\", {\n        className: classNames(\"\".concat(proLayoutClassName, \"-bg-list\"), hashId),\n        children: bgImgStyleList\n      }) : null, /*#__PURE__*/_jsxs(Layout, {\n        style: _objectSpread({\n          minHeight: '100%',\n          // hack style\n          flexDirection: siderMenuDom ? 'row' : undefined\n        }, style),\n        children: [/*#__PURE__*/_jsx(ConfigProvider\n        // @ts-ignore\n        , {\n          theme: {\n            hashed: isNeedOpenHash(),\n            token: {\n              controlHeightLG: ((_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.sider) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.menuHeight) || (token === null || token === void 0 ? void 0 : token.controlHeightLG)\n            },\n            components: {\n              Menu: coverToNewToken({\n                colorItemBg: ((_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.sider) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorMenuBackground) || 'transparent',\n                colorSubItemBg: ((_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.sider) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorMenuBackground) || 'transparent',\n                radiusItem: token.borderRadius,\n                colorItemBgSelected: ((_token$layout5 = token.layout) === null || _token$layout5 === void 0 || (_token$layout5 = _token$layout5.sider) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.colorBgMenuItemSelected) || (token === null || token === void 0 ? void 0 : token.colorBgTextHover),\n                colorItemBgHover: ((_token$layout6 = token.layout) === null || _token$layout6 === void 0 || (_token$layout6 = _token$layout6.sider) === null || _token$layout6 === void 0 ? void 0 : _token$layout6.colorBgMenuItemHover) || (token === null || token === void 0 ? void 0 : token.colorBgTextHover),\n                colorItemBgActive: ((_token$layout7 = token.layout) === null || _token$layout7 === void 0 || (_token$layout7 = _token$layout7.sider) === null || _token$layout7 === void 0 ? void 0 : _token$layout7.colorBgMenuItemActive) || (token === null || token === void 0 ? void 0 : token.colorBgTextActive),\n                colorItemBgSelectedHorizontal: ((_token$layout8 = token.layout) === null || _token$layout8 === void 0 || (_token$layout8 = _token$layout8.sider) === null || _token$layout8 === void 0 ? void 0 : _token$layout8.colorBgMenuItemSelected) || (token === null || token === void 0 ? void 0 : token.colorBgTextHover),\n                colorActiveBarWidth: 0,\n                colorActiveBarHeight: 0,\n                colorActiveBarBorderSize: 0,\n                colorItemText: ((_token$layout9 = token.layout) === null || _token$layout9 === void 0 || (_token$layout9 = _token$layout9.sider) === null || _token$layout9 === void 0 ? void 0 : _token$layout9.colorTextMenu) || (token === null || token === void 0 ? void 0 : token.colorTextSecondary),\n                colorItemTextHover: ((_token$layout10 = token.layout) === null || _token$layout10 === void 0 || (_token$layout10 = _token$layout10.sider) === null || _token$layout10 === void 0 ? void 0 : _token$layout10.colorTextMenuItemHover) || 'rgba(0, 0, 0, 0.85)',\n                // 悬浮态\n                colorItemTextSelected: ((_token$layout11 = token.layout) === null || _token$layout11 === void 0 || (_token$layout11 = _token$layout11.sider) === null || _token$layout11 === void 0 ? void 0 : _token$layout11.colorTextMenuSelected) || 'rgba(0, 0, 0, 1)',\n                popupBg: token === null || token === void 0 ? void 0 : token.colorBgElevated,\n                subMenuItemBg: token === null || token === void 0 ? void 0 : token.colorBgElevated,\n                darkSubMenuItemBg: 'transparent',\n                darkPopupBg: token === null || token === void 0 ? void 0 : token.colorBgElevated\n              })\n            }\n          },\n          children: siderMenuDom\n        }), /*#__PURE__*/_jsxs(\"div\", {\n          style: genLayoutStyle,\n          className: \"\".concat(proLayoutClassName, \"-container \").concat(hashId).trim(),\n          children: [headerDom, /*#__PURE__*/_jsx(WrapContent, _objectSpread(_objectSpread({\n            hasPageContainer: hasPageContainer,\n            isChildrenLayout: isChildrenLayout\n          }, rest), {}, {\n            hasHeader: !!headerDom,\n            prefixCls: proLayoutClassName,\n            style: contentStyle,\n            children: loading ? /*#__PURE__*/_jsx(PageLoading, {}) : children\n          })), footerDom, hasFooterToolbar && /*#__PURE__*/_jsx(\"div\", {\n            className: \"\".concat(proLayoutClassName, \"-has-footer\"),\n            style: {\n              height: 64,\n              marginBlockStart: (_token$layout12 = token.layout) === null || _token$layout12 === void 0 || (_token$layout12 = _token$layout12.pageContainer) === null || _token$layout12 === void 0 ? void 0 : _token$layout12.paddingBlockPageContainerContent\n            }\n          })]\n        })]\n      })]\n    })\n  }));\n};\nvar ProLayout = function ProLayout(props) {\n  var colorPrimary = props.colorPrimary;\n  var darkProps = props.navTheme !== undefined ? {\n    dark: props.navTheme === 'realDark'\n  } : {};\n  return /*#__PURE__*/_jsx(ConfigProvider, {\n    theme: colorPrimary ? {\n      token: {\n        colorPrimary: colorPrimary\n      }\n    } : undefined,\n    children: /*#__PURE__*/_jsx(ProConfigProvider, _objectSpread(_objectSpread({}, darkProps), {}, {\n      token: props.token,\n      prefixCls: props.prefixCls,\n      children: /*#__PURE__*/_jsx(BaseProLayout, _objectSpread(_objectSpread({\n        logo: /*#__PURE__*/_jsx(Logo, {})\n      }, defaultSettings), {}, {\n        location: isBrowser() ? window.location : undefined\n      }, props))\n    }))\n  });\n};\nexport { ProLayout };", "import { useEffect } from 'react';\nimport { isBrowser } from \"../../isBrowser\";\nexport function useDocumentTitle(titleInfo, appDefaultTitle) {\n  var titleText = typeof titleInfo.pageName === 'string' ? titleInfo.title : appDefaultTitle;\n  useEffect(function () {\n    if (isBrowser() && titleText) {\n      document.title = titleText;\n    }\n  }, [titleInfo.title, titleText]);\n}", "// @ts-nocheck\n// This file is generated by Umi automatically\n// DO NOT CHANGE IT MANUALLY!\nimport React from 'react';\n\nconst LogoIcon: React.FC = () => {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      width=\"32\"\n      height=\"32\"\n      viewBox=\"0 0 200 200\"\n    >\n      <defs>\n        <linearGradient\n          id=\"linearGradient-1\"\n          x1=\"62.102%\"\n          x2=\"108.197%\"\n          y1=\"0%\"\n          y2=\"37.864%\"\n        >\n          <stop offset=\"0%\" stopColor=\"#4285EB\"></stop>\n          <stop offset=\"100%\" stopColor=\"#2EC7FF\"></stop>\n        </linearGradient>\n        <linearGradient\n          id=\"linearGradient-2\"\n          x1=\"69.644%\"\n          x2=\"54.043%\"\n          y1=\"0%\"\n          y2=\"108.457%\"\n        >\n          <stop offset=\"0%\" stopColor=\"#29CDFF\"></stop>\n          <stop offset=\"37.86%\" stopColor=\"#148EFF\"></stop>\n          <stop offset=\"100%\" stopColor=\"#0A60FF\"></stop>\n        </linearGradient>\n        <linearGradient\n          id=\"linearGradient-3\"\n          x1=\"69.691%\"\n          x2=\"16.723%\"\n          y1=\"-12.974%\"\n          y2=\"117.391%\"\n        >\n          <stop offset=\"0%\" stopColor=\"#FA816E\"></stop>\n          <stop offset=\"41.473%\" stopColor=\"#F74A5C\"></stop>\n          <stop offset=\"100%\" stopColor=\"#F51D2C\"></stop>\n        </linearGradient>\n        <linearGradient\n          id=\"linearGradient-4\"\n          x1=\"68.128%\"\n          x2=\"30.44%\"\n          y1=\"-35.691%\"\n          y2=\"114.943%\"\n        >\n          <stop offset=\"0%\" stopColor=\"#FA8E7D\"></stop>\n          <stop offset=\"51.264%\" stopColor=\"#F74A5C\"></stop>\n          <stop offset=\"100%\" stopColor=\"#F51D2C\"></stop>\n        </linearGradient>\n      </defs>\n      <g fill=\"none\" fillRule=\"evenodd\" stroke=\"none\" strokeWidth=\"1\">\n        <g transform=\"translate(-20 -20)\">\n          <g transform=\"translate(20 20)\">\n            <g>\n              <g fillRule=\"nonzero\">\n                <g>\n                  <path\n                    fill=\"url(#linearGradient-1)\"\n                    d=\"M91.588 4.177L4.18 91.513a11.981 11.981 0 000 16.974l87.408 87.336a12.005 12.005 0 0016.989 0l36.648-36.618c4.209-4.205 4.209-11.023 0-15.228-4.208-4.205-11.031-4.205-15.24 0l-27.783 27.76c-1.17 1.169-2.945 1.169-4.114 0l-69.802-69.744c-1.17-1.169-1.17-2.942 0-4.11l69.802-69.745c1.17-1.169 2.944-1.169 4.114 0l27.783 27.76c4.209 4.205 11.032 4.205 15.24 0 4.209-4.205 4.209-11.022 0-15.227L108.581 4.056c-4.719-4.594-12.312-4.557-16.993.12z\"\n                  ></path>\n                  <path\n                    fill=\"url(#linearGradient-2)\"\n                    d=\"M91.588 4.177L4.18 91.513a11.981 11.981 0 000 16.974l87.408 87.336a12.005 12.005 0 0016.989 0l36.648-36.618c4.209-4.205 4.209-11.023 0-15.228-4.208-4.205-11.031-4.205-15.24 0l-27.783 27.76c-1.17 1.169-2.945 1.169-4.114 0l-69.802-69.744c-1.17-1.169-1.17-2.942 0-4.11l69.802-69.745c2.912-2.51 7.664-7.596 14.642-8.786 5.186-.883 10.855 1.062 17.009 5.837L108.58 4.056c-4.719-4.594-12.312-4.557-16.993.12z\"\n                  ></path>\n                </g>\n                <path\n                  fill=\"url(#linearGradient-3)\"\n                  d=\"M153.686 135.855c4.208 4.205 11.031 4.205 15.24 0l27.034-27.012c4.7-4.696 4.7-12.28 0-16.974l-27.27-27.15c-4.218-4.2-11.043-4.195-15.254.013-4.209 4.205-4.209 11.022 0 15.227l18.418 18.403c1.17 1.169 1.17 2.943 0 4.111l-18.168 18.154c-4.209 4.205-4.209 11.023 0 15.228z\"\n                ></path>\n              </g>\n              <ellipse\n                cx=\"100.519\"\n                cy=\"100.437\"\n                fill=\"url(#linearGradient-4)\"\n                rx=\"23.6\"\n                ry=\"23.581\"\n              ></ellipse>\n            </g>\n          </g>\n        </g>\n      </g>\n    </svg>\n  );\n};\n\nexport default LogoIcon;\n", "// @ts-nocheck\n// This file is generated by <PERSON><PERSON> automatically\n// DO NOT CHANGE IT MANUALLY!\nimport React from 'react';\nimport { history, type IRoute } from '@umijs/max';\nimport { Result, Button } from 'antd';\n\nconst Exception: React.FC<{\n  children: React.ReactNode;\n  route?: IRoute;\n  notFound?: React.ReactNode;\n  noAccessible?: React.ReactNode;\n  unAccessible?: React.ReactNode;\n  noFound?: React.ReactNode;\n}> = (props) => (\n  // render custom 404\n  (!props.route && (props.noFound || props.notFound)) ||\n  // render custom 403\n  (props.route?.unaccessible && (props.unAccessible || props.noAccessible)) ||\n  // render default exception\n  ((!props.route || props.route?.unaccessible) && (\n    <Result\n      status={props.route ? '403' : '404'}\n      title={props.route ? '403' : '404'}\n      subTitle={props.route ? '抱歉，你无权访问该页面' : '抱歉，你访问的页面不存在'}\n      extra={\n        <Button type=\"primary\" onClick={() => history.push('/')}>\n          返回首页\n        </Button>\n      }\n    />\n  )) ||\n  // normal render\n  props.children\n);\n\nexport default Exception;\n", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport LogoutOutlinedSvg from \"@ant-design/icons-svg/es/asn/LogoutOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar LogoutOutlined = function LogoutOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: LogoutOutlinedSvg\n  }));\n};\nvar RefIcon = /*#__PURE__*/React.forwardRef(LogoutOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'LogoutOutlined';\n}\nexport default RefIcon;", "// @ts-nocheck\n// This file is generated by <PERSON><PERSON> automatically\n// DO NOT CHANGE IT MANUALLY!\n/// <reference types=\"@ant-design/pro-components\" />\n/// <reference types=\"antd\" />\n\nimport {\n  Link, useLocation, useNavigate, Outlet, useAppData, matchRoutes,\n  type IRoute\n} from '@umijs/max';\nimport React, { useMemo } from 'react';\nimport {\n  ProLayout,\n} from \"F:/Project/teamAuth/frontend/node_modules/@ant-design/pro-components\";\nimport './Layout.css';\nimport Logo from './Logo';\nimport Exception from './Exception';\nimport { getRightRenderContent } from './rightRender';\nimport { useModel } from '@@/plugin-model';\nimport { useAccessMarkedRoutes } from '@@/plugin-access';\n\n\n// 过滤出需要显示的路由, 这里的filterFn 指 不希望显示的层级\nconst filterRoutes = (routes: IRoute[], filterFn: (route: IRoute) => boolean) => {\n  if (routes.length === 0) {\n    return []\n  }\n\n  let newRoutes = []\n  for (const route of routes) {\n    const newRoute = {...route };\n    if (filterFn(route)) {\n      if (Array.isArray(newRoute.routes)) {\n        newRoutes.push(...filterRoutes(newRoute.routes, filterFn))\n      }\n    } else {\n      if (Array.isArray(newRoute.children)) {\n        newRoute.children = filterRoutes(newRoute.children, filterFn);\n        newRoute.routes = newRoute.children;\n      }\n      newRoutes.push(newRoute);\n    }\n  }\n\n  return newRoutes;\n}\n\n// 格式化路由 处理因 wrapper 导致的 菜单 path 不一致\nconst mapRoutes = (routes: IRoute[]) => {\n  if (routes.length === 0) {\n    return []\n  }\n  return routes.map(route => {\n    // 需要 copy 一份, 否则会污染原始数据\n    const newRoute = {...route}\n    if (route.originPath) {\n      newRoute.path = route.originPath\n    }\n\n    if (Array.isArray(route.routes)) {\n      newRoute.routes = mapRoutes(route.routes);\n    }\n\n    if (Array.isArray(route.children)) {\n      newRoute.children = mapRoutes(route.children);\n    }\n\n    return newRoute\n  })\n}\n\nexport default (props: any) => {\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { clientRoutes, pluginManager } = useAppData();\n  const initialInfo = (useModel && useModel('@@initialState')) || {\n    initialState: undefined,\n    loading: false,\n    setInitialState: null,\n  };\n  const { initialState, loading, setInitialState } = initialInfo;\n  const userConfig = {\n  \"locale\": false,\n  \"navTheme\": \"light\",\n  \"colorPrimary\": \"#1890ff\",\n  \"layout\": \"side\",\n  \"contentWidth\": \"Fluid\",\n  \"fixedHeader\": false,\n  \"fixSiderbar\": true,\n  \"colorWeak\": false,\n  \"title\": \"团队协作管理系统\",\n  \"pwa\": false,\n  \"logo\": \"/logo.svg\",\n  \"iconfontUrl\": \"\",\n  \"token\": {}\n};\nconst formatMessage = undefined;\n  const runtimeConfig = pluginManager.applyPlugins({\n    key: 'layout',\n    type: 'modify',\n    initialValue: {\n      ...initialInfo\n    },\n  });\n\n\n  // 现在的 layout 及 wrapper 实现是通过父路由的形式实现的, 会导致路由数据多了冗余层级, proLayout 消费时, 无法正确展示菜单, 这里对冗余数据进行过滤操作\n  const newRoutes = filterRoutes(clientRoutes.filter(route => route.id === 'ant-design-pro-layout'), (route) => {\n    return (!!route.isLayout && route.id !== 'ant-design-pro-layout') || !!route.isWrapper;\n  })\n  const [route] = useAccessMarkedRoutes(mapRoutes(newRoutes));\n\n  const matchedRoute = useMemo(() => matchRoutes(route.children, location.pathname)?.pop?.()?.route, [location.pathname]);\n\n  return (\n    <ProLayout\n      route={route}\n      location={location}\n      title={userConfig.title || 'teamauth-frontend'}\n      navTheme=\"dark\"\n      siderWidth={256}\n      onMenuHeaderClick={(e) => {\n        e.stopPropagation();\n        e.preventDefault();\n        navigate('/');\n      }}\n      formatMessage={userConfig.formatMessage || formatMessage}\n      menu={{ locale: userConfig.locale }}\n      logo={Logo}\n      menuItemRender={(menuItemProps, defaultDom) => {\n        if (menuItemProps.isUrl || menuItemProps.children) {\n          return defaultDom;\n        }\n        if (menuItemProps.path && location.pathname !== menuItemProps.path) {\n          return (\n            // handle wildcard route path, for example /slave/* from qiankun\n            <Link to={menuItemProps.path.replace('/*', '')} target={menuItemProps.target}>\n              {defaultDom}\n            </Link>\n          );\n        }\n        return defaultDom;\n      }}\n      itemRender={(route, _, routes) => {\n        const { breadcrumbName, title, path } = route;\n        const label = title || breadcrumbName\n        const last = routes[routes.length - 1]\n        if (last) {\n          if (last.path === path || last.linkPath === path) {\n            return <span>{label}</span>;\n          }\n        }\n        return <Link to={path}>{label}</Link>;\n      }}\n      disableContentMargin\n      fixSiderbar\n      fixedHeader\n      {...runtimeConfig}\n      rightContentRender={\n        runtimeConfig.rightContentRender !== false &&\n        ((layoutProps) => {\n          const dom = getRightRenderContent({\n            runtimeConfig,\n            loading,\n            initialState,\n            setInitialState,\n          });\n          if (runtimeConfig.rightContentRender) {\n            return runtimeConfig.rightContentRender(layoutProps, dom, {\n              // BREAK CHANGE userConfig > runtimeConfig\n              userConfig,\n              runtimeConfig,\n              loading,\n              initialState,\n              setInitialState,\n            });\n          }\n          return dom;\n        })\n      }\n    >\n      <Exception\n        route={matchedRoute}\n        noFound={runtimeConfig?.noFound}\n        notFound={runtimeConfig?.notFound}\n        unAccessible={runtimeConfig?.unAccessible}\n        noAccessible={runtimeConfig?.noAccessible}\n      >\n        {runtimeConfig.childrenRender\n          ? runtimeConfig.childrenRender(<Outlet />, props)\n          : <Outlet />\n        }\n      </Exception>\n    </ProLayout>\n  );\n}\n", "// @ts-nocheck\n// This file is generated by Um<PERSON> automatically\n// DO NOT CHANGE IT MANUALLY!\nimport React from 'react';\nimport { Avatar, version, Dropdown, Menu, Spin } from 'antd';\nimport { LogoutOutlined } from 'F:/Project/teamAuth/frontend/node_modules/@umijs/plugins/node_modules/@ant-design/icons';\n\nexport function getRightRenderContent (opts: {\n   runtimeConfig: any,\n   loading: boolean,\n   initialState: any,\n   setInitialState: any,\n }) {\n  if (opts.runtimeConfig.rightRender) {\n    return opts.runtimeConfig.rightRender(\n      opts.initialState,\n      opts.setInitialState,\n      opts.runtimeConfig,\n    );\n  }\n\n  const showAvatar = opts.initialState?.avatar || opts.initialState?.name || opts.runtimeConfig.logout;\n  const disableAvatarImg = opts.initialState?.avatar === false;\n  const nameClassName = disableAvatarImg ? 'umi-plugin-layout-name umi-plugin-layout-hide-avatar-img' : 'umi-plugin-layout-name';\n  const avatar =\n    showAvatar ? (\n      <span className=\"umi-plugin-layout-action\">\n        {!disableAvatarImg ?\n          (\n            <Avatar\n              size=\"small\"\n              className=\"umi-plugin-layout-avatar\"\n              src={\n                opts.initialState?.avatar ||\n                \"https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png\"\n              }\n              alt=\"avatar\"\n            />\n          ) : null}\n        <span className={nameClassName}>{opts.initialState?.name}</span>\n      </span>\n    ) : null;\n\n\n  if (opts.loading) {\n    return (\n      <div className=\"umi-plugin-layout-right\">\n        <Spin size=\"small\" style={ { marginLeft: 8, marginRight: 8 } } />\n      </div>\n    );\n  }\n\n  // 如果没有打开Locale，并且头像为空就取消掉这个返回的内容\n    if(!avatar) return null;\n\n  const langMenu = {\n    className: \"umi-plugin-layout-menu\",\n    selectedKeys: [],\n    items: [\n      {\n        key: \"logout\",\n        label: (\n          <>\n            <LogoutOutlined />\n            退出登录\n          </>\n        ),\n        onClick: () => {\n          opts?.runtimeConfig?.logout?.(opts.initialState);\n        },\n      },\n    ],\n  };\n  // antd@5 和  4.24 之后推荐使用 menu，性能更好\n  let dropdownProps;\n  if (version.startsWith(\"5.\") || version.startsWith(\"4.24.\")) {\n    dropdownProps = { menu: langMenu };\n  } else if (version.startsWith(\"3.\")) {\n    dropdownProps = {\n      overlay: (\n        <Menu>\n          {langMenu.items.map((item) => (\n            <Menu.Item key={item.key} onClick={item.onClick}>\n              {item.label}\n            </Menu.Item>\n          ))}\n        </Menu>\n      ),\n    };\n  } else { // 需要 antd 4.20.0 以上版本\n    dropdownProps = { overlay: <Menu {...langMenu} /> };\n  }\n\n\n\n  return (\n    <div className=\"umi-plugin-layout-right anticon\">\n      {opts.runtimeConfig.logout ? (\n        <Dropdown {...dropdownProps} overlayClassName=\"umi-plugin-layout-container\">\n          {avatar}\n        </Dropdown>\n      ) : (\n        avatar\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "8RAyGI,EACA,EACA,iTCzGW,SAAS,EAAc,CAAU,EAC9C,IAAI,EAAQ,AAAkB,aAAlB,OAAO,OACf,EAAY,GAAA,UAAQ,EAAC,WACrB,MAAO,CAAA,GAAgB,OAAO,UAAU,CAAC,GAAY,OAAO,CAC9D,GACA,EAAa,GAAA,SAAc,EAAC,EAAW,GACvC,EAAU,CAAU,CAAC,EAAE,CACvB,EAAa,CAAU,CAAC,EAAE,CAc5B,MAbA,GAAA,iBAAe,EAAC,WACd,IAAI,GAGJ,IAAI,EAAiB,OAAO,UAAU,CAAC,GACnC,EAAW,SAAkB,CAAC,EAChC,OAAO,EAAW,EAAE,OAAO,EAC7B,EAEA,OADA,EAAe,WAAW,CAAC,GACpB,WACL,OAAO,EAAe,cAAc,CAAC,GACvC,GACF,EAAG,CAAC,EAAW,EACR,EACT,CCrBO,IAAI,EAAiB,CAC1B,GAAI,CACF,SAAU,IACV,WAAY,oBACd,EACA,GAAI,CACF,SAAU,IACV,SAAU,IACV,WAAY,2CACd,EACA,GAAI,CACF,SAAU,IACV,SAAU,IACV,WAAY,2CACd,EACA,GAAI,CACF,SAAU,IACV,SAAU,KACV,WAAY,4CACd,EACA,GAAI,CACF,SAAU,KACV,SAAU,KACV,WAAY,6CACd,EACA,IAAK,CACH,SAAU,KACV,WAAY,qBACd,CACF,EAOW,EAAqB,WAC9B,IAAI,EAAW,KAAA,QAEf,AAAI,AAAkB,aAAlB,OAAO,OACF,EAST,EAPoB,OAAO,IAAI,CAAC,GAAgB,IAAI,CAAC,SAAU,CAAG,EAChE,IAAI,EAAa,CAAc,CAAC,EAAI,CAAC,UAAU,SAC3C,OAAO,UAAU,CAAC,GAAY,OAAO,CAI3C,GAGF,EACI,EAAgB,WAClB,IAAI,EAAO,EAAc,EAAe,EAAE,CAAC,UAAU,EACjD,EAAO,EAAc,EAAe,EAAE,CAAC,UAAU,EACjD,EAAQ,EAAc,EAAe,GAAG,CAAC,UAAU,EACnD,EAAO,EAAc,EAAe,EAAE,CAAC,UAAU,EACjD,EAAO,EAAc,EAAe,EAAE,CAAC,UAAU,EACjD,EAAO,EAAc,EAAe,EAAE,CAAC,UAAU,EACjD,EAAY,GAAA,UAAQ,EAAC,KACvB,EAAa,GAAA,SAAc,EAAC,EAAW,GACvC,EAAU,CAAU,CAAC,EAAE,CACvB,EAAa,CAAU,CAAC,EAAE,CAgC5B,MA/BA,GAAA,WAAS,EAAC,WAKR,GAAI,EAAO,CACT,EAAW,OACX,OACF,CACA,GAAI,EAAM,CACR,EAAW,MACX,OACF,CACA,GAAI,EAAM,CACR,EAAW,MACX,OACF,CACA,GAAI,EAAM,CACR,EAAW,MACX,OACF,CACA,GAAI,EAAM,CACR,EAAW,MACX,OACF,CACA,GAAI,EAAM,CACR,EAAW,MACX,OACF,CACA,EAAW,MACb,EAAG,CAAC,EAAM,EAAM,EAAO,EAAM,EAAM,EAAK,EACjC,EACT,kCF9BA,SAAS,EAAY,CAAC,CAAE,CAAC,EACvB,OAAO,IAAM,EAAI,GAAK,GAAK,EAC7B,CAgCA,IAAI,EAAO,CAAC,WAAY,WAAY,WAAY,WAAY,UAAY,WAAY,WAAY,WAAY,WAAY,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAY,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAY,UAAY,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAY,UAAY,UAAY,UAAY,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAW,CAU3wB,SAAS,EAAS,CAAC,CAAE,CAAC,EACpB,IAAI,EAAM,AAAC,CAAA,AAAI,MAAJ,CAAS,EAAM,CAAA,AAAI,MAAJ,CAAS,EAEnC,MAAO,AADI,CAAA,GAAK,EAAC,EAAM,CAAA,GAAK,EAAC,EAAM,CAAA,GAAO,EAAC,GAC7B,GAAK,AAAM,MAAN,EACrB,CAqBA,SAAS,IAWP,IAhEqB,EAZP,EAQO,EAJL,EAAG,EAAG,EA8DlB,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAAI,AAAI,MAAM,IAGlB,EAAI,CAAK,CAAC,EAAE,CACZ,EAAI,CAAK,CAAC,EAAE,CACZ,EAAI,CAAK,CAAC,EAAE,CACZ,EAAI,CAAK,CAAC,EAAE,CACZ,EAAI,CAAK,CAAC,EAAE,CACZ,EAAI,CAAK,CAAC,EAAE,CACZ,EAAI,CAAK,CAAC,EAAE,CACZ,EAAI,CAAK,CAAC,EAAE,CAGZ,IAAK,IAAI,EAAI,EAAG,EAAI,GAAI,IACtB,CAAC,CAAC,EAAE,CAAG,CAAM,CAAC,AAAC,CAAA,GAAK,CAAA,EAAK,EAAE,CAAG,CAAM,CAAC,AAAC,CAAA,GAAK,CAAA,EAAK,EAAE,EAAI,EAAI,CAAM,CAAC,AAAC,CAAA,GAAK,CAAA,EAAK,EAAE,EAAI,GAAK,CAAM,CAAC,GAAK,EAAE,EAAI,GAG3G,IAAK,IAAI,EAAI,EAAG,EAAI,GAAI,IACtB,EAAK,EAjFA,CAAA,EAAY,EADE,EAkFI,GAjFE,EAAY,GAAI,GAAK,EAAY,GAAI,EAAC,EAZ1D,CAAA,CADO,EA8FuB,GAAG,EA7FzB,CAAC,EA6F2B,CA7FvB,EA6F4B,CAAI,CAAC,EAAE,CACjD,EAAI,GAAI,GAAM,CAAC,CAAC,EAAE,CAAM,GAAM,AAvEtC,SAAuB,CAAC,CAAE,CAAC,MAJJ,EAJA,EASrB,OAAO,CAAC,CAAC,AAAI,GAAJ,EAAS,EAAI,AAJf,CAAA,EAAY,GADE,EAKe,CAAC,CAAC,EAAI,GAAK,GAAK,EAJxB,EAAY,GAAI,GAAK,IAAM,EAAC,EAIA,CAAC,CAAC,EAAI,EAAI,GAAK,CARhE,CAAA,EAAY,EADE,EASmE,CAAC,CAAC,EAAI,EAAI,GAAK,EAR5E,EAAY,GAAI,GAAK,IAAM,CAAA,EASxD,EAqEoD,EAAG,GACnD,EAAK,AAvFA,CAAA,EAAY,EADE,EAwFA,GAvFM,EAAY,GAAI,GAAK,EAAY,GAAI,EAAC,EAJ1D,CAAA,CADS,EA4FmB,IA5FhB,EA4FmB,GA3FvB,GADO,EA4FmB,GA3FlB,EAAI,CAAA,EA4FzB,EAAI,EACJ,EAAI,EACJ,EAAI,EACJ,EAAI,EAAS,EAAG,GAChB,EAAI,EACJ,EAAI,EACJ,EAAI,EACJ,EAAI,EAAS,EAAI,GAKnB,CAAK,CAAC,EAAE,EAAI,EACZ,CAAK,CAAC,EAAE,EAAI,EACZ,CAAK,CAAC,EAAE,EAAI,EACZ,CAAK,CAAC,EAAE,EAAI,EACZ,CAAK,CAAC,EAAE,EAAI,EACZ,CAAK,CAAC,EAAE,EAAI,EACZ,CAAK,CAAC,EAAE,EAAI,EACZ,CAAK,CAAC,EAAE,EAAI,EACd,CG7LA,SAAS,EAAQ,CAAG,EAA+B,MAAO,CAAA,EAAU,YAAc,OAAO,QAAU,UAAY,OAAO,OAAO,QAAQ,CAAG,SAAU,CAAG,EAAI,OAAO,OAAO,EAAK,EAAI,SAAU,CAAG,EAAI,OAAO,GAAO,YAAc,OAAO,QAAU,EAAI,WAAW,GAAK,QAAU,IAAQ,OAAO,SAAS,CAAG,SAAW,OAAO,EAAK,CAAA,EAAW,GAAM,CAE/U,IAAI,EAAY,CAAC,wBAAyB,WAAY,OAAQ,WAAY,aAAc,SAAS,CA0BjG,SAAS,EAAiB,CAAK,EAAI,IAAI,EAAS,AAAe,YAAf,OAAO,IAAqB,IAAI,IAAQ,KAAA,EAA8nB,MAAO,AAA1nB,CAAA,EAAmB,SAA0B,CAAK,EAAI,GAAI,AAAU,OAAV,GAM7D,KAAxD,SAAS,QAAQ,CAAC,IAAI,CANoI,GAM/H,OAAO,CAAC,iBAN+H,OAAO,EAAO,GAAI,AAAiB,YAAjB,OAAO,EAAwB,MAAM,AAAI,UAAU,sDAAyD,GAAI,AAAkB,KAAA,IAAX,EAAwB,CAAE,GAAI,EAAO,GAAG,CAAC,GAAQ,OAAO,EAAO,GAAG,CAAC,GAAQ,EAAO,GAAG,CAAC,EAAO,GAAU,CAAE,SAAS,IAAY,OAAO,EAAW,EAAO,UAAW,EAAgB,IAAI,EAAE,WAAW,EAAG,CAAkJ,OAAhJ,EAAQ,SAAS,CAAG,OAAO,MAAM,CAAC,EAAM,SAAS,CAAE,CAAE,YAAa,CAAE,MAAO,EAAS,WAAY,CAAA,EAAO,SAAU,CAAA,EAAM,aAAc,CAAA,CAAK,CAAE,GAAW,EAAgB,EAAS,GAAQ,CAAA,EAA2B,GAAQ,CAEtvB,SAAS,EAAW,CAAM,CAAE,CAAI,CAAE,CAAK,EAAuV,MAAO,CAAvT,EAA/B,IAA4C,QAAQ,SAAS,CAAC,IAAI,GAA0B,SAAoB,CAAM,CAAE,CAAI,CAAE,CAAK,EAAI,IAAI,EAAI,CAAC,KAAK,CAAE,EAAE,IAAI,CAAC,KAAK,CAAC,EAAG,GAAyD,IAAI,EAAW,GAA/C,CAAA,SAAS,IAAI,CAAC,KAAK,CAAC,EAAQ,EAAC,EAA4F,OAAnD,GAAO,EAAgB,EAAU,EAAM,SAAS,EAAU,EAAU,GAAuB,KAAK,CAAC,KAAM,WAAY,CAExa,SAAS,IAA8B,GAAuB,aAAnB,OAAO,SAA2B,CAAC,QAAQ,SAAS,EAAoB,QAAQ,SAAS,CAAC,IAAI,CAAxC,MAAO,CAAA,EAAiD,GAAI,AAAiB,YAAjB,OAAO,MAAsB,MAAO,CAAA,EAAM,GAAI,CAAkF,OAAhF,QAAQ,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC,QAAS,EAAE,CAAE,WAAa,IAAY,CAAA,EAAM,CAAE,MAAO,EAAG,CAAE,MAAO,CAAA,EAAO,CAAE,CAIxU,SAAS,EAAgB,CAAC,CAAE,CAAC,EAA4I,MAAO,AAA/I,CAAA,EAAkB,OAAO,cAAc,CAAG,OAAO,cAAc,CAAC,IAAI,GAAK,SAAyB,CAAC,CAAE,CAAC,EAAqB,OAAjB,EAAE,SAAS,CAAG,EAAU,EAAG,CAAA,EAA0B,EAAG,GAAI,CAEvM,SAAS,EAAgB,CAAC,EAA8J,MAAO,AAAjK,CAAA,EAAkB,OAAO,cAAc,CAAG,OAAO,cAAc,CAAC,IAAI,GAAK,SAAyB,CAAC,EAAI,OAAO,EAAE,SAAS,EAAI,OAAO,cAAc,CAAC,GAAI,CAAA,EAA0B,GAAI,CAEnN,SAAS,EAAmB,CAAG,EAAI,OAAO,AAQ1C,SAA4B,CAAG,EAAI,GAAI,MAAM,OAAO,CAAC,GAAM,OAAO,EAAkB,GAAM,EAR7B,IAAQ,AAMrE,SAA0B,CAAI,EAAI,GAAI,AAAkB,aAAlB,OAAO,QAA0B,AAAyB,MAAzB,CAAI,CAAC,OAAO,QAAQ,CAAC,EAAY,AAAsB,MAAtB,CAAI,CAAC,aAAa,CAAU,OAAO,MAAM,IAAI,CAAC,GAAO,EANvE,IAAQ,EAA4B,IAAQ,AAElI,WAAgC,MAAM,AAAI,UAAU,wIAAyI,IAFrC,CAIxJ,SAAS,EAA4B,CAAC,CAAE,CAAM,EAAI,GAAK,GAAW,GAAI,AAAa,UAAb,OAAO,EAAgB,OAAO,EAAkB,EAAG,GAAS,IAAI,EAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,EAAG,IAAkE,GAAnD,WAAN,GAAkB,EAAE,WAAW,EAAE,CAAA,EAAI,EAAE,WAAW,CAAC,IAAI,AAAD,EAAO,AAAM,QAAN,GAAe,AAAM,QAAN,EAAa,OAAO,MAAM,IAAI,CAAC,GAAI,GAAI,AAAM,cAAN,GAAqB,2CAA2C,IAAI,CAAC,GAAI,OAAO,EAAkB,EAAG,IAAS,CAM/Z,SAAS,EAAkB,CAAG,CAAE,CAAG,EAAQ,CAAA,AAAO,MAAP,GAAe,EAAM,EAAI,MAAM,AAAD,GAAG,CAAA,EAAM,EAAI,MAAM,AAAD,EAAG,IAAK,IAAI,EAAI,EAAG,EAAO,AAAI,MAAM,GAAM,EAAI,EAAK,IAAO,CAAI,CAAC,EAAE,CAAG,CAAG,CAAC,EAAE,CAAI,OAAO,EAAM,CAMtL,SAAS,EAAQ,CAAM,CAAE,CAAc,EAAI,IAAI,EAAO,OAAO,IAAI,CAAC,GAAS,GAAI,OAAO,qBAAqB,CAAE,CAAE,IAAI,EAAU,OAAO,qBAAqB,CAAC,GAAS,GAAmB,CAAA,EAAU,EAAQ,MAAM,CAAC,SAAU,CAAG,EAAI,OAAO,OAAO,wBAAwB,CAAC,EAAQ,GAAK,UAAU,CAAE,EAAC,EAAI,EAAK,IAAI,CAAC,KAAK,CAAC,EAAM,GAAU,CAAE,OAAO,EAAM,CAEpV,SAAS,EAAc,CAAM,EAAI,IAAK,IAAI,EAAI,EAAG,EAAI,UAAU,MAAM,CAAE,IAAK,CAAE,IAAI,EAAS,MAAQ,SAAS,CAAC,EAAE,CAAG,SAAS,CAAC,EAAE,CAAG,CAAC,EAAG,EAAI,EAAI,EAAQ,OAAO,GAAS,CAAC,GAAG,OAAO,CAAC,SAAU,CAAG,MAE3J,EAAA,EAF4L,CAAM,CAAC,EAAI,CAE1L,AAF0K,KAAR,EAEpJ,OAAO,cAAc,CAF+H,EAAQ,EAE5H,CAAE,MAAO,EAAO,WAAY,CAAA,EAAM,aAAc,CAAA,EAAM,SAAU,CAAA,CAAK,GAAa,AAFkC,CAE/B,CAFuC,EAElC,CAAG,EAFkD,GAAK,OAAO,yBAAyB,CAAG,OAAO,gBAAgB,CAAC,EAAQ,OAAO,yBAAyB,CAAC,IAAW,EAAQ,OAAO,IAAS,OAAO,CAAC,SAAU,CAAG,EAAI,OAAO,cAAc,CAAC,EAAQ,EAAK,OAAO,wBAAwB,CAAC,EAAQ,IAAO,GAAI,CAAE,OAAO,EAAQ,CAOlf,IAAI,EAAoB,SACxB,SAAS,EAAgC,CAAG,EACjD,OAAO,EAAI,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CACxC,CACO,IAAI,EAAQ,SAAe,CAAI,EACpC,GAAI,CAAC,EAAK,UAAU,CAAC,QACnB,MAAO,CAAA,EAGT,GAAI,CAEF,OAAO,AADG,IAAI,IAAI,GACX,CAAA,EACT,CAAE,MAAO,EAAO,CACd,MAAO,CAAA,EACT,CACF,EACW,EAAe,SAAsB,CAAI,EAClD,IH+Mc,EG/MV,EAAO,EAAK,IAAI,CAEpB,GAAI,CAAC,GAAQ,AAAS,MAAT,EAEX,GAAI,CACF,MAAO,IAAI,MAAM,EH0MP,EG1Me,KAAK,SAAS,CAAC,GHkC5C,EAAQ,AAAI,MAAM,GAClB,EAAQ,IAAY,CACpB,EAAS,AAAI,MAAM,IACnB,CAAK,CAAC,EAAE,CAAG,CAAK,CAAC,EAAE,CAAG,EACtB,CAAK,CAAC,EAAE,CAAG,WACX,CAAK,CAAC,EAAE,CAAG,WACX,CAAK,CAAC,EAAE,CAAG,WACX,CAAK,CAAC,EAAE,CAAG,WACX,CAAK,CAAC,EAAE,CAAG,WACX,CAAK,CAAC,EAAE,CAAG,WACX,CAAK,CAAC,EAAE,CAAG,UACX,CAAK,CAAC,EAAE,CAAG,YA+JX,AAlGF,SAAuB,CAAI,CAAE,CAAQ,EAGnC,IAFI,EACA,EACA,EAAS,EAGb,EAAQ,CAAK,CAAC,EAAE,EAAI,EAAI,GACxB,IAAI,EAAY,AAAW,GAAX,EAOhB,IAJK,CAAA,CAAK,CAAC,EAAE,EAAI,GAAY,CAAA,EAAK,GAAY,GAAG,CAAK,CAAC,EAAE,GACzD,CAAK,CAAC,EAAE,EAAI,GAAY,GAGnB,EAAI,EAAG,EAAI,GAAK,EAAU,GAAK,GAAI,CACtC,IAAK,IAAI,EAAI,EAAO,EAAI,GAAI,IAC1B,CAAM,CAAC,EAAE,CAAG,EAAK,UAAU,CAAC,KAG9B,IACA,EAAQ,EACV,CAIA,IAAK,IAAI,EAAK,EAAG,EAAK,EAAW,IAC/B,CAAM,CAAC,EAAG,CAAG,EAAK,UAAU,CAAC,KAEjC,EAsEgB,EAAM,EAAK,MAAM,GAC/B,AAnEF,WACE,IAAI,EAAQ,CAAK,CAAC,EAAE,EAAI,EAAI,GAG5B,GAFA,CAAM,CAAC,IAAQ,CAAG,IAEd,GAAS,GACX,IAAK,IAAI,EAAI,EAAO,EAAI,GAAI,IAC1B,CAAM,CAAC,EAAE,CAAG,MAET,CACL,IAAK,IAAI,EAAK,EAAO,EAAK,GAAI,IAC5B,CAAM,CAAC,EAAG,CAAG,EAGf,IAEA,IAAK,IAAI,EAAM,EAAG,EAAM,GAAI,IAC1B,CAAM,CAAC,EAAI,CAAG,EAElB,CAEA,CAAM,CAAC,GAAG,CAAG,CAAK,CAAC,EAAE,GAAK,GAAK,IAC/B,CAAM,CAAC,GAAG,CAAG,CAAK,CAAC,EAAE,GAAK,GAAK,IAC/B,CAAM,CAAC,GAAG,CAAG,CAAK,CAAC,EAAE,GAAK,EAAI,IAC9B,CAAM,CAAC,GAAG,CAAG,AAAW,IAAX,CAAK,CAAC,EAAE,CACrB,CAAM,CAAC,GAAG,CAAG,CAAK,CAAC,EAAE,GAAK,GAAK,IAC/B,CAAM,CAAC,GAAG,CAAG,CAAK,CAAC,EAAE,GAAK,GAAK,IAC/B,CAAM,CAAC,GAAG,CAAG,CAAK,CAAC,EAAE,GAAK,EAAI,IAC9B,CAAM,CAAC,GAAG,CAAG,AAAW,IAAX,CAAK,CAAC,EAAE,CACrB,IACF,IAuCS,AAnBT,WAGE,IAAK,IAFD,EAAS,IAAI,OAER,EAAI,EAAG,EAAI,EAAG,IACrB,IAAK,IAAI,EAAI,GAAI,GAAK,EAAG,GAAK,EAC5B,GAAU,AA3KQ,mBA2KU,MAAM,CAAC,CAAK,CAAC,EAAE,GAAK,EAAI,IAIxD,OAAO,EACT,MGpMI,CAAE,MAAO,EAAO,CAChB,CAGF,OAAO,EAAO,EAAgC,GAAQ,EACxD,EAOI,GAAoB,SAA2B,CAAI,CAAE,CAAU,EACjE,IAAI,EAAO,EAAK,IAAI,CAChB,EAAS,EAAK,MAAM,OAExB,AAAI,CAAA,CAAA,CAAA,WAAY,CAAG,GAAK,AAAW,CAAA,IAAX,CAAe,IAAK,CAAC,GAItC,CAAA,EAAK,MAAM,EAAI,GAAG,MAAM,CAAC,EAAY,KAAK,MAAM,CAAC,EAAI,EAC9D,EAUI,GAAY,WACd,IAAI,EAAO,UAAU,MAAM,CAAG,GAAK,AAAiB,KAAA,IAAjB,SAAS,CAAC,EAAE,CAAiB,SAAS,CAAC,EAAE,CAAG,GAC3E,EAAa,UAAU,MAAM,CAAG,GAAK,AAAiB,KAAA,IAAjB,SAAS,CAAC,EAAE,CAAiB,SAAS,CAAC,EAAE,CAAG,WAErF,AAAI,EAAK,QAAQ,CAAC,MACT,EAAK,OAAO,CAAC,KAAM,KAGxB,AAAC,CAAA,GAAQ,CAAS,EAAG,UAAU,CAAC,MAIhC,EAAM,GAHD,EAOF,IAAI,MAAM,CAAC,EAAY,KAAK,MAAM,CAAC,GAAM,OAAO,CAAC,QAAS,KAAK,OAAO,CAAC,QAAS,KACzF,EAGI,GAA+B,SAAsC,CAAK,CAAE,CAAK,EACnF,IAAI,EAAc,EAAM,IAAI,CACxB,EAAO,AAAgB,KAAK,IAArB,EAAyB,CAAC,EAAI,EACrC,EAAa,EAAM,UAAU,CAC7B,EAAc,EAAM,IAAI,CAExB,EAAiB,EAAM,QAAQ,EAAI,EAAE,CACrC,EAAa,EAAK,IAAI,CACtB,EAAO,AAAe,KAAK,IAApB,EAAwB,EAAM,IAAI,CAAG,EAC5C,EAAa,EAAK,IAAI,CACtB,EAAO,AAAe,KAAK,IAApB,EAAwB,EAAM,IAAI,CAAG,EAC5C,EAAqB,EAAK,YAAY,CACtC,EAAe,AAAuB,KAAK,IAA5B,EAAgC,EAAM,YAAY,CAAG,EACpE,EAAiB,EAAK,QAAQ,CAC9B,EAAW,AAAmB,KAAK,IAAxB,EAA4B,EAAM,QAAQ,CAAG,EAGxD,EAAe,GACnB,AAAsC,aAAtC,OAAO,IAAI,CAAC,GAAY,IAAI,CAAC,KAAsB,CAAC,EAAc,CAChE,KAdS,AAAgB,KAAK,IAArB,EAAyB,GAAK,EAevC,KAAM,CACR,EAAG,GAAY,CAAC,MAAM,CAAC,GAAkB,EAAE,EAAI,EAE3C,EAAS,EAAc,CAAC,EAAG,GAU/B,GARI,GACF,CAAA,EAAO,IAAI,CAAG,CAAG,EAGf,GACF,CAAA,EAAO,IAAI,CAAG,CAAG,EAGf,GAAgB,EAAa,MAAM,CAAE,CAEvC,GAAI,EAEF,OADA,OAAO,EAAO,QAAQ,CACf,EAIT,IAAI,EAAgB,GAAU,EAAc,EAAc,CAAC,EAAG,GAAQ,CAAC,EAAG,CACxE,KAAM,CACR,GAAI,GAGJ,GAAI,EACF,OAAO,EAGT,OAAO,CAAM,CAAC,EAAkB,CAClC,CAEA,OAAO,EACT,EAEI,GAAe,SAAsB,CAAK,EAC5C,OAAO,MAAM,OAAO,CAAC,IAAU,EAAM,MAAM,CAAG,EAChD,EAQA,SAAS,GAAU,CAAK,EACtB,IAAI,EAAS,UAAU,MAAM,CAAG,GAAK,AAAiB,KAAA,IAAjB,SAAS,CAAC,EAAE,CAAiB,SAAS,CAAC,EAAE,CAAG,CAC/E,KAAM,GACR,EACI,EAAO,EAAM,IAAI,CACjB,EAAgB,EAAM,aAAa,CACnC,EAAa,EAAM,UAAU,CAC7B,EAAa,EAAM,MAAM,QAE7B,AAAI,AAAC,GAAS,MAAM,OAAO,CAAC,GAIrB,EAAK,MAAM,CAAC,SAAU,CAAI,QAC/B,EAAK,MACD,GAAa,EAAK,QAAQ,KAC1B,EAAK,IAAI,IACT,EAAK,UAAU,IACf,EAAK,MAAM,GAEX,EAAK,QAAQ,GACb,EAAK,YAAY,CACd,CAAA,IACT,GAAG,MAAM,CAAC,SAAU,CAAI,EACtB,IAAI,EAAY,SAEhB,MAAK,GAA4C,AAA6B,OAA5B,CAAA,EAAa,EAAK,IAAI,AAAD,GAAe,AAAe,KAAK,IAApB,KAAiC,EAAW,IAAI,EAAM,MAAA,KAA4C,EAAK,QAAQ,EAAM,MAAA,GAA4C,AAA8B,OAA7B,CAAA,EAAc,EAAK,IAAI,AAAD,GAAe,AAAgB,KAAK,IAArB,KAAkC,EAAY,QAAQ,EAM/U,AAAc,CAAA,IAAd,EAAK,IAAI,CAKf,GAAG,GAAG,CAAC,SAAU,CAAW,EAC1B,IAAI,EAAO,EAAc,EAAc,CAAC,EAAG,GAAc,CAAC,EAAG,CAC3D,KAAM,EAAY,IAAI,EAAI,EAAY,UAAU,AAClD,GA0BA,MAxBI,CAAC,EAAK,QAAQ,EAAI,CAAI,CAAC,EAAkB,GAC3C,EAAK,QAAQ,CAAG,CAAI,CAAC,EAAkB,CACvC,OAAO,CAAI,CAAC,EAAkB,EAK5B,EAAK,YAAY,EAEnB,OAAO,EAAK,IAAI,CAGA,MAAd,EAAK,IAAI,EACX,CAAA,EAAK,IAAI,CAAG,GAAE,EAGE,OAAd,EAAK,IAAI,EACX,CAAA,EAAK,IAAI,CAAG,GAAE,EAGZ,CAAC,EAAK,IAAI,EAAI,EAAK,UAAU,EAC/B,CAAA,EAAK,IAAI,CAAG,EAAK,UAAU,AAAD,EAGrB,EACT,GAAG,GAAG,CAAC,WACL,IAAI,EAAO,UAAU,MAAM,CAAG,GAAK,AAAiB,KAAA,IAAjB,SAAS,CAAC,EAAE,CAAiB,SAAS,CAAC,EAAE,CAAG,CAC7E,KAAM,GACR,EACI,EAAiB,EAAK,QAAQ,EAAI,CAAI,CAAC,EAAkB,EAAI,EAAE,CAC/D,EAAO,GAAU,EAAK,IAAI,CAAE,EAAS,EAAO,IAAI,CAAG,KACnD,EAAO,EAAK,IAAI,CAChB,EAAS,GAAkB,EAAM,GAAc,QAG/C,EAAa,AAAW,CAAA,IAAX,GAAoB,AAAe,CAAA,IAAf,GAAwB,GAAiB,EAAS,EAAc,CACnG,GAAI,EACJ,eAAgB,CAClB,GAAK,EAED,EAAwB,EAAO,qBAAqB,CAOpD,GALW,EAAO,QAAQ,CACnB,EAAO,IAAI,CACP,EAAO,QAAQ,CACb,EAAO,UAAU,CACrB,EAAO,MAAM,CACT,AAlPrB,SAAkC,CAAM,CAAE,CAAQ,EAAI,GAAI,AAAU,MAAV,EAAgB,MAAO,CAAC,EAAG,IAAkE,EAAK,EAAnE,EAAS,AAElG,SAAuC,CAAM,CAAE,CAAQ,EAAI,GAAI,AAAU,MAAV,EAAgB,MAAO,CAAC,EAAG,IAA2D,EAAK,EAA5D,EAAS,CAAC,EAAO,EAAa,OAAO,IAAI,CAAC,GAAqB,IAAK,EAAI,EAAG,EAAI,EAAW,MAAM,CAAE,IAAO,EAAM,CAAU,CAAC,EAAE,CAAM,EAAS,OAAO,CAAC,IAAQ,GAAa,CAAA,CAAM,CAAC,EAAI,CAAG,CAAM,CAAC,EAAI,AAAD,EAAK,OAAO,EAAQ,EAFlL,EAAQ,GAAuB,GAAI,OAAO,qBAAqB,CAAE,CAAE,IAAI,EAAmB,OAAO,qBAAqB,CAAC,GAAS,IAAK,EAAI,EAAG,EAAI,EAAiB,MAAM,CAAE,IAAO,EAAM,CAAgB,CAAC,EAAE,EAAM,CAAA,EAAS,OAAO,CAAC,IAAQ,CAAA,GAAkB,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAQ,IAAgB,CAAA,CAAM,CAAC,EAAI,CAAG,CAAM,CAAC,EAAI,AAAD,EAAK,CAAE,OAAO,EAAQ,EAkP7b,EAAQ,IAE9C,EAA6B,IAAI,IAAI,EAAE,CAAC,MAAM,CAAC,EARvB,AAA0B,KAAK,IAA/B,EAAmC,EAAE,CAAG,GAQ0B,EAAmB,EAAK,UAAU,EAAI,EAAE,IAElI,EAAO,GAAG,EACZ,EAA2B,GAAG,CAAC,EAAO,GAAG,EAG3C,IAAI,EAAc,EAAc,EAAc,EAAc,CAAC,EAAG,GAAa,CAAC,EAAG,CAC/E,KAAM,KAAA,CACR,EAAG,GAAO,CAAC,EAAG,CACZ,KAAM,EACN,OAAQ,EACR,IAAK,EAAK,GAAG,EAAI,EAAa,EAAc,EAAc,CAAC,EAAG,GAAO,CAAC,EAAG,CACvE,KAAM,CACR,IACA,sBAAuB,MAAM,IAAI,CAAC,GAA4B,MAAM,CAAC,SAAU,CAAG,EAChF,OAAO,GAAO,AAAQ,MAAR,EAChB,EACF,GAYA,GAVI,EACF,EAAY,IAAI,CAAG,EAEnB,OAAO,EAAY,IAAI,CAGA,KAAA,IAArB,EAAY,IAAI,EAClB,OAAO,EAAY,IAAI,CAGrB,GAAa,GAAiB,CAChC,IAAI,EAAoB,GAAU,EAAc,EAAc,CAAC,EAAG,GAAQ,CAAC,EAAG,CAC5E,KAAM,EACN,WAAY,GAAU,EACxB,GAAI,GAEA,GAAa,IACf,CAAA,EAAY,QAAQ,CAAG,CAAgB,EAE3C,CAEA,OAAO,GAA6B,EAAa,GACnD,GAAG,IAAI,CAAC,GA1HC,EAAE,CA2Hb,CAMA,IAAI,GAAwB,SAAS,IACnC,IAAI,EAAW,UAAU,MAAM,CAAG,GAAK,AAAiB,KAAA,IAAjB,SAAS,CAAC,EAAE,CAAiB,SAAS,CAAC,EAAE,CAAG,EAAE,CACrF,OAAO,EAAS,MAAM,CAAC,SAAU,CAAI,EACnC,OAAO,GAAS,CAAA,EAAK,IAAI,EAAI,GAAa,EAAK,QAAQ,CAAA,GAAM,CAAC,EAAK,UAAU,EAAI,CAAC,EAAK,QAAQ,CACjG,GAAG,GAAG,CAAC,SAAU,CAAI,EACnB,IAAI,EAAU,EAAc,CAAC,EAAG,GAE5B,EAAiB,EAAQ,QAAQ,EAAI,CAAI,CAAC,EAAkB,EAAI,EAAE,CAGtE,GAFA,OAAO,CAAO,CAAC,EAAkB,CAE7B,GAAa,IAAmB,CAAC,EAAQ,kBAAkB,EAAI,EAAe,IAAI,CAAC,SAAU,CAAK,EACpG,OAAO,GAAS,CAAC,CAAC,EAAM,IAAI,CAC9B,GAAI,CACF,IAAI,EAAc,EAAsB,GACxC,GAAI,EAAY,MAAM,CAAE,OAAO,EAAc,EAAc,CAAC,EAAG,GAAU,CAAC,EAAG,CAC3E,SAAU,CACZ,GACF,CAEA,OAAO,EAAc,CAAC,EAAG,GAC3B,GAAG,MAAM,CAAC,SAAU,CAAI,EACtB,OAAO,EACT,GACF,EAMI,GAA4B,SAAU,CAAI,GAC5C,AAlWF,SAAmB,CAAQ,CAAE,CAAU,EAAI,GAAI,AAAsB,YAAtB,OAAO,GAA6B,AAAe,OAAf,EAAuB,MAAM,AAAI,UAAU,sDAAyD,EAAS,SAAS,CAAG,OAAO,MAAM,CAAC,GAAc,EAAW,SAAS,CAAE,CAAE,YAAa,CAAE,MAAO,EAAU,SAAU,CAAA,EAAM,aAAc,CAAA,CAAK,CAAE,GAAI,OAAO,cAAc,CAAC,EAAU,YAAa,CAAE,SAAU,CAAA,CAAM,GAAQ,GAAY,EAAgB,EAAU,GAAa,EAkWvb,EAAc,GAExB,IAlWmC,EAJF,EAsW7B,GAlW+B,EAA4B,IAAoC,WAAkC,IAAsC,EAAlC,EAAQ,EAkWvH,GAlWwV,OAA3G,EAAhF,EAAyF,QAAQ,SAAS,CAAC,EAAO,UAArE,EAAgB,IAAI,EAAE,WAAW,EAA6E,EAAM,KAAK,CAAC,IAAI,CAAE,WAAqB,AAE3X,SAAoC,CAAI,CAAE,CAAI,EAAI,GAAI,GAAS,CAAA,AAAkB,WAAlB,EAAQ,IAAsB,AAAgB,YAAhB,OAAO,CAAkB,EAAM,OAAO,EAAa,GAAI,AAAS,KAAK,IAAd,EAAmB,MAAM,AAAI,UAAU,4DAA+D,OAAO,AAEjQ,SAAgC,CAAI,EAAI,GAAI,AAAS,KAAK,IAAd,EAAmB,MAAM,AAAI,eAAe,6DAAgE,OAAO,EAAM,EAFmH,GAAO,EAFuH,IAAI,CAAE,GAAS,GAoWna,SAAS,IAGP,OAFA,AA7WJ,SAAyB,CAAQ,CAAE,CAAW,EAAI,GAAI,CAAE,CAAA,aAAoB,CAAU,EAAM,MAAM,AAAI,UAAU,qCAAwC,EA6WpI,IAAI,CAAE,GAEf,EAAO,KAAK,CAAC,IAAI,CAAE,WAC5B,CAsCA,OAlZiC,EA8WN,CAAC,CAC1B,IAAK,MACL,MAAO,SAAa,CAAQ,EAC1B,IAAI,EAEJ,GAAI,CAEF,IACI,EADA,EAAY,AA3XxB,SAAoC,CAAC,CAAE,CAAc,EAAI,IAAI,EAAK,AAAkB,aAAlB,OAAO,QAA0B,CAAC,CAAC,OAAO,QAAQ,CAAC,EAAI,CAAC,CAAC,aAAa,CAAE,GAAI,CAAC,EAAI,CAAE,GAAI,MAAM,OAAO,CAAC,IAAO,CAAA,EAAK,EAA4B,EAAC,EAA2D,CAAM,GAAI,CAAA,EAAI,CAAC,EAAG,IAAI,EAAI,EAAO,EAAI,WAAc,EAAG,MAAO,CAAE,EAAG,EAAG,EAAG,kBAAe,AAAI,GAAK,EAAE,MAAM,CAAS,CAAE,KAAM,CAAA,CAAK,EAAU,CAAE,KAAM,CAAA,EAAO,MAAO,CAAC,CAAC,IAAI,AAAC,EAAG,EAAG,EAAG,SAAW,CAAG,EAAI,MAAM,EAAK,EAAG,EAAG,CAAE,EAAG,CAAE,MAAM,AAAI,UAAU,yIAA0I,CAAE,IAA6C,EAAzC,EAAmB,CAAA,EAAM,EAAS,CAAA,EAAY,MAAO,CAAE,EAAG,WAAe,EAAK,EAAG,IAAI,CAAC,GAAI,EAAG,EAAG,WAAe,IAAI,EAAO,EAAG,IAAI,GAAkC,OAA9B,EAAmB,EAAK,IAAI,CAAS,EAAM,EAAG,EAAG,SAAW,CAAG,EAAI,EAAS,CAAA,EAAM,EAAM,EAAK,EAAG,EAAG,WAAe,GAAI,CAAO,GAAoB,AAAa,MAAb,EAAG,MAAM,EAAU,EAAG,MAAM,GAAI,QAAU,CAAE,GAAI,EAAQ,MAAM,EAAK,CAAE,CAAE,EAAG,EA2Xp7B,IAAI,CAAC,OAAO,IAGvD,GAAI,CACF,IAAK,EAAU,CAAC,GAAI,CAAC,AAAC,CAAA,EAAQ,EAAU,CAAC,EAAC,EAAG,IAAI,EAAG,CAClD,IAxYY,EAwYR,GAxYQ,EAwYqB,EAAM,KAAK,CAxYf,AAMzC,SAAyB,CAAG,EAAI,GAAI,MAAM,OAAO,CAAC,GAAM,OAAO,EAAK,EANX,IAAQ,AAIjE,SAA+B,CAAG,CAAE,CAAC,EAAI,IAAoL,EAAI,EAApL,EAAK,AAAO,MAAP,EAAc,KAAO,AAAkB,aAAlB,OAAO,QAA0B,CAAG,CAAC,OAAO,QAAQ,CAAC,EAAI,CAAG,CAAC,aAAa,CAAE,GAAI,AAAM,MAAN,GAAoB,IAAI,EAAO,EAAE,CAAM,EAAK,CAAA,EAAU,EAAK,CAAA,EAAmB,GAAI,CAAE,IAAK,EAAK,EAAG,IAAI,CAAC,GAAM,CAAE,CAAA,EAAK,AAAC,CAAA,EAAK,EAAG,IAAI,EAAC,EAAG,IAAI,AAAD,IAAiB,EAAK,IAAI,CAAC,EAAG,KAAK,EAAY,AAoYnR,IApYmR,EAAK,MAAM,EAAtD,EAAK,CAAA,GAAkE,CAAE,MAAO,EAAK,CAAE,EAAK,CAAA,EAAM,EAAK,EAAK,QAAU,CAAE,GAAI,CAAO,GAAM,AAAgB,MAAhB,EAAG,MAAS,EAAU,EAAG,MAAS,GAAI,QAAU,CAAE,GAAI,EAAI,MAAM,EAAI,CAAE,CAAE,OAAO,GAAM,EAJza,EAwY7B,IAxYwC,EAA4B,EAwYpE,IAxY+E,AAEzI,WAA8B,MAAM,AAAI,UAAU,6IAA8I,KAuYhL,EAAM,CAAW,CAAC,EAAE,CACpB,EAAQ,CAAW,CAAC,EAAE,CAEtB,EAAO,EAAgC,GAE3C,GAAI,CAAC,EAAM,IAAQ,GAAA,cAAY,EAAC,EAAM,EAAE,EAAE,IAAI,CAAC,GAAW,CACxD,EAAa,EACb,MACF,CACF,CACF,CAAE,MAAO,EAAK,CACZ,EAAU,CAAC,CAAC,GACd,QAAU,CACR,EAAU,CAAC,GACb,CACF,CAAE,MAAO,EAAO,CACd,EAAa,KAAA,EACf,CAEA,OAAO,EACT,CACF,EAAE,CAhZ0E,AAF9E,SAA2B,CAAM,CAAE,CAAK,EAAI,IAAK,IAAI,EAAI,EAAG,EAAI,EAAM,MAAM,CAAE,IAAK,CAAE,IAAI,EAAa,CAAK,CAAC,EAAE,CAAE,EAAW,UAAU,CAAG,EAAW,UAAU,EAAI,CAAA,EAAO,EAAW,YAAY,CAAG,CAAA,EAAU,UAAW,GAAY,CAAA,EAAW,QAAQ,CAAG,CAAA,CAAG,EAAG,OAAO,cAAc,CAAC,EAAQ,EAAW,GAAG,CAAE,GAAa,CAAE,EAE5N,AA8WjF,EA9W6F,SAAS,CAAE,GAA2E,OAAO,cAAc,CA8WxM,EA9WsN,YAAa,CAAE,SAAU,CAAA,CAAM,GAkZ3P,EACT,EAAgB,EAAiB,MAO7B,GAAuB,SAA8B,CAAQ,EAE/D,IAAI,EAAY,IAAI,GAiBpB,OADA,AAdsB,SAAS,EAAgB,CAAI,CAAE,CAAM,EACzD,EAAK,OAAO,CAAC,SAAU,CAAQ,EAC7B,IAAI,EAAiB,EAAS,QAAQ,EAAI,CAAQ,CAAC,EAAkB,EAAI,EAAE,CAEvE,GAAa,IACf,EAAgB,EAAgB,GAIlC,IAAI,EAAO,GAAU,EAAS,IAAI,CAAE,EAAS,EAAO,IAAI,CAAG,KAC3D,EAAU,GAAG,CAAC,EAAgC,GAAO,GACvD,GACF,EAEgB,GACT,EACT,EAEI,GAAgB,SAAS,IAC3B,IAAI,EAAW,UAAU,MAAM,CAAG,GAAK,AAAiB,KAAA,IAAjB,SAAS,CAAC,EAAE,CAAiB,SAAS,CAAC,EAAE,CAAG,EAAE,CACrF,OAAO,EAAS,GAAG,CAAC,SAAU,CAAI,EAChC,IAAI,EAAiB,EAAK,QAAQ,EAAI,CAAI,CAAC,EAAkB,CAE7D,GAAI,GAAa,IAEX,AADc,EAAc,GAChB,MAAM,CAAE,OAAO,EAAc,CAAC,EAAG,GAGnD,IAAI,EAAc,EAAc,CAAC,EAAG,GAIpC,OAFA,OAAO,CAAW,CAAC,EAAkB,CACrC,OAAO,EAAY,QAAQ,CACpB,EACT,GAAG,MAAM,CAAC,SAAU,CAAI,EACtB,OAAO,EACT,GACF,EAUI,GAAiB,SAAwB,CAAS,CAAE,CAAM,CAAE,CAAa,CAAE,CAAY,EACzF,IAAI,EAAmB,GAAU,CAC/B,KAAM,EACN,cAAe,EACf,OAAQ,CACV,GACI,EAAW,EAAe,GAAc,GAAoB,GAAsB,GAGtF,MAAO,CACL,WAFe,GAAqB,GAGpC,SAAU,CACZ,EACF,EC3eA,SAAS,GAAQ,CAAM,CAAE,CAAc,EAAI,IAAI,EAAO,OAAO,IAAI,CAAC,GAAS,GAAI,OAAO,qBAAqB,CAAE,CAAE,IAAI,EAAU,OAAO,qBAAqB,CAAC,GAAS,GAAmB,CAAA,EAAU,EAAQ,MAAM,CAAC,SAAU,CAAG,EAAI,OAAO,OAAO,wBAAwB,CAAC,EAAQ,GAAK,UAAU,CAAE,EAAC,EAAI,EAAK,IAAI,CAAC,KAAK,CAAC,EAAM,GAAU,CAAE,OAAO,EAAM,CAEpV,SAAS,GAAc,CAAM,EAAI,IAAK,IAAI,EAAI,EAAG,EAAI,UAAU,MAAM,CAAE,IAAK,CAAE,IAAI,EAAS,MAAQ,SAAS,CAAC,EAAE,CAAG,SAAS,CAAC,EAAE,CAAG,CAAC,EAAG,EAAI,EAAI,GAAQ,OAAO,GAAS,CAAC,GAAG,OAAO,CAAC,SAAU,CAAG,MAE3J,EAAA,EAF4L,CAAM,CAAC,EAAI,CAE1L,AAF0K,KAAR,EAEpJ,OAAO,cAAc,CAF+H,EAAQ,EAE5H,CAAE,MAAO,EAAO,WAAY,CAAA,EAAM,aAAc,CAAA,EAAM,SAAU,CAAA,CAAK,GAAa,AAFkC,CAE/B,CAFuC,EAElC,CAAG,EAFkD,GAAK,OAAO,yBAAyB,CAAG,OAAO,gBAAgB,CAAC,EAAQ,OAAO,yBAAyB,CAAC,IAAW,GAAQ,OAAO,IAAS,OAAO,CAAC,SAAU,CAAG,EAAI,OAAO,cAAc,CAAC,EAAQ,EAAK,OAAO,wBAAwB,CAAC,EAAQ,IAAO,GAAI,CAAE,OAAO,EAAQ,CAWlf,IAAI,GAAe,SAAS,IACjC,IAAI,EAAW,UAAU,MAAM,CAAG,GAAK,AAAiB,KAAA,IAAjB,SAAS,CAAC,EAAE,CAAiB,SAAS,CAAC,EAAE,CAAG,EAAE,CACjF,EAAQ,CAAC,EAqBb,OApBA,EAAS,OAAO,CAAC,SAAU,CAAO,EAChC,IAAI,EAAO,GAAc,CAAC,EAAG,GAE7B,GAAI,AAAC,GAAS,EAAK,GAAG,EAIlB,CAAC,EAAK,QAAQ,EAAI,CAAI,CAAC,EAAkB,GAC3C,EAAK,QAAQ,CAAG,CAAI,CAAC,EAAkB,CACvC,OAAO,CAAI,CAAC,EAAkB,EAGhC,IAAI,EAAiB,EAAK,QAAQ,EAAI,EAAE,CACxC,CAAK,CAAC,EAAgC,EAAK,IAAI,EAAI,EAAK,GAAG,EAAI,KAAK,CAAG,GAAc,CAAC,EAAG,GACzF,CAAK,CAAC,EAAK,GAAG,EAAI,EAAK,IAAI,EAAI,IAAI,CAAG,GAAc,CAAC,EAAG,GAEpD,GACF,CAAA,EAAQ,GAAc,GAAc,CAAC,EAAG,GAAQ,EAAa,GAAe,GAEhF,GACO,EACT,ECjCW,GAAiB,WAC1B,IAAI,EAAe,UAAU,MAAM,CAAG,GAAK,AAAiB,KAAA,IAAjB,SAAS,CAAC,EAAE,CAAiB,SAAS,CAAC,EAAE,CAAG,EAAE,CACrF,EAAO,UAAU,MAAM,CAAG,EAAI,SAAS,CAAC,EAAE,CAAG,KAAA,EAC7C,EAAQ,UAAU,MAAM,CAAG,EAAI,SAAS,CAAC,EAAE,CAAG,KAAA,EAClD,OAAO,EAAa,MAAM,CAAC,SAAU,CAAI,EACvC,GAAI,AAAS,MAAT,GAAgB,AAAS,MAAT,EAClB,MAAO,CAAA,EAGT,GAAI,AAAS,MAAT,GAAgB,AAAS,OAAT,GAAiB,GAAQ,CAAC,EAAM,GAAO,CACzD,IAAI,EAAU,EAAgC,GAE9C,GAAI,CAEF,GAAI,GACE,GAAA,cAAY,EAAC,GAAG,MAAM,CAAC,IAAU,IAAI,CAAC,IAMxC,GAAA,cAAY,EAAC,GAAG,MAAM,CAAC,GAAU,EAAE,EAAE,IAAI,CAAC,IAK1C,GAAA,cAAY,EAAC,GAAG,MAAM,CAAC,EAAS,UAAU,IAAI,CAAC,GAV/C,MAAO,CAAA,EAab,CAAE,MAAO,EAAO,CAChB,CACF,CAEA,MAAO,CAAA,EACT,GAAG,IAAI,CAAC,SAAU,CAAC,CAAE,CAAC,SAEpB,AAAI,IAAM,EACD,GAGL,IAAM,EACD,IAGF,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,MAAM,CAAG,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,MAAM,CACtE,GACF,EAQW,GAAe,SAAsB,CAAQ,CAAE,CAAQ,CAIlE,CAAQ,CAAE,CAAK,EACb,IAAI,EAAY,GAAY,GAExB,EAAe,GADA,OAAO,IAAI,CAAC,GACiB,GAAY,IAAK,SAEjE,AAAI,CAAC,GAAgB,EAAa,MAAM,CAAG,EAClC,EAAE,EAGN,GACH,CAAA,EAAe,CAAC,CAAY,CAAC,EAAa,MAAM,CAAG,EAAE,CAAC,AAAD,EAGhD,EAAa,GAAG,CAAC,SAAU,CAAW,EAC3C,IAAI,EAAW,CAAS,CAAC,EAAY,EAAI,CACvC,sBAAuB,GACvB,IAAK,EACP,EAEI,EAAM,IAAI,IACV,EAAc,AAAC,CAAA,EAAS,qBAAqB,EAAI,EAAE,AAAD,EAAG,GAAG,CAAC,SAAU,CAAG,SACxE,AAAI,EAAI,GAAG,CAAC,GACH,MAGT,EAAI,GAAG,CAAC,EAAK,CAAA,GACN,CAAS,CAAC,EAAI,EACvB,GAAG,MAAM,CAAC,SAAU,CAAI,EACtB,OAAO,EACT,GAMA,OAJI,EAAS,GAAG,EACd,EAAY,IAAI,CAAC,GAGZ,EACT,GAAG,IAAI,CAAC,IACV,gYCxFI,GAA6B,SAAU,CAAgB,EACzD,GAAA,UAAS,EAAC,EAAe,GACzB,IAAI,EAAS,GAAA,UAAY,EAAC,GAC1B,SAAS,IACP,IAAI,EACJ,GAAA,UAAe,EAAC,IAAI,CAAE,GACtB,IAAK,IAAI,EAAO,UAAU,MAAM,CAAE,EAAO,AAAI,MAAM,GAAO,EAAO,EAAG,EAAO,EAAM,IAC/E,CAAI,CAAC,EAAK,CAAG,SAAS,CAAC,EAAK,CAO9B,OALA,EAAQ,EAAO,IAAI,CAAC,KAAK,CAAC,EAAQ,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAChD,GAAA,SAAe,EAAC,GAAA,UAAsB,EAAC,GAAQ,QAAS,CACtD,SAAU,CAAA,EACV,UAAW,EACb,GACO,EACT,CA8BA,MA7BA,GAAA,UAAY,EAAC,EAAe,CAAC,CAC3B,IAAK,oBACL,MAAO,SAA2B,CAAK,CAAE,CAAS,EAGhD,QAAQ,GAAG,CAAC,EAAO,GACrB,CACF,EAAG,CACD,IAAK,SACL,MAAO,kBACL,AAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAED,GAAA,KAAI,EAAC,UAAM,CAAE,CAC/B,OAAQ,QACR,MAAO,wBACP,MAAO,IAAI,CAAC,KAAK,CAAC,SAAS,AAC7B,GAEK,IAAI,CAAC,KAAK,CAAC,QAAQ,CAC5B,CACF,EAAE,CAAE,CAAC,CACH,IAAK,2BACL,MAAO,SAAkC,CAAK,EAC5C,MAAO,CACL,SAAU,CAAA,EACV,UAAW,EAAM,OAAO,AAC1B,EACF,CACF,EAAE,EACK,EACT,EAAE,SAAK,CAAC,SAAS,EClDb,GAAc,SAAqB,CAAK,EAC1C,IACE,EAAS,AADO,GAAA,YAAU,EAAC,aAAW,EACjB,MAAM,CACzB,EAAQ,EAAM,KAAK,CACrB,EAAY,EAAM,SAAS,CAC3B,EAAW,EAAM,QAAQ,CACzB,EAAwB,EAAM,gBAAgB,CAE5C,EAAmB,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAW,YAAa,EAAQ,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAW,eAAgB,EAAM,SAAS,EAAG,GAAG,MAAM,CAAC,EAAW,+BAAgC,AADvM,CAAA,AAA0B,KAAK,IAA/B,EAAmC,EAAI,CAAoB,EAC+J,IAC3O,EAAiB,EAAM,aAAa,EAAI,GAC5C,MAAO,AAAwB,CAAA,IAAxB,EAAM,aAAa,CAA0B,GAAA,KAAI,EAAC,UAAM,CAAC,OAAO,CAAE,CACvE,UAAW,EACX,MAAO,EACP,SAAU,CACZ,GAAkB,GAAA,KAAI,EAAC,EAAgB,CACrC,SAAuB,GAAA,KAAI,EAAC,UAAM,CAAC,OAAO,CAAE,CAC1C,UAAW,EACX,MAAO,EACP,SAAU,CACZ,EACF,GACF,EC1BW,GAAO,WAChB,MAAoB,GAAA,MAAK,EAAC,MAAO,CAC/B,MAAO,MACP,OAAQ,MACR,QAAS,cACT,SAAU,CAAc,GAAA,MAAK,EAAC,OAAQ,CACpC,SAAU,CAAc,GAAA,MAAK,EAAC,iBAAkB,CAC9C,GAAI,cACJ,GAAI,KACJ,GAAI,aACJ,GAAI,cACJ,GAAI,mBACJ,SAAU,CAAc,GAAA,KAAI,EAAC,OAAQ,CACnC,UAAW,UACX,OAAQ,IACV,GAAiB,GAAA,KAAI,EAAC,OAAQ,CAC5B,UAAW,UACX,OAAQ,MACV,GAAG,AACL,GAAiB,GAAA,MAAK,EAAC,iBAAkB,CACvC,GAAI,aACJ,GAAI,KACJ,GAAI,cACJ,GAAI,cACJ,GAAI,mBACJ,SAAU,CAAc,GAAA,KAAI,EAAC,OAAQ,CACnC,UAAW,UACX,OAAQ,IACV,GAAiB,GAAA,KAAI,EAAC,OAAQ,CAC5B,UAAW,UACX,OAAQ,aACV,GAAiB,GAAA,KAAI,EAAC,OAAQ,CAC5B,UAAW,UACX,OAAQ,MACV,GAAG,AACL,GAAiB,GAAA,MAAK,EAAC,iBAAkB,CACvC,GAAI,cACJ,GAAI,eACJ,GAAI,cACJ,GAAI,cACJ,GAAI,mBACJ,SAAU,CAAc,GAAA,KAAI,EAAC,OAAQ,CACnC,UAAW,UACX,OAAQ,IACV,GAAiB,GAAA,KAAI,EAAC,OAAQ,CAC5B,UAAW,UACX,OAAQ,YACV,GAAiB,GAAA,KAAI,EAAC,OAAQ,CAC5B,UAAW,UACX,OAAQ,MACV,GAAG,AACL,GAAiB,GAAA,MAAK,EAAC,iBAAkB,CACvC,GAAI,cACJ,GAAI,eACJ,GAAI,cACJ,GAAI,cACJ,GAAI,mBACJ,SAAU,CAAc,GAAA,KAAI,EAAC,OAAQ,CACnC,UAAW,UACX,OAAQ,IACV,GAAiB,GAAA,KAAI,EAAC,OAAQ,CAC5B,UAAW,UACX,OAAQ,aACV,GAAiB,GAAA,KAAI,EAAC,OAAQ,CAC5B,UAAW,UACX,OAAQ,MACV,GAAG,AACL,GAAG,AACL,GAAiB,GAAA,KAAI,EAAC,IAAK,CACzB,OAAQ,OACR,YAAa,EACb,KAAM,OACN,SAAU,UACV,SAAuB,GAAA,KAAI,EAAC,IAAK,CAC/B,UAAW,oCACX,SAAuB,GAAA,KAAI,EAAC,IAAK,CAC/B,UAAW,kCACX,SAAuB,GAAA,MAAK,EAAC,IAAK,CAChC,SAAU,CAAc,GAAA,MAAK,EAAC,IAAK,CACjC,SAAU,UACV,SAAU,CAAc,GAAA,MAAK,EAAC,IAAK,CACjC,SAAU,CAAc,GAAA,KAAI,EAAC,OAAQ,CACnC,EAAG,g3BACH,KAAM,wBACR,GAAiB,GAAA,KAAI,EAAC,OAAQ,CAC5B,EAAG,o0BACH,KAAM,wBACR,GAAG,AACL,GAAiB,GAAA,KAAI,EAAC,OAAQ,CAC5B,EAAG,+fACH,KAAM,wBACR,GAAG,AACL,GAAiB,GAAA,KAAI,EAAC,UAAW,CAC/B,KAAM,yBACN,GAAI,aACJ,GAAI,aACJ,GAAI,aACJ,GAAI,WACN,GAAG,AACL,EACF,EACF,EACF,GAAG,AACL,GACF,mBCzGW,GAA0B,SAAS,EAAwB,CAAQ,EAC5E,MAAO,AAAC,CAAA,GAAY,EAAE,AAAD,EAAG,MAAM,CAAC,SAAU,CAAG,CAAE,CAAI,QAIhD,CAHI,EAAK,GAAG,EACV,EAAI,IAAI,CAAC,EAAK,GAAG,EAEf,EAAK,QAAQ,EAAI,EAAK,MAAM,EACf,EAAI,MAAM,CAAC,EAAwB,EAAK,QAAQ,EAAI,EAAK,MAAM,GAAK,EAAE,EAGhF,EACT,EAAG,EAAE,EACP,EAoBO,SAAS,GAAc,CAAS,EACrC,OAAO,EAAU,GAAG,CAAC,SAAU,CAAI,EACjC,IAAI,EAAW,EAAK,QAAQ,EAAI,EAAE,CAC9B,EAAY,GAAA,SAAa,EAAC,CAAC,EAAG,GAIlC,GAHI,CAAC,EAAU,QAAQ,EAAI,EAAU,MAAM,EACzC,CAAA,EAAU,QAAQ,CAAG,EAAU,MAAM,AAAD,EAElC,CAAC,EAAU,IAAI,EAAI,EAAU,UAAU,CACzC,OAAO,KAET,GAAI,GAAmC,MAAtB,GAA8C,EAAU,QAAQ,CAAE,CACjF,GAAI,CAAC,EAAU,kBAAkB,EAAI,EAAS,IAAI,CAAC,SAAU,CAAK,EAChE,OAAO,GAAS,EAAM,IAAI,EAAI,CAAC,EAAM,UAAU,CACjD,GACE,MAAO,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAO,CAAC,EAAG,CAChD,SAAU,GAAc,EAC1B,GAGF,OAAO,EAAU,QAAQ,CAC3B,CAEA,OADA,OAAO,EAAU,MAAM,CAChB,EACT,GAAG,MAAM,CAAC,SAAU,CAAI,EACtB,OAAO,EACT,GACF,kKCzDA,SAAS,KACP,MAAoB,GAAA,KAAI,EAAC,MAAO,CAC9B,MAAO,MACP,OAAQ,MACR,QAAS,YACT,KAAM,eACN,cAAe,OACf,SAAuB,GAAA,KAAI,EAAC,OAAQ,CAClC,EAAG,2MACL,EACF,GACF,sBCTI,GAAoB,SAA2B,CAAK,EACtD,IAAI,EAAe,EAAgB,EACnC,MAAO,GAAA,SAAe,EAAC,CAAC,EAAG,EAAM,YAAY,CAAE,CAC7C,SAAU,WACV,gBAAiB,OACjB,OAAQ,MACR,MAAO,OACP,OAAQ,OACR,SAAU,CAAC,OAAQ,OAAO,CAC1B,UAAW,SACX,aAAc,OACd,eAAgB,QAChB,WAAY,iBACZ,QAAS,OACT,WAAY,SACZ,eAAgB,SAChB,OAAQ,UACR,MAAO,AAAmC,OAAlC,CAAA,EAAgB,EAAM,MAAM,AAAD,GAAe,AAAkB,KAAK,IAAvB,GAA4B,AAA0C,OAAzC,CAAA,EAAgB,EAAc,KAAK,AAAD,GAAe,AAAkB,KAAK,IAAvB,EAA2B,KAAK,EAAI,EAAc,wBAAwB,CAC1M,gBAAiB,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,sBAAsB,CACxN,UAAW,kGACX,UAAW,CACT,MAAO,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,6BAA6B,CACrN,UAAW,oGACb,EACA,WAAY,CACV,SAAU,MACZ,EACA,UAAW,CACT,WAAY,kBACZ,UAAW,eACb,EACA,cAAe,CACb,UAAW,CACT,UAAW,gBACb,CACF,CACF,GACF,ECrCI,GAAY,CAAC,WAAY,YAAY,CAK9B,GAAgB,SAAuB,CAAK,EACrD,IDgCuB,EChCnB,EAAW,EAAM,QAAQ,CAC3B,EAAY,EAAM,SAAS,CAC3B,EAAO,GAAA,SAAwB,EAAC,EAAO,IACrC,GD6BmB,EC7BE,EAAM,SAAS,CD8BjC,GAAA,WAAY,EAAC,yBAA0B,SAAU,CAAK,EAI3D,MAAO,CAAC,GAHa,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,CAAC,EAAG,CAC/D,aAAc,IAAI,MAAM,CAAC,EAC3B,IAC0C,CAC5C,IClCE,EAAU,EAAU,OAAO,CAC3B,EAAS,EAAU,MAAM,QAC3B,AAAI,GAAY,EAAkB,KAC3B,EAAsB,GAAA,KAAI,EAAC,MAAO,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAO,CAAC,EAAG,CAClF,UAAW,GAAA,UAAU,EAAC,EAAM,SAAS,CAAE,EAAQ,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,SAAS,CAAE,cAAe,GAAY,GAAG,MAAM,CAAC,EAAM,SAAS,CAAE,cAAe,IACnL,SAAuB,GAAA,KAAI,EAAC,GAAc,CAAC,EAC7C,KACF,wHChBI,GAAY,CAAC,YAAa,YAAa,UAAW,OAAQ,SAAU,WAAY,UAAW,WAAW,CAOtG,GAAoB,EAAM,UAAU,CAAC,SAAU,CAAK,CAAE,CAAG,EAC3D,IAAI,EAAY,EAAM,SAAS,CAC7B,EAAY,EAAM,SAAS,CAC3B,EAAU,EAAM,OAAO,CACvB,EAAO,EAAM,IAAI,CACjB,EAAS,EAAM,MAAM,CACrB,EAAW,EAAM,QAAQ,CACzB,EAAU,EAAM,OAAO,CACvB,EAAW,EAAM,QAAQ,CACzB,EAAY,GAAA,SAAwB,EAAC,EAAO,IAC1C,EAAU,EAAM,MAAM,GACtB,EAAY,GAAA,gBAAa,EAAC,EAAS,GACvC,GAAA,UAAO,EAAC,CAAA,CAAQ,CAAA,GAAa,CAAO,EAAI,+CACxC,GAAA,kBAAe,EAAC,GAChB,IAAI,EAAoB,EAAM,UAAU,CAAC,UAAO,EAC9C,EAAwB,EAAkB,SAAS,CACnD,EAAY,AAA0B,KAAK,IAA/B,EAAmC,UAAY,EAC3D,EAAgB,EAAkB,aAAa,CAC7C,EAAc,GAAA,UAAU,EAAC,EAAe,EAAW,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAW,SAAU,CAAC,CAAC,GAAQ,CAAC,CAAC,GAAY,GAC9H,EAAiB,GAAA,UAAU,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAW,SAAU,CAAC,CAAC,IAKjF,EAAgB,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,eAAY,EAAG,CAAC,EAAG,CACrE,UAAW,EACX,MANa,EAAS,CACtB,YAAa,UAAU,MAAM,CAAC,EAAQ,QACtC,UAAW,UAAU,MAAM,CAAC,EAAQ,OACtC,EAAI,KAAA,EAIF,QAAS,CACX,GACK,GACH,OAAO,EAAc,OAAO,CAgB9B,IAAI,EAAe,EAInB,OAHqB,KAAA,IAAjB,GAA8B,GAChC,CAAA,EAAe,EAAC,EAEE,EAAM,aAAa,CAAC,OAAQ,GAAA,UAAQ,EAAC,CACvD,KAAM,KACR,EAAG,EAAW,CACZ,IAAK,EACL,SAAU,EACV,QAAS,EACT,UAAW,CACb,GAtBE,AAAI,EACkB,EAAM,aAAa,CAAC,EAAW,EAAe,GAEhE,GACF,GAAA,UAAO,EAAC,CAAA,CAAQ,GAAY,AAAmC,IAAnC,EAAM,QAAQ,CAAC,KAAK,CAAC,IAAgC,EAAM,cAAc,CAAC,IAAa,AAAuC,QAAvC,EAAM,QAAQ,CAAC,IAAI,CAAC,GAAU,IAAI,CAAY,4FAC7I,EAAM,aAAa,CAAC,MAAO,GAAA,UAAQ,EAAC,CAAC,EAAG,EAAe,CACzE,QAAS,CACX,GAAI,IAEC,MAcX,GACA,GAAK,WAAW,CAAG,WCpEnB,IAAI,GAAY,CAAC,OAAQ,WAAW,CAGhC,GAAc,IAAI,IAItB,SAAS,GAAwB,CAAU,EACzC,IAAI,EAAQ,UAAU,MAAM,CAAG,GAAK,AAAiB,KAAA,IAAjB,SAAS,CAAC,EAAE,CAAiB,SAAS,CAAC,EAAE,CAAG,EAC5E,EAAmB,CAAU,CAAC,EAAM,CACxC,GALe,AAAqB,UAArB,OAKY,GALqB,AAKrB,EAL+B,MAAM,EAAI,CAAC,GAAY,GAAG,CAKzD,GAAmB,CAC5C,IAAI,EAAS,SAAS,aAAa,CAAC,UACpC,EAAO,YAAY,CAAC,MAAO,GAC3B,EAAO,YAAY,CAAC,iBAAkB,GAClC,EAAW,MAAM,CAAG,EAAQ,IAC9B,EAAO,MAAM,CAAG,WACd,GAAwB,EAAY,EAAQ,GAC9C,EACA,EAAO,OAAO,CAAG,WACf,GAAwB,EAAY,EAAQ,GAC9C,GAEF,GAAY,GAAG,CAAC,GAChB,SAAS,IAAI,CAAC,WAAW,CAAC,GAC5B,CACF,CACe,SAAS,KACtB,IAAI,EAAU,UAAU,MAAM,CAAG,GAAK,AAAiB,KAAA,IAAjB,SAAS,CAAC,EAAE,CAAiB,SAAS,CAAC,EAAE,CAAG,CAAC,EAC/E,EAAY,EAAQ,SAAS,CAC/B,EAAwB,EAAQ,gBAAgB,CAChD,EAAmB,AAA0B,KAAK,IAA/B,EAAmC,CAAC,EAAI,EAQzD,GAAa,AAAoB,aAApB,OAAO,UAA4B,AAAkB,aAAlB,OAAO,QAA0B,AAAkC,YAAlC,OAAO,SAAS,aAAa,GAC5G,MAAM,OAAO,CAAC,GAEhB,GAAwB,EAAU,OAAO,IAEzC,GAAwB,CAAC,EAAU,GAGvC,IAAI,EAAwB,EAAM,UAAU,CAAC,SAAU,CAAK,CAAE,CAAG,EAC/D,IAAI,EAAO,EAAM,IAAI,CACnB,EAAW,EAAM,QAAQ,CACzB,EAAY,GAAA,SAAwB,EAAC,EAAO,IAG1C,EAAU,KASd,OARI,EAAM,IAAI,EACZ,CAAA,EAAuB,EAAM,aAAa,CAAC,MAAO,CAChD,UAAW,IAAI,MAAM,CAAC,EACxB,EAAC,EAEC,GACF,CAAA,EAAU,CAAO,EAEC,EAAM,aAAa,CAAC,GAAM,GAAA,UAAQ,EAAC,CAAC,EAAG,EAAkB,EAAW,CACtF,IAAK,CACP,GAAI,GACN,GAEA,OADA,EAAS,WAAW,CAAG,WAChB,EACT,8ECrEI,GAAkB,CACpB,SAAU,QACV,OAAQ,OACR,aAAc,QACd,YAAa,CAAA,EACb,YAAa,CAAA,EACb,YAAa,GACb,aAAc,UACd,WAAY,CAAA,CACd,ECNI,GAA4B,SAAmC,CAAK,CAAE,CAAI,EAE5E,IADI,EAAe,EACf,EAAY,EAAK,QAAQ,CAAC,cAAgB,AAAmC,OAAlC,CAAA,EAAgB,EAAM,MAAM,AAAD,GAAe,AAAkB,KAAK,IAAvB,EAA2B,KAAK,EAAI,EAAc,MAAM,CAAG,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,KAAK,CACzP,MAAO,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,YAAY,EAAG,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CACpO,WAAY,cACZ,MAAO,MAAA,EAA6C,KAAK,EAAI,EAAU,aAAa,CACpF,OAAQ,MACV,EAAG,GAAG,MAAM,CAAC,EAAM,YAAY,CAAE,cAAe,CAC9C,WAAY,iBACd,GAAI,GAAG,MAAM,CAAC,EAAM,YAAY,CAAE,qBAAsB,GAAA,SAAe,EAAC,CAAC,EAAG,KAAK,MAAM,CAAC,EAAM,MAAM,CAAE,aAAc,CAClH,mBAAoB,EACtB,IAAK,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,CACnD,MAAO,OACP,OAAQ,OACR,QAAS,aACX,GAAI,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,CAClD,gBAAiB,CACf,MAAO,MACT,CACF,GAAI,GAAG,MAAM,CAAC,EAAM,YAAY,CAAE,cAAe,CAC/C,QAAS,OACT,WAAY,QACd,GAAI,eAAgB,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,0BAA0B,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAuB,MAAM,CAAC,EAAM,MAAM,CAAE,4BAA4B,MAAM,CAAC,EAAM,MAAM,CAAE,0BAA0B,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAuB,MAAM,CAAC,EAAM,MAAM,CAAE,4BAA4B,MAAM,CAAC,EAAM,MAAM,CAAE,oBAAoB,MAAM,CAAC,EAAM,MAAM,CAAE,mCAAmC,MAAM,CAAC,EAAM,MAAM,CAAE,oBAAoB,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,CACxhB,cAAe,eACf,YAAa,gBACf,GAAI,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAuB,MAAM,CAAC,EAAM,MAAM,CAAE,4BAA4B,MAAM,CAAC,EAAM,MAAM,CAAE,6BAA6B,MAAM,CAAC,EAAM,MAAM,CAAE,mCAAmC,MAAM,CAAC,EAAM,MAAM,CAAE,6BAA6B,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,CAC3S,gBAAiB,MAAA,EAA6C,KAAK,EAAI,EAAU,uBAAuB,CACxG,aAAc,EAAM,cAAc,AACpC,GAAI,GAAG,MAAM,CAAC,EAAM,YAAY,CAAE,UAAW,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,0BAA2B,CAClH,cAAe,CACjB,KAAM,eAAgB,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CACpG,QAAS,OACT,cAAe,MACf,WAAY,SACZ,IAAK,EAAM,QAAQ,AACrB,EAAG,GAAG,MAAM,CAAC,EAAM,YAAY,CAAE,cAAe,CAC9C,SAAU,OACV,aAAc,WACd,SAAU,SACV,UAAW,YACX,WAAY,QACd,GAAI,cAAe,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CACjD,SAAU,GACV,OAAQ,EACV,EAAG,GAAG,MAAM,CAAC,EAAM,YAAY,CAAE,cAAe,CAC9C,OAAQ,OACR,MAAO,OACP,WAAY,kBACZ,WAAY,CACV,WAAY,kBACZ,OAAQ,MACV,CACF,GAAI,GAAG,MAAM,CAAC,EAAM,YAAY,CAAE,uBAAwB,CACxD,QAAS,iBACX,IAAK,sBAAuB,CAC1B,cAAe,SACf,eAAgB,QAClB,GAAI,IAAI,MAAM,CAAC,EAAM,YAAY,CAAE,qBAAsB,CACvD,IAAK,EAAM,QAAQ,CACnB,OAAQ,GACR,SAAU,QACZ,GAAI,IAAI,MAAM,CAAC,EAAM,YAAY,CAAE,8BAA+B,GAAA,SAAe,EAAC,CAChF,WAAY,OACZ,IAAK,CACP,EAAG,IAAI,MAAM,CAAC,EAAM,YAAY,CAAE,yBAA0B,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAC1F,QAAS,MACX,EAAG,GAAG,MAAM,CAAC,EAAM,YAAY,CAAE,cAAe,CAC9C,OAAQ,OACR,MAAO,OACP,WAAY,kBACZ,WAAY,CACV,WAAY,iBACZ,OAAQ,MACV,CACF,GAAI,GAAG,MAAM,CAAC,EAAM,YAAY,CAAE,cAAe,CAC/C,QAAS,eACT,QAAS,oBACT,UAAW,SACX,SAAU,GACV,OAAQ,GACR,WAAY,OACZ,SAAU,SACV,aAAc,WACd,WAAY,SACZ,MAAO,OACP,OAAQ,EACR,QAAS,EACT,iBAAkB,CACpB,MAAO,UAAW,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,0BAA2B,CACvF,SAAU,GACV,MAAO,EAAM,cAAc,CAC3B,WAAY,CACV,gBAAiB,CACnB,CACF,IAAK,kBAAmB,CACtB,MAAO,EAAM,kBAAkB,CAC/B,SAAU,GACV,WAAY,EACd,IAAK,EAAK,QAAQ,CAAC,cAAgB,CAAC,EAAI,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,iBAAiB,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,YAAY,CAAE,eAAgB,CAClN,WAAY,YACd,KAAM,CAAC,EAAG,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,CAC5E,gBAAiB,4BACjB,0BAA2B,YAC3B,eAAgB,WAClB,IACF,ECxFI,GAAkB,SAAyB,CAAK,EAClD,IAAI,EAAY,GAAA,UAAQ,EAAC,EAAM,SAAS,EACtC,EAAa,GAAA,SAAc,EAAC,EAAW,GACvC,EAAY,CAAU,CAAC,EAAE,CACzB,EAAe,CAAU,CAAC,EAAE,CAC1B,EAAa,GAAA,UAAQ,EAAC,CAAA,GACxB,EAAa,GAAA,SAAc,EAAC,EAAY,GACxC,EAAO,CAAU,CAAC,EAAE,CACpB,EAAU,CAAU,CAAC,EAAE,OAOzB,CANA,GAAA,WAAS,EAAC,WACR,EAAQ,CAAA,GACR,WAAW,WACT,EAAa,EAAM,SAAS,EAC9B,EAAG,KACL,EAAG,CAAC,EAAM,SAAS,CAAC,EAChB,EAAM,OAAO,EACR,EAAM,QAAQ,CAEH,GAAA,KAAI,EAAC,UAAO,CAAE,CAChC,MAAO,EAAM,KAAK,CAClB,KAAM,EAAA,KAAa,EAAM,SAAS,EAAG,EACrC,UAAW,QACX,aAAc,EACd,SAAU,EAAM,QAAQ,AAC1B,GACF,EACI,GAAW,GAAqB,CAClC,UAAW,GAAgB,WAAW,AACxC,GAQI,GAAU,SAAiB,CAAI,EACjC,IAAI,EAAe,UAAU,MAAM,CAAG,GAAK,AAAiB,KAAA,IAAjB,SAAS,CAAC,EAAE,CAAiB,SAAS,CAAC,EAAE,CAAG,QACnF,EAAY,UAAU,MAAM,CAAG,EAAI,SAAS,CAAC,EAAE,CAAG,KAAA,EACtD,GAAI,AAAgB,UAAhB,OAAO,GAAqB,AAAS,KAAT,EAAa,CAC3C,GAAI,GAAA,QAAK,EAAC,IC1DL,uCAAuC,IAAI,CD0DvB,GACvB,MAAoB,GAAA,KAAI,EAAC,MAAO,CAC9B,MAAO,GACP,IAAK,EACL,IAAK,OACL,UAAW,CACb,EAAG,GAEL,GAAI,EAAK,UAAU,CAAC,GAClB,MAAoB,GAAA,KAAI,EAAC,GAAU,CACjC,KAAM,CACR,GAEJ,CACA,OAAO,EACT,EACI,GAAqB,SAA4B,CAAK,SACxD,AAAI,GAAS,AAAiB,UAAjB,OAAO,EACL,EAAM,SAAS,CAAC,EAAG,GAAG,WAAW,GAGzC,KACT,EACI,GAAwB,GAAA,UAAY,EAAC,SAAS,EAAS,CAAK,EAC9D,IAAI,EAAQ,IAAI,CAChB,GAAA,UAAe,EAAC,IAAI,CAAE,GACtB,GAAA,SAAe,EAAC,IAAI,CAAE,QAAS,KAAK,GACpC,GAAA,SAAe,EAAC,IAAI,CAAE,kBAAmB,WACvC,IAAI,EAAY,UAAU,MAAM,CAAG,GAAK,AAAiB,KAAA,IAAjB,SAAS,CAAC,EAAE,CAAiB,SAAS,CAAC,EAAE,CAAG,EAAE,CAClF,EAAQ,UAAU,MAAM,CAAG,EAAI,SAAS,CAAC,EAAE,CAAG,KAAA,EAC9C,EAAe,UAAU,MAAM,CAAG,EAAI,SAAS,CAAC,EAAE,CAAG,KAAA,EACzD,OAAO,EAAU,GAAG,CAAC,SAAU,CAAI,EACjC,OAAO,EAAM,gBAAgB,CAAC,EAAM,EAAO,GAC7C,GAAG,MAAM,CAAC,SAAU,CAAI,EACtB,OAAO,EACT,GAAG,IAAI,CAAC,GACV,GAEA,GAAA,SAAe,EAAC,IAAI,CAAE,mBAAoB,SAAU,CAAI,CAAE,CAAK,CAAE,CAAY,EAC3E,IAAI,EAAc,EAAM,KAAK,CAC3B,EAAoB,EAAY,iBAAiB,CACjD,EAAgB,EAAY,aAAa,CACzC,EAAY,EAAY,SAAS,CACjC,EAAY,EAAY,SAAS,CACjC,EAAO,EAAY,IAAI,CACvB,EAAe,EAAY,YAAY,CACvC,EAAS,EAAY,MAAM,CACzB,EAAU,AAAC,CAAA,MAAA,EAAmC,KAAK,EAAI,EAAK,IAAI,AAAD,IAAO,SAAW,AAAW,QAAX,EACjF,EAAc,EAAM,KAAK,CAAC,KAAK,CAC/B,EAAO,EAAM,WAAW,CAAC,GACzB,EAAW,AAAC,CAAA,MAAA,EAAmC,KAAK,EAAI,EAAK,QAAQ,AAAD,GAAO,CAAA,MAAA,EAAmC,KAAK,EAAI,EAAK,MAAM,AAAD,EACjI,EAAW,GAAW,AAAU,IAAV,EAAc,QAAU,KAAA,EAClD,GAAI,MAAM,OAAO,CAAC,IAAa,EAAS,MAAM,CAAG,EAAG,CAGlD,IAFI,EAAc,EAAc,EAAc,EAAc,EAExD,EAAgB,AAAU,IAAV,GAAe,GAAW,AAAU,IAAV,EAG1C,EAAU,GAAQ,EAAK,IAAI,CAAE,EAAc,GAAG,MAAM,CAAC,EAAe,UAAU,MAAM,CAAC,AAAiC,OAAhC,CAAA,EAAe,EAAM,KAAK,AAAD,GAAe,AAAiB,KAAK,IAAtB,EAA0B,KAAK,EAAI,EAAa,MAAM,GAIpL,EAAc,GAAa,EAAgB,GAAmB,GAAQ,KACtE,EAA4B,GAAA,MAAK,EAAC,MAAO,CAC3C,UAAW,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAe,eAAgB,AAAiC,OAAhC,CAAA,EAAe,EAAM,KAAK,AAAD,GAAe,AAAiB,KAAK,IAAtB,EAA0B,KAAK,EAAI,EAAa,MAAM,CAAE,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAe,yBAA0B,GAAY,GAAG,MAAM,CAAC,EAAe,gCAAgC,MAAM,CAAC,GAAe,GAAY,GAAG,MAAM,CAAC,EAAe,qBAAsB,AAAa,UAAb,GAAuB,GAAG,MAAM,CAAC,EAAe,8BAA+B,AAAC,CAAA,MAAA,EAAmC,KAAK,EAAI,EAAK,kBAAkB,AAAD,GAAM,IACrkB,SAAU,CAAC,AAAa,UAAb,GAAwB,EAAY,KAAO,GAAiB,EAAuB,GAAA,KAAI,EAAC,OAAQ,CACzG,UAAW,GAAG,MAAM,CAAC,EAAe,eAAe,MAAM,CAAC,AAAiC,OAAhC,CAAA,EAAe,EAAM,KAAK,AAAD,GAAe,AAAiB,KAAK,IAAtB,EAA0B,KAAK,EAAI,EAAa,MAAM,EAAE,IAAI,GAC/J,SAAU,CACZ,GAAK,EAA0B,GAAA,KAAI,EAAC,OAAQ,CAC1C,UAAW,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAe,cAAe,AAAiC,OAAhC,CAAA,EAAe,EAAM,KAAK,AAAD,GAAe,AAAiB,KAAK,IAAtB,EAA0B,KAAK,EAAI,EAAa,MAAM,CAAE,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAe,uBAAwB,AAAa,UAAb,GAAwB,GAAkB,CAAA,GAAW,CAAU,IACnS,SAAU,CACZ,GAAG,AACL,GAGI,EAAQ,EAAoB,EAAkB,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAO,CAAC,EAAG,CAC3F,MAAO,CAAA,CACT,GAAI,EAAc,EAAM,KAAK,EAAI,EAGjC,GAAI,GAAW,AAAU,IAAV,GAAe,EAAM,KAAK,CAAC,SAAS,EAAI,CAAC,EAAK,uBAAuB,CAClF,OAAO,EAAM,eAAe,CAAC,EAAU,EAAQ,EAAG,GAEpD,IAAI,EAAe,EAAM,eAAe,CAAC,EAAU,EAAQ,EAAG,GAAW,AAAU,IAAV,GAAe,EAAM,KAAK,CAAC,SAAS,CAAG,EAAQ,EAAQ,GAChI,MAAO,CAAC,CACN,KAAM,EACN,IAAK,EAAK,GAAG,EAAI,EAAK,IAAI,CAC1B,MAAO,EACP,QAAS,EAAU,KAAA,EAAY,EAAK,YAAY,CAChD,SAAU,EACV,UAAW,GAAA,UAAU,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAe,UAAW,AAAa,UAAb,GAAuB,GAAG,MAAM,CAAC,EAAe,YAAa,AAAa,UAAb,GAAuB,GAAG,MAAM,CAAC,EAAe,qBAAsB,AAAa,UAAb,GAAwB,GAAiB,GAC5R,EAAG,GAAW,AAAU,IAAV,EAAc,CAC1B,KAAM,UACN,UAAW,EACX,UAAW,GAAG,MAAM,CAAC,EAAe,YACpC,IAAK,AAAC,CAAA,EAAK,GAAG,EAAI,EAAK,IAAI,AAAD,EAAK,iBAC/B,MAAO,CACL,QAAS,EACT,eAAgB,EAChB,OAAQ,EAAM,KAAK,CAAC,SAAS,CAAG,MAAQ,WACxC,iBAAkB,EAAM,KAAK,CAAC,SAAS,CAAG,EAAI,EAC9C,YAAa,MAAA,GAAkD,AAA+C,OAA9C,CAAA,EAAsB,EAAY,MAAM,AAAD,GAAe,AAAwB,KAAK,IAA7B,GAAkC,AAAsD,OAArD,CAAA,EAAsB,EAAoB,KAAK,AAAD,GAAe,AAAwB,KAAK,IAA7B,EAAiC,KAAK,EAAI,EAAoB,oBAAoB,AAC1S,CACF,EAAI,KAAA,EAAU,CAAC,MAAM,CAAC,SACxB,CACA,MAAO,CACL,UAAW,GAAG,MAAM,CAAC,EAAe,cACpC,SAAU,EAAK,QAAQ,CACvB,IAAK,EAAK,GAAG,EAAI,EAAK,IAAI,CAC1B,QAAS,EAAK,YAAY,CAE1B,MAAO,EAAM,eAAe,CAAC,EAAM,EAAO,EAC5C,EACF,GACA,GAAA,SAAe,EAAC,IAAI,CAAE,cAAe,SAAU,CAAI,EACjD,IAAI,EAAO,EAAK,IAAI,CAClB,EAAS,EAAK,MAAM,CAClB,EAAe,EAAM,KAAK,CAC5B,EAAO,EAAa,IAAI,CACxB,EAAgB,EAAa,aAAa,CACxC,EAAY,QAOhB,CANI,GAAU,AAAC,CAAA,MAAA,EAAmC,KAAK,EAAI,EAAK,MAAM,AAAD,IAAO,CAAA,GAC1E,CAAA,EAAY,MAAA,EAAqD,KAAK,EAAI,EAAc,CACtF,GAAI,EACJ,eAAgB,CAClB,EAAC,EAEC,EAAM,KAAK,CAAC,cAAc,EACrB,EAAM,KAAK,CAAC,cAAc,CAAC,EAAM,EAAW,EAAM,KAAK,EAEzD,EACT,GAMA,GAAA,SAAe,EAAC,IAAI,CAAE,kBAAmB,SAAU,CAAI,CAAE,CAAK,CAAE,CAAY,EAE1E,IADI,EAAc,EAAe,EAAe,EA6C1C,EAAe,EAAe,EA5ChC,EAAW,EAAM,cAAc,CAAC,EAAK,IAAI,EAAI,KAC7C,EAAe,EAAM,KAAK,CAC5B,EAAwB,EAAa,QAAQ,CAI7C,EAAW,EAAa,QAAQ,CAChC,EAAa,EAAa,UAAU,CACpC,EAAiB,EAAa,cAAc,CAC5C,EAAe,EAAa,YAAY,CAGtC,EAAgB,EAAM,WAAW,CAAC,GAClC,EAAe,EAAM,KAAK,CAC5B,EAAgB,EAAa,aAAa,CAC1C,EAAO,EAAa,IAAI,CACxB,EAAY,EAAa,SAAS,CAChC,EAAU,AAAC,CAAA,MAAA,EAAmC,KAAK,EAAI,EAAK,IAAI,AAAD,IAAO,QAEtE,EAAU,AAAU,IAAV,GAAe,GAAW,AAAU,IAAV,EACpC,EAAO,AAAC,EAAiB,GAAQ,EAAK,IAAI,CAAE,EAAc,GAAG,MAAM,CAAC,EAAe,UAAU,MAAM,CAAC,AAAiC,OAAhC,CAAA,EAAe,EAAM,KAAK,AAAD,GAAe,AAAiB,KAAK,IAAtB,EAA0B,KAAK,EAAI,EAAa,MAAM,GAAjL,KAGlB,EAAc,GAAa,EAAU,GAAmB,GAAiB,KACzE,EAA2B,GAAA,MAAK,EAAC,MAAO,CAC1C,UAAW,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAe,eAAgB,AAAkC,OAAjC,CAAA,EAAgB,EAAM,KAAK,AAAD,GAAe,AAAkB,KAAK,IAAvB,EAA2B,KAAK,EAAI,EAAc,MAAM,CAAE,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAe,yBAA0B,GAAY,GAAG,MAAM,CAAC,EAAe,gCAAgC,MAAM,CAAC,GAAe,GAAY,GAAG,MAAM,CAAC,EAAe,8BAA+B,AAAC,CAAA,MAAA,EAAmC,KAAK,EAAI,EAAK,kBAAkB,AAAD,GAAM,IAClf,SAAU,CAAc,GAAA,KAAI,EAAC,OAAQ,CACnC,UAAW,GAAG,MAAM,CAAC,EAAe,eAAe,MAAM,CAAC,AAAkC,OAAjC,CAAA,EAAgB,EAAM,KAAK,AAAD,GAAe,AAAkB,KAAK,IAAvB,EAA2B,KAAK,EAAI,EAAc,MAAM,EAAE,IAAI,GAClK,MAAO,CACL,QAAS,AAAgB,OAAhB,GAAyB,EAAgB,GAAT,MAC3C,EACA,SAAU,GAAqB,GAAA,KAAI,EAAC,OAAQ,CAC1C,UAAW,UACX,SAAU,CACZ,EACF,GAAiB,GAAA,KAAI,EAAC,OAAQ,CAC5B,UAAW,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAe,cAAe,AAAkC,OAAjC,CAAA,EAAgB,EAAM,KAAK,AAAD,GAAe,AAAkB,KAAK,IAAvB,EAA2B,KAAK,EAAI,EAAc,MAAM,CAAE,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAe,uBAAwB,GAAY,CAAA,GAAQ,CAAU,IACrQ,SAAU,CACZ,GAAG,AACL,EAAG,GACC,EAAY,GAAA,QAAK,EAAC,GA0BtB,GAvBI,GAEF,CAAA,EAA2B,GAAA,MAAK,EAAC,OAAQ,CACvC,QAAS,WACP,IAAI,EAAS,EACb,AAAuB,OAAtB,CAAA,EAAU,MAAK,GAAe,AAAY,KAAK,IAAjB,GAAsB,AAAkC,OAAjC,CAAA,EAAe,EAAQ,IAAI,AAAD,GAAe,AAAiB,KAAK,IAAtB,GAA2B,EAAa,IAAI,CAAC,EAAS,EAAU,UACjK,EACA,UAAW,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAe,eAAgB,AAAkC,OAAjC,CAAA,EAAgB,EAAM,KAAK,AAAD,GAAe,AAAkB,KAAK,IAAvB,EAA2B,KAAK,EAAI,EAAc,MAAM,CAAE,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAe,yBAA0B,GAAY,GAAG,MAAM,CAAC,EAAe,gCAAgC,MAAM,CAAC,GAAe,GAAY,GAAG,MAAM,CAAC,EAAe,cAAe,CAAA,GAAO,GAAG,MAAM,CAAC,EAAe,8BAA+B,AAAC,CAAA,MAAA,EAAmC,KAAK,EAAI,EAAK,kBAAkB,AAAD,GAAM,IACjjB,SAAU,CAAc,GAAA,KAAI,EAAC,OAAQ,CACnC,UAAW,GAAG,MAAM,CAAC,EAAe,eAAe,MAAM,CAAC,AAAkC,OAAjC,CAAA,EAAgB,EAAM,KAAK,AAAD,GAAe,AAAkB,KAAK,IAAvB,EAA2B,KAAK,EAAI,EAAc,MAAM,EAAE,IAAI,GAClK,MAAO,CACL,QAAS,AAAgB,OAAhB,GAAyB,EAAgB,GAAT,MAC3C,EACA,SAAU,GAAqB,GAAA,KAAI,EAAC,OAAQ,CAC1C,UAAW,UACX,SAAU,CACZ,EACF,GAAiB,GAAA,KAAI,EAAC,OAAQ,CAC5B,UAAW,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAe,cAAe,AAAkC,OAAjC,CAAA,EAAgB,EAAM,KAAK,AAAD,GAAe,AAAkB,KAAK,IAAvB,EAA2B,KAAK,EAAI,EAAc,MAAM,CAAE,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAe,uBAAwB,GAAY,CAAA,GAAQ,CAAU,IACrQ,SAAU,CACZ,GAAG,AACL,EAAG,EAAQ,EAET,EAAgB,CAClB,IAAI,EAAkB,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAO,CAAC,EAAG,CAC/D,MAAO,EACP,SAAU,EACV,SAAU,EACV,QAAS,IAAa,AApEb,CAAA,AAA0B,KAAK,IAA/B,EAAmC,CAC5C,SAAU,GACZ,EAAI,CAAoB,EAkES,QAAQ,CACvC,QAAS,WACP,OAAO,GAAc,EAAW,CAAA,GAClC,EACA,SAAU,KAAA,CACZ,GACA,OAAO,AAAU,IAAV,EAA2B,GAAA,KAAI,EAAC,GAAiB,CACtD,UAAW,EACX,MAAO,EACP,QAAS,EAAK,eAAe,CAC7B,SAAU,EAAe,EAAiB,EAAa,EAAM,KAAK,CACpE,GAAK,EAAe,EAAiB,EAAa,EAAM,KAAK,EAC/D,CACA,OAAO,AAAU,IAAV,EAA2B,GAAA,KAAI,EAAC,GAAiB,CACtD,UAAW,EACX,MAAO,EACP,QAAS,EAAK,eAAe,CAC7B,SAAU,CACZ,GAAK,EACP,GACA,GAAA,SAAe,EAAC,IAAI,CAAE,iBAAkB,SAAU,CAAI,SACpD,AAAI,GAAQ,AAAyB,IAAzB,EAAK,OAAO,CAAC,QAChB,EAEF,IAAI,MAAM,CAAC,GAAQ,IAAI,OAAO,CAAC,OAAQ,KAChD,GACA,IAAI,CAAC,KAAK,CAAG,EACf,GAMI,GAAmB,SAA0B,CAAQ,CAAE,CAAI,EAC7D,IAAI,EAAS,EAAK,MAAM,CACtB,EAAY,EAAK,SAAS,CACxB,EAAgB,CAAC,EAMrB,OALI,GAAY,CAAC,GAAa,CAAC,OAAQ,MAAM,CAAC,QAAQ,CAAC,GAAU,QAC/D,CAAA,EAAgB,CACd,SAAU,CACZ,CAAA,EAEK,EACT,EACI,GAAW,SAAkB,CAAK,EACpC,IAAI,EAAO,EAAM,IAAI,CACnB,EAAY,EAAM,SAAS,CAC3B,EAAmB,EAAM,gBAAgB,CACzC,EAAQ,EAAM,KAAK,CACnB,EAAW,EAAM,QAAQ,CACzB,EAAY,EAAM,SAAS,CAC3B,EAAO,EAAM,IAAI,CACjB,EAAgB,EAAM,aAAa,CACnC,EAAc,EAAM,WAAW,CAC/B,EAAoB,EAAM,YAAY,CACtC,EAAW,EAAM,QAAQ,CACzB,EAAiB,EAAM,cAAc,CACrC,EAAgB,EAAM,QAAQ,CAC5B,EAAc,GAAA,YAAU,EAAC,aAAW,EACtC,EAAO,EAAY,IAAI,CACvB,EAAc,EAAY,KAAK,CAC7B,EAAgB,GAAG,MAAM,CAAC,EAAW,eAAe,MAAM,CAAC,GAE3D,EAAqB,GAAA,QAAM,EAAC,EAAE,EAC9B,EAAsB,GAAA,oBAAkB,EAAC,MAAA,EAAmC,KAAK,EAAI,EAAK,cAAc,EAC1G,EAAuB,GAAA,SAAc,EAAC,EAAqB,GAC3D,EAAiB,CAAoB,CAAC,EAAE,CACxC,EAAoB,CAAoB,CAAC,EAAE,CACzC,EAAuB,GAAA,oBAAkB,EAAC,kBAC1C,AAAI,MAAA,GAAoC,EAAK,cAAc,CAClD,GAAwB,IAAa,EAAE,CAE1B,CAAA,IAAlB,GAGG,EAAE,CACX,EAAG,CACD,MAAO,AAAkB,CAAA,IAAlB,EAA0B,KAAA,EAAY,EAC7C,SAAU,CACZ,GACA,EAAuB,GAAA,SAAc,EAAC,EAAsB,GAC5D,EAAW,CAAoB,CAAC,EAAE,CAClC,EAAc,CAAoB,CAAC,EAAE,CACnC,EAAuB,GAAA,oBAAkB,EAAC,EAAE,CAAE,CAC9C,MAAO,EACP,SAAU,EAAW,SAAU,CAAI,EAC7B,GAAY,GACd,EAAS,GAEb,EAAI,KAAA,CACN,GACA,EAAuB,GAAA,SAAc,EAAC,EAAsB,GAC5D,EAAe,CAAoB,CAAC,EAAE,CACtC,EAAkB,CAAoB,CAAC,EAAE,CAC3C,GAAA,WAAS,EAAC,WACJ,CAAA,MAAA,IAAoC,EAAK,cAAc,AAAD,GAAK,AAAkB,CAAA,IAAlB,GAG3D,IACF,EAAY,GACZ,EAAgB,IAGpB,EAAG,CAAC,EAAc,IAAI,CAAC,KAAK,EAC5B,GAAA,WAAS,EAAC,WAEJ,GACF,CAAA,GAAW,GAAqB,CAC9B,UAAW,CACb,EAAC,EAEL,EAAG,CAAC,EAAY,EAChB,GAAA,WAAS,EAAC,WAKR,GAHI,EAAc,IAAI,CAAC,OAAS,AAAC,CAAA,GAAgB,EAAE,AAAD,EAAG,IAAI,CAAC,MACxD,EAAgB,GAEd,AAAC,GAAkB,AAAkB,CAAA,IAAlB,GAA2B,EAAc,IAAI,CAAC,OAAS,AAAC,CAAA,GAAY,EAAE,AAAD,EAAG,IAAI,CAAC,KAOzF,MAAA,GAAoC,EAAK,cAAc,EAAI,EAEpE,EAAY,GAAwB,IAEpC,EAAkB,CAAA,OAXsF,CACxG,IAAI,EAAU,EAET,CAAA,MAAA,EAAmC,KAAK,EAAI,EAAK,SAAS,AAAD,IAAO,CAAA,GACnE,CAAA,EAAU,MAAM,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,MAAM,CAAC,GAAA,UAAkB,EAAC,GAAgB,GAAA,UAAkB,EAAC,GAAY,EAAE,IAAG,EAEhH,EAAY,GACd,CAMF,EAEA,CAAC,EAAc,IAAI,CAAC,KAAK,EACzB,IAAI,EAAgB,GAAA,SAAO,EAAC,WAC1B,OAAO,GAAiB,EAAU,GACpC,EAEA,CAAC,GAAY,EAAS,IAAI,CAAC,KAAM,EAAM,MAAM,CAAE,EAAM,SAAS,CAAC,EAC3D,KD1SG,WAAY,EAAC,oBC0SoB,ED1SQ,SAAU,CAAK,EAI7D,MAAO,CAAC,GAHiB,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,CAAC,EAAG,CACnE,aAAc,IAAI,MAAM,CCwSH,EDvSvB,GACsD,ACsShB,GDtSwB,UAAU,CAC1E,GCsSE,EAAU,EAAU,OAAO,CAC3B,EAAS,EAAU,MAAM,CACvB,EAAY,GAAA,SAAO,EAAC,WACtB,OAAO,IAAI,GAAS,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,CAAC,EAAG,CAC9D,MAAO,EACP,eAAgB,EAChB,cAAe,EACf,OAAQ,CACV,IACF,EAAG,CAAC,EAAO,EAAa,EAAgB,EAAe,EAAO,EAC9D,GAAI,MAAA,GAAoC,EAAK,OAAO,CAClD,MAAoB,GAAA,KAAI,EAAC,MAAO,CAC9B,MAAO,MAAA,GAAoC,EAAK,QAAQ,CAAC,UAAY,CACnE,QAAS,EACX,EAAI,CACF,iBAAkB,EACpB,EACA,SAAuB,GAAA,KAAI,EAAC,UAAQ,CAAE,CACpC,OAAQ,CAAA,EACR,MAAO,CAAA,EACP,UAAW,CACT,KAAM,MAAA,GAAoC,EAAK,QAAQ,CAAC,UAAY,EAAI,CAC1E,CACF,EACF,GAMqB,CAAA,IAAnB,EAAM,QAAQ,EAAe,EAAM,gBAAgB,EACrD,CAAA,EAAmB,OAAO,CAAG,CAAY,EAE3C,IAAI,EAAc,EAAM,YAAY,CAAG,EAAM,YAAY,CAAC,GAAY,SACtE,AAAI,GAAe,AAAC,CAAA,MAAA,EAAiD,KAAK,EAAI,EAAY,MAAM,AAAD,EAAK,EAC3F,KAEF,EAAsB,GAAA,eAAc,EAAC,UAAI,CAAE,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAgB,CAAC,EAAG,CACpG,qCAAsC,CAAA,EACtC,IAAK,OACL,KAAM,EACN,aAAc,GACd,gBAAiB,EAAmB,OAAO,CAC3C,MAAO,EAAO,OAAS,QACvB,aAAc,EACd,MAAO,GAAA,SAAa,EAAC,CACnB,gBAAiB,cACjB,OAAQ,MACV,EAAG,GACH,UAAW,GAAA,UAAU,EAAC,EAAW,EAAQ,EAAe,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAe,eAAgB,AAAS,eAAT,GAAwB,GAAG,MAAM,CAAC,EAAe,cAAe,EAAM,SAAS,GACpN,MAAO,EAAU,eAAe,CAAC,EAAa,EAAG,GACjD,aAAc,SAAsB,CAAS,EACtC,EAAM,SAAS,EAClB,EAAY,GAEhB,CACF,EAAG,EAAM,SAAS,IACpB,EE/cI,GAAY,CAAC,QAAS,SAAS,CAa/B,GAAsC,SAAK,CAAC,IAAI,CAAC,SAAU,CAAK,EAIlE,MAAoB,GAAA,KAAI,EAAC,UAAS,CAAE,CAClC,SAAU,EAAM,QAAQ,AAC1B,GACF,GACI,GAAQ,UAAM,CAAC,KAAK,CACtB,GAAwB,UAAM,CAAC,qBAAqB,CACpD,GAAe,AAA0B,KAAK,IAA/B,GAAmC,CAChD,SAAU,EACZ,EAAI,GAQK,GAAqB,SAA4B,CAAK,EAC/D,IAAI,EAAY,UAAU,MAAM,CAAG,GAAK,AAAiB,KAAA,IAAjB,SAAS,CAAC,EAAE,CAAiB,SAAS,CAAC,EAAE,CAAG,mBAChF,EAAO,EAAM,IAAI,CACnB,EAAQ,EAAM,KAAK,CACnB,EAAS,EAAM,MAAM,CACnB,EAAiB,CAAK,CAAC,EAAU,CACrC,GAAI,AAAmB,CAAA,IAAnB,EACF,OAAO,KAET,IAAI,EAAU,GAAA,oBAAiB,EAAC,GAC5B,EAAwB,GAAA,KAAI,EAAC,KAAM,CACrC,SAAU,MAAA,EAAqC,EAAQ,gBACzD,UACA,AAAI,EAEK,EAAe,EAAS,EAAM,SAAS,CAAG,KAAO,EAAU,GAMhE,EAAM,QAAQ,CACT,KAEL,CAAA,AAAW,QAAX,GAAoB,AAAc,qBAAd,CAA+B,IACnD,EAAM,SAAS,CACG,GAAA,KAAI,EAAC,IAAK,CAC5B,SAAU,CACZ,EAAG,SAEe,GAAA,MAAK,EAAC,IAAK,CAC7B,SAAU,CAAC,EAAS,EAAS,AAC/B,EAAG,UACL,EACI,GAAY,SAAmB,CAAK,EAEtC,ICrEyB,EAAW,EAChC,EACF,EDkEE,EACA,EAAY,EAAM,SAAS,CAC7B,EAAkB,EAAM,eAAe,CACvC,EAAc,EAAM,WAAW,CAC/B,EAAmB,EAAM,gBAAgB,CACzC,EAAc,EAAM,UAAU,CAC9B,EAAQ,EAAM,KAAK,CACnB,EAAa,EAAM,UAAU,CAC7B,EAAW,EAAM,QAAQ,CACzB,EAAoB,EAAM,iBAAiB,CAC3C,EAAoB,EAAM,UAAU,CACpC,EAAa,AAAsB,KAAK,IAA3B,EAA+B,KAAO,EACnD,EAAQ,EAAM,KAAK,CACnB,EAAS,EAAM,MAAM,CACrB,EAAwB,EAAM,eAAe,CAC7C,EAAkB,AAA0B,KAAK,IAA/B,GAA2C,EAC7D,EAAQ,EAAM,KAAK,CACnB,EAAoB,EAAM,iBAAiB,CAC3C,EAAwB,EAAM,qBAAqB,CACnD,EAAY,EAAM,SAAS,CAC3B,EAAc,EAAM,WAAW,CAC/B,EAAqB,EAAM,kBAAkB,CAC7C,EAAgB,EAAM,aAAa,CACnC,EAAe,EAAM,YAAY,CACjC,EAAU,EAAM,OAAO,CACvB,EAAY,EAAM,SAAS,CAE3B,EAAS,AADO,GAAA,YAAU,EAAC,aAAW,EACjB,MAAM,CACzB,EAAoB,GAAA,SAAO,EAAC,kBAC1B,GACA,AAAW,QAAX,EAEN,EAAG,CAAC,EAAU,EAAO,EACjB,EAAgB,GAAG,MAAM,CAAC,EAAW,UAMrC,GC3GqB,ED2GS,GAAG,MAAM,CAAC,EAAe,KAAK,MAAM,CAAC,EAAe,YC1GlF,EAAU,CADsB,ED2G+D,CACjG,QAAS,EACT,wBALmB,EAMrB,GC7GmB,OAAO,CACxB,EAA0B,EAAK,uBAAuB,CACjD,GAAA,WAAY,EAAC,4BAA6B,SAAU,CAAK,EAC9D,IAAI,EAAiB,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,CAAC,EAAG,CAC/D,aAAc,IAAI,MAAM,CAAC,GACzB,wBAAyB,CAC3B,UACA,AAAK,EACE,CAAC,GAAA,SAAe,EAAC,CAAC,EAAG,MAAM,MAAM,CAAC,EAAM,gBAAgB,CAAE,WAAY,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAe,YAAY,EAAG,MAAA,EAAyC,KAAK,EAAI,EAAQ,KAAkB,CADhM,EAAE,CAEzB,IDqGI,EAAiB,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,GAAgB,EAAQ,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAe,UAAW,GAAc,GAAG,MAAM,CAAC,EAAe,cAAe,AAAW,QAAX,GAAoB,CAAC,GAAY,GAAc,GAAG,MAAM,CAAC,EAAe,cAAe,EAAM,SAAS,EAAG,GAAG,MAAM,CAAC,EAAe,YAAY,MAAM,CAAC,GAAS,GAAU,CAAC,GAAW,GAAG,MAAM,CAAC,EAAe,UAAW,AAAU,SAAV,GAAmB,GAAG,MAAM,CAAC,EAAe,QAAS,AAAW,QAAX,GAAoB,CAAC,GAAW,GAAG,MAAM,CAAC,EAAe,YAAa,CAAC,CAAC,IACjmB,EAAY,GAAmB,GAC/B,EAAW,GAAmB,EAAgB,GAC9C,EAAU,GAAA,SAAO,EAAC,WACpB,MAAO,AAAsB,CAAA,IAAtB,GAA4C,GAAA,eAAc,EAAC,GAAU,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,CAAC,EAAG,CACtH,IAAK,YACL,KAAM,GAAa,CAAC,EAAW,WAAa,SAC5C,iBAAkB,EAClB,MAAO,CACL,MAAO,MACT,EACA,UAAW,GAAG,MAAM,CAAC,EAAe,UAAU,MAAM,CAAC,GAAQ,IAAI,EACnE,IACF,EAAG,CAAC,EAAe,EAAQ,EAAmB,EAAc,EAAM,EAC9D,EAAiB,AAAC,CAAA,GAAS,EAAE,AAAD,EAAG,GAAG,CAAC,SAAU,CAAI,CAAE,CAAK,EAC1D,MAAO,CACL,UAAW,GAAG,MAAM,CAAC,EAAe,SACpC,MAAO,EACP,IAAK,CACP,EACF,GACI,EAAgB,GAAA,SAAO,EAAC,WAC1B,OAAO,EAAoB,EAAkB,EAAO,GAAW,EACjE,EAAG,CAAC,EAAmB,EAAS,EAAM,EAClC,EAAY,GAAA,SAAO,EAAC,WACtB,GAAI,CAAC,EAAa,OAAO,KACzB,IAAI,EAAQ,EAAY,KAAK,CAC3B,EAAS,EAAY,MAAM,CAC3B,EAAO,GAAA,SAAwB,EAAC,EAAa,IAC3C,EAAmB,GAAA,MAAK,EAAC,MAAO,CAClC,UAAW,GAAG,MAAM,CAAC,EAAe,mBACpC,SAAU,CAAC,MAAA,GAAoC,EAAK,GAAG,EAAI,MAAA,GAAoC,EAAK,MAAM,EAAI,EAAK,IAAI,EAAI,EAAK,QAAQ,CAAgB,GAAA,KAAI,EAAC,UAAM,CAAE,GAAA,SAAa,EAAC,CACjL,KAAM,EACR,EAAG,IAAS,KAAM,EAAY,KAAK,EAAI,CAAC,GAA0B,GAAA,KAAI,EAAC,OAAQ,CAC7E,SAAU,CACZ,GAAG,AACL,UACA,AAAI,EACK,EAAO,EAAa,EAAK,GAE3B,EACT,EAAG,CAAC,EAAa,EAAe,EAAU,EACtC,EAAa,GAAA,SAAO,EAAC,kBACvB,AAAK,EACe,GAAA,KAAI,EAAC,UAAK,CAAE,CAC9B,MAAO,SACP,KAAM,EACN,UAAW,EAAY,WAAa,aACpC,UAAW,GAAA,UAAU,EAAC,CAAC,GAAG,MAAM,CAAC,EAAe,iBAAkB,GAAa,GAAG,MAAM,CAAC,EAAe,2BAA4B,EAAO,EAC3I,SAAU,CAAC,MAAA,EAAqD,KAAK,EAAI,EAAc,GAAO,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,SAAU,CAAI,CAAE,CAAK,EAC9H,MAAoB,GAAA,KAAI,EAAC,MAAO,CAC9B,UAAW,GAAG,MAAM,CAAC,EAAe,uBAAuB,MAAM,CAAC,GAAQ,IAAI,GAC9E,SAAU,CACZ,EAAG,GACL,EACF,GAZ2B,KAa7B,EAEA,CAAC,EAAe,EAAe,EAAU,EACrC,EAAU,GAAA,SAAO,EAAC,WACpB,MAAoB,GAAA,KAAI,EAAC,qBAAkB,CAAE,CAC3C,YAAa,EAAM,SAAS,CAC5B,cAAe,EAAM,aAAa,CAClC,QAAS,EAAM,OAAO,CACtB,UAAW,EAAM,SAAS,AAC5B,GACF,EAAG,CAAC,EAAM,OAAO,CAAE,EAAM,aAAa,CAAE,EAAM,SAAS,CAAC,EACpD,EAAe,GAAA,SAAO,EAAC,WACzB,GAAI,AAA0B,CAAA,IAA1B,EAAiC,OAAO,KAC5C,IAAI,EAAmB,GAAA,KAAI,EAAC,GAAe,CACzC,SAAU,EACV,UAAW,EACX,UAAW,GAAG,MAAM,CAAC,EAAe,qBACpC,QAAS,WACP,MAAA,GAAkD,EAAY,CAAC,GACjE,CACF,UACA,AAAI,EAA8B,EAAsB,EAAW,GAC5D,EACT,EAAG,CAAC,EAAuB,EAAU,EAAiB,EAAe,EAAW,EAAY,EAGxF,EAAgB,GAAA,SAAO,EAAC,kBAC1B,AAAI,AAAC,GAAc,EACC,GAAA,MAAK,EAAC,MAAO,CAC/B,UAAW,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAe,YAAa,EAAQ,GAAa,GAAG,MAAM,CAAC,EAAe,uBAC1G,SAAU,CAAC,EAAW,EAAW,AACnC,GAJsC,KAKxC,EAAG,CAAC,EAAY,EAAW,EAAe,EAAW,EAAO,EAGxD,EAAiC,GAAA,SAAO,EAAC,WAC3C,IAAI,SAEJ,AAAI,MAAA,GAAsC,AAA+B,OAA9B,CAAA,EAAc,EAAM,IAAI,AAAD,GAAe,AAAgB,KAAK,IAArB,GAA0B,EAAY,qBAAqB,EAAI,EACvI,GAAG,MAAM,CAAC,EAAe,wBAE3B,KACT,EAAG,CAAC,EAAe,EAAW,MAAA,GAAsC,AAAgC,OAA/B,CAAA,EAAe,EAAM,IAAI,AAAD,GAAe,AAAiB,KAAK,IAAtB,EAA0B,KAAK,EAAI,EAAa,qBAAqB,CAAC,EAC9K,EAAgB,GAAqB,CAAA,MAAA,EAA2D,KAAK,EAAI,EAAiB,EAAK,EAC/H,GAA4B,GAAA,MAAK,EAAC,UAAS,CAAE,CAC/C,SAAU,CAAC,GAA0B,GAAA,MAAK,EAAC,MAAO,CAChD,UAAW,GAAA,UAAU,EAAC,CAAC,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAe,SAAU,EAAQ,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAe,mBAAoB,IAAY,EAC1J,QAAS,EAAoB,EAAoB,KAAA,EACjD,GAAI,OACJ,MAAO,EACP,SAAU,CAAC,EAAW,EAAQ,AAChC,GAAI,GAAyB,GAAA,KAAI,EAAC,MAAO,CACvC,UAAW,GAAA,UAAU,EAAC,CAAC,GAAG,MAAM,CAAC,EAAe,UAAW,CAAC,GAAa,GAAG,MAAM,CAAC,EAAe,kBAAmB,EAAO,EAC5H,SAAU,CACZ,GAAiB,GAAA,KAAI,EAAC,MAAO,CAC3B,MAAO,CACL,KAAM,EACN,UAAW,OACX,UAAW,QACb,EACA,SAAU,CACZ,GAAiB,GAAA,MAAK,EAAC,GAAa,QAAQ,CAAE,CAC5C,MAAO,CAAC,EACR,SAAU,CAAC,EAAqB,GAAA,KAAI,EAAC,MAAO,CAC1C,UAAW,GAAG,MAAM,CAAC,EAAe,WAAW,MAAM,CAAC,GAAQ,IAAI,GAClE,SAAuB,GAAA,KAAI,EAAC,UAAI,CAAE,CAChC,aAAc,GACd,UAAW,GAAG,MAAM,CAAC,EAAe,eAAe,MAAM,CAAC,GAAQ,IAAI,GACtE,aAAc,EAAE,CAChB,SAAU,EAAE,CACZ,MAAO,EACP,KAAM,SACN,MAAO,CACT,EACF,GAAK,KAAM,GAAkC,GAAA,MAAK,EAAC,UAAS,CAAE,CAC5D,SAAU,CAAC,EAAe,CAAC,GAAc,EAAkC,GAAA,KAAI,EAAC,MAAO,CACrF,UAAW,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAe,YAAa,EAAQ,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAe,sBAAuB,IACxI,SAAU,MAAA,EAA+D,KAAK,EAAI,EAAmB,EACvG,GAAK,KAAK,AACZ,GAAI,GAA8B,GAAA,KAAI,EAAC,MAAO,CAC5C,UAAW,GAAA,UAAU,EAAC,CAAC,GAAG,MAAM,CAAC,EAAe,WAAY,EAAQ,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAe,qBAAsB,GAAW,EAClJ,SAAU,CACZ,GAAG,AACL,GAAG,AACL,GACA,OAAO,EAAiB,OAAO,CAAe,GAAA,MAAK,EAAC,UAAS,CAAE,CAC7D,SAAU,CAAC,GAAe,CAAC,GAAY,CAAC,GAA+C,GAAA,KAAI,EAAC,MAAO,CACjG,MAAO,GAAA,SAAa,EAAC,CACnB,MAAO,EAvJQ,GAuJqB,EACpC,SAAU,SACV,KAAM,OAAO,MAAM,CAAC,EAzJL,GAyJkC,EAAY,MAC7D,SAAU,EA1JK,GA0JwB,EACvC,SAAU,EA3JK,GA2JwB,EACvC,WAAY,kBACd,EAAG,EACL,GAAiB,GAAA,MAAK,EAAC,GAAO,CAC5B,YAAa,CAAA,EACb,QAAS,KACT,UAAW,EACX,WAAY,AAAe,CAAA,IAAf,EAAuB,KAAA,EAAY,EAC/C,WAAY,SAAoB,CAAQ,EAClC,GACJ,MAAA,GAAkD,EAAY,GAChE,EACA,eAvKiB,GAwKjB,MAAO,EACP,MAAO,EACP,MAAO,EACP,UAAW,GAAA,UAAU,EAAC,EAAgB,EAAQ,GAC9C,SAAU,CAAC,EAA8C,GAAA,KAAI,EAAC,MAAO,CACnE,UAAW,GAAG,MAAM,CAAC,EAAe,yBAAyB,MAAM,CAAC,GAAQ,IAAI,GAChF,MAAO,CACL,OAAQ,OACR,MAAO,OACP,QAAS,EAAiC,EAAI,CAChD,EACA,SAAU,EACZ,GAAK,GAAc,EAAa,AAClC,GAAG,AACL,IACF,EEhSI,GAAiB,SAAwB,CAAU,EACrD,IAAI,EAAM,GAAA,QAAM,EAAC,MAEjB,OADA,EAAI,OAAO,CAAG,EACP,GAAA,aAAW,EAAC,WAEjB,IAAK,IADD,EACK,EAAO,UAAU,MAAM,CAAE,EAAO,AAAI,MAAM,GAAO,EAAO,EAAG,EAAO,EAAM,IAC/E,CAAI,CAAC,EAAK,CAAG,SAAS,CAAC,EAAK,CAE9B,OAAO,AAAiC,OAAhC,CAAA,EAAe,EAAI,OAAO,AAAD,GAAe,AAAiB,KAAK,IAAtB,EAA0B,KAAK,EAAI,EAAa,IAAI,CAAC,KAAK,CAAC,EAAc,CAAC,EAAI,CAAC,MAAM,CAAC,GAAA,UAAkB,EAAC,KAC3J,EAAG,EAAE,EACP,8BCTI,GAAuB,SAA8B,CAAK,EAC5D,IAAI,EAAe,EAAgB,EAAgB,EAAgB,EACnE,MAAO,GAAA,SAAe,EAAC,CAAC,EAAG,EAAM,YAAY,CAAE,CAC7C,mBAAoB,CAClB,QAAS,OACT,OAAQ,OACR,WAAY,SACZ,SAAU,CACR,QAAS,cACT,WAAY,SACZ,eAAgB,SAChB,aAAc,EACd,cAAe,EACf,MAAO,AAAmC,OAAlC,CAAA,EAAgB,EAAM,MAAM,AAAD,GAAe,AAAkB,KAAK,IAAvB,GAA4B,AAA2C,OAA1C,CAAA,EAAgB,EAAc,MAAM,AAAD,GAAe,AAAkB,KAAK,IAAvB,EAA2B,KAAK,EAAI,EAAc,yBAAyB,CAC5M,SAAU,OACV,OAAQ,UACR,aAAc,EAAM,YAAY,CAChC,MAAO,CACL,cAAe,EACf,aAAc,EACd,aAAc,EAAM,YAAY,CAChC,UAAW,CACT,gBAAiB,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,4BAA4B,AACjO,CACF,CACF,EACA,WAAY,CACV,QAAS,cACT,WAAY,SACZ,eAAgB,SAChB,mBAAoB,EAAM,OAAO,CACjC,iBAAkB,EAAM,OAAO,CAC/B,OAAQ,UACR,MAAO,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,yBAAyB,CAClN,QAAS,CACP,OAAQ,OACR,MAAO,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,yBAAyB,CAClN,cAAe,EACf,aAAc,EACd,OAAQ,UACR,QAAS,OACT,WAAY,SACZ,WAAY,OACZ,aAAc,EAAM,YAAY,CAChC,UAAW,CACT,gBAAiB,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,4BAA4B,AACjO,CACF,CACF,CACF,CACF,GACF,EChDI,GAAY,CAAC,qBAAsB,cAAe,gBAAiB,sBAAsB,CAC3F,GAAa,CAAC,QAAS,SAAS,CAevB,GAAiB,SAAwB,CAAI,EACtD,IAqEM,EClFF,EACA,EACA,EAMA,EDKA,EAAqB,EAAK,kBAAkB,CAC9C,EAAc,EAAK,WAAW,CAC9B,EAAgB,EAAK,aAAa,CAElC,GADsB,EAAK,mBAAmB,CACtC,GAAA,SAAwB,EAAC,EAAM,KAEvC,EAAe,AADC,GAAA,YAAU,EAAC,UAAc,CAAC,aAAa,EAC5B,YAAY,CACrC,EAAY,GAAG,MAAM,CAAC,IAAgB,sBACtC,KDyBG,WAAY,EAAC,wBAAyB,SAAU,CAAK,EAI1D,MAAO,CAAC,GAHO,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,CAAC,EAAG,CACzD,aAAc,IAAI,MAAM,CC3BH,ED4BvB,IACuC,CACzC,GC7BE,EAAU,EAAU,OAAO,CAC3B,EAAS,EAAU,MAAM,CACvB,EAAY,GAAA,UAAQ,EAAC,QACvB,EAAa,GAAA,SAAc,EAAC,EAAW,GACvC,EAAY,CAAU,CAAC,EAAE,CACzB,EAAe,CAAU,CAAC,EAAE,CAC1B,EAAY,GAAA,SAAO,EAAC,WACtB,GAAI,CAAC,EAAa,OAAO,KACzB,IAAI,EAAQ,EAAY,KAAK,CAC3B,EAAS,EAAY,MAAM,CAC3B,EAAO,GAAA,SAAwB,EAAC,EAAa,IAC3C,EAAU,CAAC,MAAA,GAAoC,EAAK,GAAG,EAAI,MAAA,GAAoC,EAAK,MAAM,EAAI,EAAK,IAAI,EAAI,EAAK,QAAQ,CAAgB,GAAA,eAAc,EAAC,UAAM,CAAE,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAO,CAAC,EAAG,CAC5N,KAAM,GACN,IAAK,QACP,IAAM,KAAM,EAAqB,GAAA,KAAI,EAAC,OAAQ,CAC5C,MAAO,CACL,kBAAmB,CACrB,EACA,SAAU,CACZ,EAAG,QAAU,KAAA,EAAU,QACvB,AAAI,EACK,EAAO,EAA0B,GAAA,KAAI,EAAC,MAAO,CAClD,SAAU,CACZ,GAAI,GAEc,GAAA,KAAI,EAAC,MAAO,CAC9B,SAAU,CACZ,GACF,EAAG,CAAC,EAAY,EACZ,EAAqB,GAAiB,EAAY,SAAU,CAAU,EACxE,IAAI,EAAO,GAAkB,CAAA,MAAA,EAAqD,KAAK,EAAI,EAAc,EAAU,SACnH,AAAI,AAAC,GAAS,EACT,MAAM,OAAO,CAAC,GAOZ,EAAsB,GAAA,MAAK,EAAC,MAAO,CACxC,UAAW,GAAG,MAAM,CAAC,EAAW,oBAAoB,MAAM,CAAC,GAAQ,IAAI,GACvE,SAAU,CAAC,EAAK,MAAM,CAAC,SAAS,GAAG,CAAC,SAAU,CAAG,CAAE,CAAK,EACtD,IAGM,EAHF,EAAY,CAAA,EAMhB,OAJkB,SAAK,CAAC,cAAc,CAAC,IAErC,CAAA,EAAY,CAAC,CAAE,CAAA,MAAA,GAAkC,AAA6B,OAA5B,CAAA,EAAa,EAAI,KAAK,AAAD,GAAe,AAAe,KAAK,IAApB,GAAyB,CAAU,CAAC,cAAc,AAAD,CAAC,EAEtH,GAAA,KAAI,EAAC,MAAO,CAC9B,UAAW,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAW,yBAAyB,MAAM,CAAC,GAAS,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAW,yBAA0B,CAAC,IACxJ,SAAU,CACZ,EAAG,GACL,GAAI,GAA0B,GAAA,KAAI,EAAC,OAAQ,CACzC,UAAW,GAAG,MAAM,CAAC,EAAW,2BAA2B,MAAM,CAAC,GAAQ,IAAI,GAC9E,SAAU,CACZ,GAAG,AACL,IAxBiC,EAAsB,GAAA,MAAK,EAAC,MAAO,CAClE,UAAW,GAAG,MAAM,CAAC,EAAW,oBAAoB,MAAM,CAAC,GAAQ,IAAI,GACvE,SAAU,CAAC,EAAM,GAA0B,GAAA,KAAI,EAAC,OAAQ,CACtD,UAAW,GAAG,MAAM,CAAC,EAAW,2BAA2B,MAAM,CAAC,GAAQ,IAAI,GAC9E,SAAU,CACZ,GAAG,AACL,IAPgC,KA0BlC,EAAI,KAAA,EAEA,GACE,EAAQ,GAAA,SAAiB,EAAe,GAAA,SAAmB,IAAG,IAAI,CAAC,SAAS,EAAQ,CAAK,EAC3F,MAAO,GAAA,SAAmB,IAAG,IAAI,CAAC,SAAkB,CAAQ,EAC1D,OAAU,OAAQ,EAAS,IAAI,CAAG,EAAS,IAAI,EAC7C,KAAK,EACH,EAAa,GACf,KAAK,EACL,IAAK,MACH,OAAO,EAAS,IAAI,GACxB,CACF,EAAG,GACL,IC5FE,EAAW,GD6FN,SAAU,CAAE,EACjB,OAAO,EAAM,KAAK,CAAC,IAAI,CAAE,WAC3B,GC9FE,EAAQ,GAAA,QAAM,IACd,EAAS,GAAA,aAAW,EAAC,WACnB,EAAM,OAAO,GACf,aAAa,EAAM,OAAO,EAC1B,EAAM,OAAO,CAAG,MAEpB,EAAG,EAAE,EACD,EAAM,GAAA,aAAW,EAAe,GAAA,SAAiB,EAAe,GAAA,SAAmB,IAAG,IAAI,CAAC,SAAS,IACtG,IAAI,EACF,EACA,EACA,EAAS,UACX,MAAO,GAAA,SAAmB,IAAG,IAAI,CAAC,SAAmB,CAAS,EAC5D,OAAU,OAAQ,EAAU,IAAI,CAAG,EAAU,IAAI,EAC/C,KAAK,EACH,IAAK,AAAsB,EAAO,AAAI,MAAjC,EAAO,EAAO,MAAM,EAA0B,EAAO,EAAG,EAAO,EAAM,IACxE,CAAI,CAAC,EAAK,CAAG,CAAM,CAAC,EAAK,CAGzB,EAAU,IAAI,CAAG,EACjB,MAGJ,KAAK,EAEH,OADA,IACO,EAAU,MAAM,CAAC,SAAU,IAAI,QAAQ,SAAU,CAAO,EAC7D,EAAM,OAAO,CAAG,WAAyB,GAAA,SAAiB,EAAe,GAAA,SAAmB,IAAG,IAAI,CAAC,SAAS,IAC3G,MAAO,GAAA,SAAmB,IAAG,IAAI,CAAC,SAAkB,CAAQ,EAC1D,OAAU,OAAQ,EAAS,IAAI,CAAG,EAAS,IAAI,EAC7C,KAAK,EAGH,OAFA,EAAS,EAAE,CAAG,EACd,EAAS,IAAI,CAAG,EACT,EAAS,KAAK,CAAC,KAAK,EAAG,GAChC,KAAK,EAGH,OAFA,EAAS,EAAE,CAAG,EAAS,IAAI,CAC3B,AAAC,CAAA,EAAG,EAAS,EAAE,AAAD,EAAG,EAAS,EAAE,EACrB,EAAS,MAAM,CAAC,UACzB,KAAK,EACL,IAAK,MACH,OAAO,EAAS,IAAI,GACxB,CACF,EAAG,GACL,IDqDL,KCpDG,IACF,KAAK,EACL,IAAK,MACH,OAAO,EAAU,IAAI,GACzB,CACF,EAAG,GACL,IAAK,CAAC,EAAU,ED8CX,IC9CwB,EAC7B,GAAA,WAAS,EAAC,WACR,OAAO,EACT,EAAG,CAAC,EAAO,EACJ,CACL,IAAK,EACL,OAAQ,CACV,GDwCI,EAAgB,GAAsB,EAC1C,MAAoB,GAAA,KAAI,EAAC,MAAO,CAC9B,UAAW,GAAG,MAAM,CAAC,EAAW,mBAAmB,MAAM,CAAC,GAAQ,IAAI,GACtE,MAAO,CACL,SAAU,EACV,OAAQ,MACV,EACA,SAAuB,GAAA,KAAI,EAAC,MAAO,CACjC,MAAO,CACL,OAAQ,MACV,EACA,SAAuB,GAAA,KAAI,EAAC,UAAc,CAAE,CAC1C,SAAU,SAAkB,CAAK,EAC/B,IAAI,EAAQ,EAAM,KAAK,CACvB,EAAuB,GAAG,CAAC,GAC7B,EACA,SAAU,EAA6B,GAAA,KAAI,EAAC,MAAO,CACjD,MAAO,CACL,QAAS,OACT,WAAY,SACZ,OAAQ,OACR,eAAgB,UAClB,EACA,SAAU,EAAc,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,CAAC,EAAG,CAGlE,iBAAkB,CACpB,GACF,GAAK,IACP,EACF,EACF,GACF,EExII,GAAuB,SAA8B,CAAK,EAC5D,IAAI,EAAe,EACnB,MAAO,GAAA,SAAe,EAAC,CAAC,EAAG,EAAM,YAAY,CAAE,CAC7C,SAAU,WACV,MAAO,OACP,OAAQ,OACR,gBAAiB,cACjB,WAAY,CACV,MAAO,SACT,EACA,SAAU,CACR,QAAS,OACT,OAAQ,OACR,mBAAoB,OACpB,SAAU,GAAA,SAAe,EAAC,CACxB,QAAS,OACT,WAAY,QACd,EAAG,GAAG,MAAM,CAAC,EAAM,gBAAgB,CAAE,qBAAsB,CACzD,gBAAiB,GACjB,kBAAmB,EACrB,EACF,EACA,SAAU,CACR,SAAU,KACV,OAAQ,QACV,EACA,SAAU,CACR,SAAU,WACV,QAAS,OACT,OAAQ,OACR,WAAY,SACZ,SAAU,SACV,kBAAmB,CACjB,QAAS,OACT,WAAY,SACZ,UAAW,OACX,SAAU,MACZ,EACA,wBAAyB,CACvB,QAAS,eACT,OAAQ,OACR,cAAe,QACjB,EACA,uBAAwB,CACtB,QAAS,eACT,YAAa,EACb,aAAc,EACd,WAAY,OACZ,kBAAmB,EACnB,WAAY,MACZ,SAAU,OACV,MAAO,AAAmC,OAAlC,CAAA,EAAgB,EAAM,MAAM,AAAD,GAAe,AAAkB,KAAK,IAAvB,GAA4B,AAA2C,OAA1C,CAAA,EAAgB,EAAc,MAAM,AAAD,GAAe,AAAkB,KAAK,IAAvB,EAA2B,KAAK,EAAI,EAAc,gBAAgB,CACnM,cAAe,KACjB,CACF,EACA,SAAU,CACR,SAAU,EACV,QAAS,OACT,WAAY,SACZ,cAAe,EACf,aAAc,EACd,WAAY,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,AAAC,CAAA,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,kBAAkB,AAAD,GAAM,EAAC,EAAK,GAAI,IAAK,KAC3P,CACF,GACF,ECrDI,GAAe,SAAsB,CAAK,EAE5C,IADI,EAAiB,EAAiB,EAAiB,EAAiB,EAAiB,EAAiB,EACtG,EAAM,GAAA,QAAM,EAAC,MACb,EAAoB,EAAM,iBAAiB,CAC7C,EAAe,EAAM,YAAY,CACjC,EAAqB,EAAM,kBAAkB,CAC7C,EAAiB,EAAM,SAAS,CAChC,EAAQ,EAAM,KAAK,CACnB,EAAsB,EAAM,mBAAmB,CAC/C,EAAS,EAAM,MAAM,CACrB,EAAgB,EAAM,aAAa,CAEnC,EAAe,AADC,GAAA,YAAU,EAAC,UAAc,CAAC,aAAa,EAC5B,YAAY,CAEvC,EAAO,AADU,GAAA,YAAU,EAAC,aAAW,EACnB,IAAI,CACtB,EAAY,GAAG,MAAM,CAAC,EAAM,SAAS,EAAI,EAAa,OAAQ,mBAC9D,KDuCG,WAAY,EAAC,wBAAyB,SAAU,CAAK,EAI1D,MAAO,CAAC,GAHgB,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,CAAC,EAAG,CAClE,aAAc,IAAI,MAAM,CCzCH,ED0CvB,IACgD,CAClD,GC3CE,EAAU,EAAU,OAAO,CAC3B,EAAS,EAAU,MAAM,CACvB,EAAY,KAAA,EACZ,AAA2B,KAAA,IAA3B,EAAM,gBAAgB,CACxB,EAAY,mBACH,CAAA,AAAW,QAAX,GAAoB,AAAW,QAAX,CAAe,GAC5C,CAAA,EAAY,mBAAkB,EAEhC,IAAI,EAAY,GAAmB,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,CAAC,EAAG,CAC7E,UAAW,CAAA,CACb,GAAI,GAEF,EAAQ,AADS,GAAA,YAAU,EAAC,aAAW,EAClB,KAAK,CACxB,EAAa,GAAA,SAAO,EAAC,WAEvB,IADI,EAAe,EAAgB,EAAgB,EAAgB,EAAgB,EAAgB,EAAgB,EAAgB,EAAgB,EAAiB,EAAiB,EAAiB,EAClM,EAA0B,GAAA,KAAI,EAAC,UAAc,CAC/C,CACA,MAAO,CACL,OAAQ,GAAA,gBAAc,IACtB,WAAY,CACV,OAAQ,CACN,SAAU,cACV,OAAQ,aACV,EACA,KAAM,GAAA,SAAa,EAAC,CAAC,EAAG,GAAA,iBAAe,EAAC,CACtC,YAAa,AAAC,CAAA,AAAmC,OAAlC,CAAA,EAAgB,EAAM,MAAM,AAAD,GAAe,AAAkB,KAAK,IAAvB,GAA4B,AAA2C,OAA1C,CAAA,EAAgB,EAAc,MAAM,AAAD,GAAe,AAAkB,KAAK,IAAvB,EAA2B,KAAK,EAAI,EAAc,aAAa,AAAD,GAAM,cAC5M,eAAgB,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,aAAa,AAAD,GAAM,cACrN,WAAY,EAAM,YAAY,CAC9B,oBAAqB,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,uBAAuB,AAAD,GAAO,CAAA,MAAA,EAAqC,KAAK,EAAI,EAAM,gBAAgB,AAAD,EACxS,YAAa,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,oBAAoB,AAAD,GAAO,CAAA,MAAA,EAAqC,KAAK,EAAI,EAAM,gBAAgB,AAAD,EAC7R,8BAA+B,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,uBAAuB,AAAD,GAAO,CAAA,MAAA,EAAqC,KAAK,EAAI,EAAM,gBAAgB,AAAD,EAClT,oBAAqB,EACrB,qBAAsB,EACtB,yBAA0B,EAC1B,cAAe,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,aAAa,AAAD,GAAO,CAAA,MAAA,EAAqC,KAAK,EAAI,EAAM,kBAAkB,AAAD,EAC1R,6BAA8B,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,mBAAmB,AAAD,GAAO,CAAA,MAAA,EAAqC,KAAK,EAAI,EAAM,SAAS,AAAD,EACtS,gCAAiC,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,qBAAqB,AAAD,GAAO,CAAA,MAAA,EAAqC,KAAK,EAAI,EAAM,aAAa,AAAD,EAC/S,2BAA4B,EAC5B,mBAAoB,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,mBAAmB,AAAD,GAAM,sBAC/N,sBAAuB,AAAC,CAAA,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,oBAAoB,AAAD,GAAM,sBACzO,sBAAuB,AAAC,CAAA,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,qBAAqB,AAAD,GAAM,mBAC1O,QAAS,MAAA,EAAqC,KAAK,EAAI,EAAM,eAAe,CAC5E,cAAe,MAAA,EAAqC,KAAK,EAAI,EAAM,eAAe,CAClF,kBAAmB,cACnB,YAAa,MAAA,EAAqC,KAAK,EAAI,EAAM,eAAe,AAClF,GACF,EACA,MAAO,CACL,gBAAiB,AAAC,CAAA,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,aAAa,AAAD,GAAM,aAC9N,CACF,EACA,SAAuB,GAAA,KAAI,EAAC,GAAU,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAC9E,MAAO,EAAO,OAAS,OACzB,EAAG,GAAQ,CAAC,EAAG,CACb,UAAW,GAAG,MAAM,CAAC,EAAW,eAAe,MAAM,CAAC,GAAQ,IAAI,EACpE,EAAG,EAAM,SAAS,EAAG,CAAC,EAAG,CACvB,MAAO,GAAA,SAAa,EAAC,CACnB,MAAO,MACT,EAAG,AAAyC,OAAxC,CAAA,EAAmB,EAAM,SAAS,AAAD,GAAe,AAAqB,KAAK,IAA1B,EAA8B,KAAK,EAAI,EAAiB,KAAK,EACjH,UAAW,CAAA,EACX,eAAgB,SAChB,KAAM,YACR,GACF,UACA,AAAI,EACK,EAAoB,EAAO,GAE7B,EACT,EAAG,CAAC,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,aAAa,CAAE,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,uBAAuB,CAAE,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,oBAAoB,CAAE,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,aAAa,CAAE,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,mBAAmB,CAAE,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,qBAAqB,CAAE,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,mBAAmB,CAAE,EAAM,YAAY,CAAE,MAAA,EAAqC,KAAK,EAAI,EAAM,gBAAgB,CAAE,MAAA,EAAqC,KAAK,EAAI,EAAM,kBAAkB,CAAE,MAAA,EAAqC,KAAK,EAAI,EAAM,SAAS,CAAE,MAAA,EAAqC,KAAK,EAAI,EAAM,aAAa,CAAE,EAAM,eAAe,CAAE,EAAM,EAAO,EAAW,EAAQ,EAAoB,EACzwD,OAAO,EAAsB,GAAA,KAAI,EAAC,MAAO,CACvC,UAAW,GAAA,UAAU,EAAC,EAAW,EAAQ,EAAgB,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAW,UAAW,CAAA,IAC7G,MAAO,EACP,SAAuB,GAAA,MAAK,EAAC,MAAO,CAClC,IAAK,EACL,UAAW,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAW,SAAU,EAAQ,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAW,SAAU,AAAiB,UAAjB,GAA4B,AAAW,QAAX,IAC5I,SAAU,CAAC,GAA0B,GAAA,MAAK,EAAC,MAAO,CAChD,UAAW,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAW,eAAe,MAAM,CAAC,IACjE,QAAS,EACT,SAAU,CAAc,GAAA,KAAI,EAAC,qBAAkB,CAAE,GAAA,SAAa,EAAC,CAAC,EAAG,IAAsB,GAAA,KAAI,EAAC,MAAO,CACnG,UAAW,GAAG,MAAM,CAAC,EAAW,UAAU,MAAM,CAAC,GAAQ,IAAI,GAC7D,GAAI,OACJ,SAAU,CACZ,EAAG,QAAQ,AACb,GAAiB,GAAA,KAAI,EAAC,MAAO,CAC3B,MAAO,CACL,KAAM,CACR,EACA,UAAW,GAAG,MAAM,CAAC,EAAW,UAAU,MAAM,CAAC,GAAQ,IAAI,GAC7D,SAAU,CACZ,GAAI,AAAC,CAAA,GAAsB,GAAiB,EAAM,WAAW,AAAD,GAAmB,GAAA,KAAI,EAAC,GAAgB,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAC9H,mBAAoB,CACtB,EAAG,GAAQ,CAAC,EAAG,CACb,UAAW,CACb,IAAI,AACN,EACF,IACF,EC5HI,GAAuB,SAA8B,CAAK,EAC5D,IAAI,EAAe,EAAgB,EACnC,MAAO,GAAA,SAAe,EAAC,CAAC,EAAG,EAAM,YAAY,CAAE,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAC7G,SAAU,WACV,WAAY,cACZ,QAAS,OACT,WAAY,SACZ,YAAa,EACb,aAAc,GACd,OAAQ,AAAC,CAAA,AAAmC,OAAlC,CAAA,EAAgB,EAAM,MAAM,AAAD,GAAe,AAAkB,KAAK,IAAvB,GAA4B,AAA2C,OAA1C,CAAA,EAAgB,EAAc,MAAM,AAAD,GAAe,AAAkB,KAAK,IAAvB,EAA2B,KAAK,EAAI,EAAc,kBAAkB,AAAD,GAAM,GAC5M,UAAW,aACX,MAAO,CACL,OAAQ,MACV,CACF,EAAG,GAAG,MAAM,CAAC,EAAM,gBAAgB,CAAE,qBAAsB,CACzD,gBAAiB,EACnB,GAAI,qBAAsB,CACxB,UAAW,OACX,MAAO,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,gBAAgB,CACzM,SAAU,OACV,gBAAiB,MACnB,GAAI,SAAU,CACZ,SAAU,WACV,gBAAiB,OACjB,EAAG,CACD,QAAS,OACT,WAAY,SACZ,OAAQ,OACR,UAAW,OACX,SAAU,MACZ,EACA,IAAK,CACH,OAAQ,MACV,EACA,GAAI,CACF,OAAQ,OACR,YAAa,EACb,aAAc,EACd,kBAAmB,EACnB,WAAY,MACZ,MAAO,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,gBAAgB,AAAD,GAAM,EAAM,gBAAgB,CACrO,SAAU,OACV,WAAY,MACd,EACA,QAAS,CACP,QAAS,OACT,WAAY,QACd,CACF,GAAI,gBAAiB,CACnB,SAAU,OACV,gBAAiB,CACnB,IACF,EC/BI,GAAe,SAAsB,CAAK,EAC5C,IAAI,EAAW,EAAM,QAAQ,CAC3B,EAAO,EAAM,IAAI,CACjB,EAAY,EAAM,SAAS,CAC3B,EAAa,EAAM,UAAU,CAC7B,EAAqB,EAAM,kBAAkB,CAC7C,EAAmB,EAAM,gBAAgB,CACzC,EAAoB,EAAM,iBAAiB,CAC3C,EAAgB,EAAM,SAAS,CAC/B,EAAQ,EAAM,KAAK,CACnB,EAAS,EAAM,MAAM,CACrB,EAAW,EAAM,QAAQ,CACzB,EAAa,EAAM,UAAU,CAC7B,EAAW,EAAM,QAAQ,CACzB,EAAY,EAAM,SAAS,CACzB,EAAc,GAAA,YAAU,EAAC,UAAc,CAAC,aAAa,EACvD,EAAe,EAAY,YAAY,CACvC,EAAY,EAAY,SAAS,CAC/B,EAAgB,GAAG,MAAM,CAAC,GAAa,EAAa,OAAQ,kBAC5D,KDcG,WAAY,EAAC,wBAAyB,SAAU,CAAK,EAI1D,MAAO,CAAC,GAHgB,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,CAAC,EAAG,CAClE,aAAc,IAAI,MAAM,CChBH,EDiBvB,IACgD,CAClD,GClBE,EAAU,EAAU,OAAO,CAC3B,EAAS,EAAU,MAAM,CACvB,EAAY,GAAA,UAAU,EAAC,EAAe,EAAe,GACzD,GAAI,AAAW,QAAX,GAAoB,CAAC,GAAY,EAAY,CAO/C,IAAI,EAAgB,GANK,AAAC,CAAA,GAAY,EAAE,AAAD,EAAG,GAAG,CAAC,SAAU,CAAI,EAC1D,MAAO,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAO,CAAC,EAAG,CAChD,SAAU,KAAA,EACV,OAAQ,KAAA,CACV,GACF,IAEA,MAAoB,GAAA,KAAI,EAAC,GAAc,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CACjE,KAAM,YACR,EAAG,GAAQ,CAAC,EAAG,CACb,WAAY,CAAA,EACZ,SAAU,CACZ,IACF,CACA,IAAI,EAAiB,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAe,SAAU,EAAQ,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAe,aAAc,AAAc,QAAd,GAAsB,GAAG,MAAM,CAAC,EAAe,aAAc,AAAW,QAAX,GAAmB,GAAG,MAAM,CAAC,EAAe,gBAAiB,IAC5R,EAAuB,GAAA,KAAI,EAAC,OAAQ,CACtC,UAAW,EACX,SAAuB,GAAA,KAAI,EAAC,IAAK,CAC/B,SAAU,GAAA,oBAAiB,EAAC,EAC9B,EACF,EAAG,QACH,OAAO,EAAsB,GAAA,MAAK,EAAC,MAAO,CACxC,UAAW,EACX,MAAO,GAAA,SAAa,EAAC,CAAC,EAAG,GACzB,SAAU,CAAC,GAAyB,GAAA,KAAI,EAAC,OAAQ,CAC/C,UAAW,GAAG,MAAM,CAAC,EAAe,sBAAsB,MAAM,CAAC,GAAQ,IAAI,GAC7E,QAAS,WACP,MAAA,GAAgD,EAAW,CAAC,GAC9D,EACA,SAAuB,GAAA,KAAI,EAAC,UAAY,CAAE,CAAC,EAC7C,GAAI,IA9DF,AAAqB,CAAA,IA8DI,EA7DpB,KA6DoB,EA1DpB,AA0DoB,EAAkB,EA1DZ,MA0DY,GAAU,AAAW,QAAX,GAAoB,CAAC,GAAyB,GAAA,MAAK,EAAC,UAAS,CAAE,CACpH,SAAU,CAAc,GAAA,KAAI,EAAC,qBAAkB,CAAE,GAAA,SAAa,EAAC,CAAC,EAAG,IAAsB,GAAA,KAAI,EAAC,MAAO,CACnG,UAAW,EACX,QAAS,EACT,SAAU,GAAmB,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,CAAC,EAAG,CACvE,UAAW,CAAA,CACb,GAAI,oBACN,GAAG,AACL,GAAiB,GAAA,KAAI,EAAC,MAAO,CAC3B,MAAO,CACL,KAAM,CACR,EACA,SAAU,CACZ,GAAI,AAAC,CAAA,GAAsB,EAAM,aAAa,EAAI,EAAM,WAAW,AAAD,GAAmB,GAAA,KAAI,EAAC,GAAgB,GAAA,SAAa,EAAC,CACtH,mBAAoB,CACtB,EAAG,IAAQ,AACb,IACF,EC5FI,GAA0B,SAAiC,CAAK,EAClE,IAAI,EAAe,EAAgB,EAAgB,EACnD,MAAO,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,gBAAgB,CAAE,WAAY,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,kBAAkB,MAAM,CAAC,EAAM,YAAY,EAAG,CACjK,OAAQ,AAAC,CAAA,AAAmC,OAAlC,CAAA,EAAgB,EAAM,MAAM,AAAD,GAAe,AAAkB,KAAK,IAAvB,GAA4B,AAA2C,OAA1C,CAAA,EAAgB,EAAc,MAAM,AAAD,GAAe,AAAkB,KAAK,IAAvB,EAA2B,KAAK,EAAI,EAAc,kBAAkB,AAAD,GAAM,GAC5M,WAAY,GAAG,MAAM,CAAC,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,kBAAkB,AAAD,GAAM,GAAI,MAEpO,OAAQ,GACR,MAAO,OACP,aAAc,EACd,cAAe,EACf,eAAgB,aAAa,MAAM,CAAC,EAAM,UAAU,EACpD,gBAAiB,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,aAAa,AAAD,GAAM,2BACtN,qBAAsB,YACtB,eAAgB,YAChB,WAAY,6DACZ,iBAAkB,CAChB,SAAU,QACV,gBAAiB,EACjB,MAAO,OACP,OAAQ,IACR,eAAgB,CAClB,EACA,wBAAyB,CACvB,gBAAiB,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,mBAAmB,AAAD,GAAM,0BAC9N,EACA,mBAAoB,CAClB,QAAS,OACT,WAAY,SACZ,SAAU,KACV,OAAQ,UACR,WAAY,CACV,aAAc,EACd,cAAe,EACf,UAAW,CACT,MAAO,EAAM,SAAS,AACxB,CACF,CACF,EACA,oBAAqB,CACnB,UAAW,gCACb,EACA,iCAAkC,CAChC,WAAY,iDACd,CACF,IACF,ECjCI,GAAS,UAAM,CAAC,MAAM,CACtB,GAAgB,SAAuB,CAAK,EAE9C,ICfyB,EAAW,EAChC,EACF,EDYE,EAAgB,EAAgB,EAChC,EAAW,EAAM,QAAQ,CAC3B,EAAc,EAAM,WAAW,CAC/B,EAAiB,EAAM,SAAS,CAChC,EAAQ,EAAM,KAAK,CACnB,EAAY,EAAM,SAAS,CAC3B,EAAY,EAAM,SAAS,CAC3B,EAAa,EAAM,UAAU,CAC7B,EAAS,EAAM,MAAM,CACrB,EAAe,EAAM,YAAY,CACjC,EAAsB,EAAM,mBAAmB,CAE/C,EAAQ,AADQ,GAAA,YAAU,EAAC,aAAW,EAClB,KAAK,CACvB,EAAU,GAAA,YAAU,EAAC,UAAc,CAAC,aAAa,EACjD,EAAY,GAAA,UAAQ,EAAC,CAAA,GACvB,EAAa,GAAA,SAAc,EAAC,EAAW,GACvC,EAAsB,CAAU,CAAC,EAAE,CACnC,EAAyB,CAAU,CAAC,EAAE,CACpC,EAAkB,GAAe,AAAW,QAAX,EACjC,EAAgB,GAAA,aAAW,EAAC,WAC9B,IAAI,EAAQ,AAAW,QAAX,EACR,EAAgB,GAAc,EAAM,QAAQ,EAAI,EAAE,EAClD,EAA0B,GAAA,KAAI,EAAC,GAAc,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAC3E,WAAY,CACd,EAAG,GAAQ,CAAC,EAAG,CACb,SAAU,EACV,SAAU,GAAuB,EAAoB,EAAO,KAC9D,UASA,CARI,GAAS,CAAC,GACZ,CAAA,EAA0B,GAAA,KAAI,EAAC,GAAc,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CACvE,KAAM,aACN,WAAY,CACd,EAAG,GAAQ,CAAC,EAAG,CACb,SAAU,CACZ,GAAE,EAEA,GAAgB,AAAwB,YAAxB,OAAO,GAClB,EAAa,EAAO,GAEtB,EACT,EAAG,CAAC,EAAqB,EAAc,EAAU,EAAQ,EAAY,EAAM,EAC3E,GAAA,WAAS,EAAC,WAER,IADI,EACA,EAAM,AAAC,CAAA,MAAA,GAA0C,AAAyD,OAAxD,CAAA,EAAwB,EAAQ,kBAAkB,AAAD,GAAe,AAA0B,KAAK,IAA/B,EAAmC,KAAK,EAAI,EAAsB,IAAI,CAAC,EAAO,GAAM,SAAS,IAAI,CACnN,EAAkB,WAEpB,IADI,SAEJ,AAAI,AADY,EAAI,SAAS,CACZ,CAAA,AAAC,CAAA,AAAmC,OAAlC,CAAA,EAAgB,EAAM,MAAM,AAAD,GAAe,AAAkB,KAAK,IAAvB,GAA4B,AAA2C,OAA1C,CAAA,EAAgB,EAAc,MAAM,AAAD,GAAe,AAAkB,KAAK,IAAvB,EAA2B,KAAK,EAAI,EAAc,kBAAkB,AAAD,GAAM,EAAC,GAAM,CAAC,GAC3N,EAAuB,CAAA,GAChB,CAAA,IAEL,GACF,EAAuB,CAAA,GAElB,CAAA,GACT,EACA,GAAK,GACD,AAAkB,aAAlB,OAAO,OAIX,OAHA,EAAI,gBAAgB,CAAC,SAAU,EAAiB,CAC9C,QAAS,CAAA,CACX,GACO,WACL,EAAI,mBAAmB,CAAC,SAAU,GACpC,EACF,EAAG,CAAC,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,kBAAkB,CAAE,EAAiB,EAAoB,EAE/O,IAAI,EAAgB,GAAG,MAAM,CAAC,EAAW,kBACrC,KDlCG,WAAY,EAAC,kBAAmB,SAAU,CAAK,EAIpD,MAAO,CAAC,GAHmB,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,CAAC,EAAG,CACrE,aAAc,IAAI,MAAM,CCgCH,ED/BvB,IACsD,CACxD,GC8BE,EAAU,EAAU,OAAO,CAC3B,EAAS,EAAU,MAAM,CACvB,GCpFqB,EDoFA,GAAG,MAAM,CAAC,EAAe,KAAK,MAAM,CAAC,EAAe,YCnFzE,EAAU,CADsB,EDoFsD,CACxF,wBAAyB,GACzB,QAAS,EAAM,OAAO,AACxB,GCtFmB,OAAO,CACxB,EAA0B,EAAK,uBAAuB,CACjD,GAAA,WAAY,EAAC,yBAA0B,SAAU,CAAK,EAC3D,IAAI,EAAe,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,CAAC,EAAG,CAC7D,aAAc,IAAI,MAAM,CAAC,GACzB,wBAAyB,CAC3B,UACA,AAAK,EACE,CAAC,GAAA,SAAe,EAAC,CAAC,EAAG,MAAM,MAAM,CAAC,EAAM,gBAAgB,CAAE,WAAY,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAa,YAAY,EAAG,MAAA,EAAyC,KAAK,EAAI,EAAQ,KAAgB,CAD5L,EAAE,CAEzB,ID8EI,EAAY,GAAA,UAAU,EAAC,EAAgB,EAAQ,EAAe,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAe,iBAAkB,GAAkB,GAAG,MAAM,CAAC,EAAe,wBAAyB,GAAsB,GAAG,MAAM,CAAC,EAAe,QAAS,AAAW,QAAX,GAAmB,GAAG,MAAM,CAAC,EAAe,wBAAyB,CAAC,GAAY,GAAG,MAAM,CAAC,EAAe,aAT5b,AAAW,QAAX,GASkd,GAAG,MAAM,CAAC,EAAe,WAAY,CAAA,GAAO,GAAG,MAAM,CAAC,EAAe,YAAa,CAAC,CAAC,EAAM,OAAO,SAC/jB,AAAI,AAAW,SAAX,GAAsB,EACnB,EAAQ,OAAO,CAAC,EAAsB,GAAA,KAAI,EAAC,UAAS,CAAE,CAC3D,SAAuB,GAAA,MAAK,EAAC,UAAc,CAEzC,CACA,MAAO,CACL,OAAQ,GAAA,gBAAc,IACtB,WAAY,CACV,OAAQ,CACN,SAAU,cACV,OAAQ,aACV,CACF,CACF,EACA,SAAU,CAAC,GAAgC,GAAA,KAAI,EAAC,GAAQ,CACtD,MAAO,GAAA,SAAa,EAAC,CACnB,OAAQ,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,kBAAkB,AAAD,GAAM,GAClN,WAAY,GAAG,MAAM,CAAC,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,kBAAkB,AAAD,GAAM,GAAI,MACpO,gBAAiB,cACjB,OAAQ,EACV,EAAG,EACL,GAAiB,GAAA,KAAI,EAAC,GAAQ,CAC5B,UAAW,EACX,MAAO,EACP,SAAU,GACZ,GAAG,AACL,EACF,KA3B2C,KA4B7C,4MEtHI,GAAY,CAAC,YAAa,YAAa,eAAe,CAMtD,GAAc,SAAqB,CAAK,EAC1C,IAAI,EAAY,EAAM,SAAS,CAC7B,EAAY,EAAM,SAAS,CAC3B,EAAe,EAAM,YAAY,CACjC,EAAY,GAAA,SAAwB,EAAC,EAAO,IAE5C,EAAW,AADW,EAAM,UAAU,CAAC,aAAU,EACpB,KAAK,CAChC,EAAY,GAAA,gBAAa,EAAC,EAAU,GAIxC,OAAoB,EAAM,aAAa,CAAC,MAAO,GAAA,UAAQ,EAAC,CACtD,UAAW,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAW,YAAa,GACxD,KAAM,SACN,IAAK,CACP,EAAG,GAAA,UAAS,EAAC,EAAO,CAClB,KAAM,CAAA,CACR,GAAI,CACF,aAAc,MAChB,EAAG,IACL,mBC1BO,SAAS,GAAiB,CAAK,QACpC,AAAI,AAAiB,UAAjB,OAAO,GAAsB,OAAO,OAAO,MAAY,GACzD,GAAA,UAAO,EAAC,CAAA,EAAO,kFACR,OAAO,IAET,EACT,SCIA,IAAI,GAAgB,CAClB,MAAO,EACP,OAAQ,EACR,SAAU,SACV,QAAS,OACT,SAAU,UACZ,EAqPI,GAA8B,EAAM,UAAU,CApPlD,SAAqB,CAAK,CAAE,CAAG,EAE7B,IADI,EAAM,EAAsB,EAC5B,EAAY,EAAM,SAAS,CAC7B,EAAO,EAAM,IAAI,CACjB,EAAY,EAAM,SAAS,CAC3B,EAAS,EAAM,MAAM,CACrB,EAAO,EAAM,IAAI,CACjB,EAAc,EAAM,WAAW,CAC/B,EAAY,EAAM,SAAS,CAC3B,EAAW,EAAM,QAAQ,CACzB,EAAmB,EAAM,UAAU,CACnC,EAAgB,EAAM,aAAa,CACnC,EAAY,EAAM,SAAS,CAC3B,EAAS,EAAM,MAAM,CACrB,EAAY,EAAM,SAAS,CAC3B,EAAK,EAAM,EAAE,CACb,EAAQ,EAAM,KAAK,CACnB,EAAS,EAAM,MAAM,CACrB,EAAQ,EAAM,KAAK,CACnB,EAAS,EAAM,MAAM,CACrB,EAAW,EAAM,QAAQ,CACzB,EAAO,EAAM,IAAI,CACjB,EAAe,EAAM,YAAY,CACjC,EAAa,EAAM,UAAU,CAC7B,EAAgB,EAAM,aAAa,CACnC,EAAY,EAAM,SAAS,CAC3B,EAAkB,EAAM,eAAe,CACvC,EAAU,EAAM,OAAO,CACvB,EAAe,EAAM,YAAY,CACjC,EAAc,EAAM,WAAW,CAC/B,EAAe,EAAM,YAAY,CACjC,EAAU,EAAM,OAAO,CACvB,EAAY,EAAM,SAAS,CAC3B,EAAU,EAAM,OAAO,CACvB,EAAS,EAAM,MAAM,CACrB,EAAe,EAAM,YAAY,CAG/B,EAAW,EAAM,MAAM,GACvB,EAAmB,EAAM,MAAM,GAC/B,EAAiB,EAAM,MAAM,GACjC,EAAM,mBAAmB,CAAC,EAAK,WAC7B,OAAO,EAAS,OAAO,CACzB,GAsCA,EAAM,SAAS,CAAC,WACd,GAAI,GAAQ,EAAW,CACrB,IAAI,EACJ,AAA2C,OAA1C,CAAA,EAAoB,EAAS,OAAO,AAAD,GAAe,AAAsB,KAAK,IAA3B,GAAgC,EAAkB,KAAK,CAAC,CACzG,cAAe,CAAA,CACjB,GACF,CACF,EAAG,CAAC,EAAK,EAGT,IAAI,EAAkB,EAAM,QAAQ,CAAC,CAAA,GACnC,EAAmB,GAAA,SAAc,EAAC,EAAiB,GACnD,EAAS,CAAgB,CAAC,EAAE,CAC5B,EAAY,CAAgB,CAAC,EAAE,CAC7B,EAAgB,EAAM,UAAU,CAAC,UAAa,EAW9C,EAAe,AAAkS,OAAjS,CAAA,EAAO,AAA2H,OAA1H,CAAA,EAAuB,AAA+B,OAA9B,CAAA,EAPhD,AAAgB,WAAhB,OAAO,EACI,EAAO,CAAC,EAAI,CACvB,SAAU,CACZ,EAEa,GAAQ,CAAC,CAEmD,GAAe,AAAgB,KAAK,IAArB,EAAyB,KAAK,EAAI,EAAY,QAAQ,AAAD,GAAe,AAAyB,KAAK,IAA9B,EAAkC,EAAuB,MAAA,EAAqD,KAAK,EAAI,EAAc,YAAY,AAAD,GAAe,AAAS,KAAK,IAAd,EAAkB,EAAO,IAClV,EAAgB,EAAM,OAAO,CAAC,WAChC,MAAO,CACL,aAAc,EACd,KAAM,WACJ,EAAU,CAAA,GACZ,EACA,KAAM,WACJ,EAAU,CAAA,GACZ,CACF,EACF,EAAG,CAAC,EAAa,EAIjB,EAAM,SAAS,CAAC,eAER,EAGA,EAJF,EAEF,MAAA,GAAsD,AAA+C,OAA9C,CAAA,EAAsB,EAAc,IAAI,AAAD,GAAe,AAAwB,KAAK,IAA7B,GAAkC,EAAoB,IAAI,CAAC,GAGxK,MAAA,GAAsD,AAA+C,OAA9C,CAAA,EAAsB,EAAc,IAAI,AAAD,GAAe,AAAwB,KAAK,IAA7B,GAAkC,EAAoB,IAAI,CAAC,GAE5K,EAAG,CAAC,EAAK,EAGT,EAAM,SAAS,CAAC,WACd,OAAO,WACL,IAAI,EACJ,MAAA,GAAsD,AAAgD,OAA/C,CAAA,EAAuB,EAAc,IAAI,AAAD,GAAe,AAAyB,KAAK,IAA9B,GAAmC,EAAqB,IAAI,CAAC,GAC7K,EACF,EAAG,EAAE,EAGL,IAAI,EAAwB,EAAM,aAAa,CAAC,UAAS,CAAE,GAAA,UAAQ,EAAC,CAClE,IAAK,MACP,EAAG,EAAY,CACb,QAAS,GAAQ,CACnB,GAAI,SAAU,CAAK,CAAE,CAAO,EAC1B,IAAI,EAAsB,EAAM,SAAS,CACvC,EAAkB,EAAM,KAAK,CAC/B,OAAoB,EAAM,aAAa,CAAC,MAAO,CAC7C,UAAW,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAW,SAAU,EAAqB,MAAA,EAA2D,KAAK,EAAI,EAAiB,IAAI,CAAE,GACrK,MAAO,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAkB,GAAY,MAAA,EAAuC,KAAK,EAAI,EAAO,IAAI,EAC9I,QAAS,GAAgB,EAAO,EAAU,KAAA,EAC1C,IAAK,CACP,GACF,GAGI,GAAc,AAAkB,YAAlB,OAAO,EAAwB,EAAO,GAAa,EACjE,GAAe,CAAC,EACpB,GAAI,GAAU,EACZ,OAAQ,GACN,IAAK,MACH,GAAa,SAAS,CAAG,cAAc,MAAM,CAAC,EAAc,OAC5D,MACF,IAAK,SACH,GAAa,SAAS,CAAG,cAAc,MAAM,CAAC,CAAC,EAAc,OAC7D,MACF,IAAK,OACH,GAAa,SAAS,CAAG,cAAc,MAAM,CAAC,EAAc,OAC5D,MACF,QACE,GAAa,SAAS,CAAG,cAAc,MAAM,CAAC,CAAC,EAAc,OAEjE,CAEE,AAAc,SAAd,GAAwB,AAAc,UAAd,EAC1B,GAAa,KAAK,CAAG,GAAiB,GAEtC,GAAa,MAAM,CAAG,GAAiB,GAEzC,IAAI,GAAgB,CAClB,aAAc,EACd,YAAa,EACb,aAAc,EACd,QAAS,EACT,UAAW,EACX,QAAS,CACX,EACI,GAAyB,EAAM,aAAa,CAAC,UAAS,CAAE,GAAA,UAAQ,EAAC,CACnE,IAAK,OACP,EAAG,GAAa,CACd,QAAS,EACT,YAAa,EACb,iBAAkB,SAA0B,CAAW,EACrD,MAAA,GAA0D,EAAgB,GAC5E,EACA,cAAe,CAAA,EACf,gBAAiB,GAAG,MAAM,CAAC,EAAW,0BACxC,GAAI,SAAU,CAAK,CAAE,CAAS,EAC5B,IAAI,EAAkB,EAAM,SAAS,CACnC,EAAc,EAAM,KAAK,CACvB,EAAuB,EAAM,aAAa,CAAC,GAAa,GAAA,UAAQ,EAAC,CACnE,GAAI,EACJ,aAAc,EACd,UAAW,EACX,UAAW,GAAA,UAAU,EAAC,EAAW,MAAA,EAA2D,KAAK,EAAI,EAAiB,OAAO,EAC7H,MAAO,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,MAAA,EAAuC,KAAK,EAAI,EAAO,OAAO,CAC/G,EAAG,GAAA,UAAS,EAAC,EAAO,CAClB,KAAM,CAAA,CACR,GAAI,IAAgB,GACpB,OAAoB,EAAM,aAAa,CAAC,MAAO,GAAA,UAAQ,EAAC,CACtD,UAAW,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAW,oBAAqB,MAAA,EAA2D,KAAK,EAAI,EAAiB,OAAO,CAAE,GAC9J,MAAO,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,IAAe,GAAc,MAAA,EAAuC,KAAK,EAAI,EAAO,OAAO,CAClJ,EAAG,GAAA,UAAS,EAAC,EAAO,CAClB,KAAM,CAAA,CACR,IAAK,EAAe,EAAa,GAAW,GAC9C,GAGI,GAAiB,GAAA,SAAa,EAAC,CAAC,EAAG,GAIvC,OAHI,GACF,CAAA,GAAe,MAAM,CAAG,CAAK,EAEX,EAAM,aAAa,CAAC,UAAa,CAAC,QAAQ,CAAE,CAC9D,MAAO,CACT,EAAgB,EAAM,aAAa,CAAC,MAAO,CACzC,UAAW,GAAA,UAAU,EAAC,EAAW,GAAG,MAAM,CAAC,EAAW,KAAK,MAAM,CAAC,GAAY,EAAe,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAW,SAAU,GAAO,GAAG,MAAM,CAAC,EAAW,WAAY,IACxM,MAAO,GACP,SAAU,GACV,IAAK,EACL,UAzLmB,SAAwB,CAAK,EAChD,IAQc,EAKA,EAbV,EAAU,EAAM,OAAO,CACzB,EAAW,EAAM,QAAQ,CAC3B,OAAQ,GAEN,KAAK,UAAO,CAAC,GAAG,CAER,IAAY,UAAO,CAAC,GAAG,GACrB,AAAC,GAAY,SAAS,aAAa,GAAK,EAAe,OAAO,CAKvD,GAAY,SAAS,aAAa,GAAK,EAAiB,OAAO,EAExE,CAAA,AAAqD,OAApD,CAAA,EAAwB,EAAe,OAAO,AAAD,GAAe,AAA0B,KAAK,IAA/B,GAAoC,EAAsB,KAAK,CAAC,CAC3H,cAAe,CAAA,CACjB,EAAC,EAPD,AAAuD,OAAtD,CAAA,EAAwB,EAAiB,OAAO,AAAD,GAAe,AAA0B,KAAK,IAA/B,GAAoC,EAAsB,KAAK,CAAC,CAC7H,cAAe,CAAA,CACjB,IAQJ,MAIJ,KAAK,UAAO,CAAC,GAAG,CAER,GAAW,IACb,EAAM,eAAe,GACrB,EAAQ,IAIhB,CACF,CAyJA,EAAG,EAAuB,EAAM,aAAa,CAAC,MAAO,CACnD,SAAU,EACV,IAAK,EACL,MAAO,GACP,cAAe,OACf,gBAAiB,OACnB,GAAI,GAAwB,EAAM,aAAa,CAAC,MAAO,CACrD,SAAU,EACV,IAAK,EACL,MAAO,GACP,cAAe,OACf,gBAAiB,KACnB,KACF,GC9PI,GAAS,SAAgB,CAAK,EAChC,IAAI,EAAc,EAAM,IAAI,CAE1B,EAAmB,EAAM,SAAS,CAElC,EAAmB,EAAM,SAAS,CAElC,EAAmB,EAAM,SAAS,CAElC,EAAkB,EAAM,QAAQ,CAEhC,EAAe,EAAM,KAAK,CAE1B,EAAc,EAAM,IAAI,CACxB,EAAO,AAAgB,KAAK,IAArB,GAAgC,EACvC,EAAsB,EAAM,YAAY,CAExC,EAAe,EAAM,YAAY,CACjC,EAAc,EAAM,WAAW,CAC/B,EAAkB,EAAM,eAAe,CACvC,EAAiB,EAAM,cAAc,CACrC,EAAe,EAAM,YAAY,CACjC,EAAc,EAAM,WAAW,CAC/B,EAAe,EAAM,YAAY,CACjC,EAAU,EAAM,OAAO,CACvB,EAAY,EAAM,SAAS,CAC3B,EAAU,EAAM,OAAO,CACvB,EAAW,EAAM,QAAQ,CACvB,EAAkB,EAAM,QAAQ,CAAC,CAAA,GACnC,EAAmB,GAAA,SAAc,EAAC,EAAiB,GACnD,EAAkB,CAAgB,CAAC,EAAE,CACrC,EAAqB,CAAgB,CAAC,EAAE,CAQtC,EAAmB,EAAM,QAAQ,CAAC,CAAA,GACpC,EAAmB,GAAA,SAAc,EAAC,EAAkB,GACpD,EAAU,CAAgB,CAAC,EAAE,CAC7B,EAAa,CAAgB,CAAC,EAAE,CAClC,GAAA,UAAe,EAAC,WACd,EAAW,CAAA,GACb,EAAG,EAAE,EACL,IAAI,EAAa,EAAA,GA5CR,AAAgB,KAAK,IAArB,GAAiC,EA+CtC,EAAW,EAAM,MAAM,GACvB,EAAgB,EAAM,MAAM,GAChC,GAAA,UAAe,EAAC,WACV,GACF,CAAA,EAAc,OAAO,CAAG,SAAS,aAAa,AAAD,EAEjD,EAAG,CAAC,EAAW,EAgBf,IAAI,EAAa,EAAM,OAAO,CAAC,WAC7B,MAAO,CACL,MAAO,CACT,EACF,EAAG,CAAC,EAAS,EAGb,GAAI,CAAC,GAAe,CAAC,GAAmB,CAAC,GAAc,EACrD,OAAO,KAUT,IAAI,EAAmB,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,CAAC,EAAG,CACjE,KAAM,EACN,UAvFY,AAAqB,KAAK,IAA1B,EAA8B,YAAc,EAwFxD,UAtFY,AAAqB,KAAK,IAA1B,EAA8B,QAAU,EAuFpD,UArFY,AAAqB,KAAK,IAA1B,GAAqC,EAsFjD,SApFW,AAAoB,KAAK,IAAzB,GAAoC,EAqF/C,MAnFQ,AAAiB,KAAK,IAAtB,EAA0B,IAAM,EAoFxC,KAAM,EACN,aAjFe,AAAwB,KAAK,IAA7B,GAAwC,EAkFvD,OAAQ,AAAiB,CAAA,IAAjB,EACR,gBAzC4B,SAAiC,CAAW,MACpE,EAIE,EAHN,EAAmB,GACnB,MAAA,GAA0D,EAAgB,GACrE,IAAe,EAAc,OAAO,EAAM,AAA2C,OAA1C,CAAA,EAAoB,EAAS,OAAO,AAAD,GAAe,AAAsB,KAAK,IAA3B,GAAgC,EAAkB,QAAQ,CAAC,EAAc,OAAO,GAEhL,AAAoD,OAAnD,CAAA,EAAwB,EAAc,OAAO,AAAD,GAAe,AAA0B,KAAK,IAA/B,GAAoC,EAAsB,KAAK,CAAC,CAC1H,cAAe,CAAA,CACjB,GAEJ,EAgCE,IAAK,CACP,EApBoB,CAClB,aAAc,EACd,YAAa,EACb,aAAc,EACd,QAAS,EACT,UAAW,EACX,QAAS,CACX,GAcA,OAAoB,EAAM,aAAa,CAAC,aAAU,CAAC,QAAQ,CAAE,CAC3D,MAAO,CACT,EAAgB,EAAM,aAAa,CAAC,UAAM,CAAE,CAC1C,KAAM,GAAc,GAAe,EACnC,YAAa,CAAA,EACb,aAAc,EACd,SAAU,GAAS,CAAA,GAAc,CAAc,CACjD,EAAgB,EAAM,aAAa,CAAC,GAAa,KACnD,4KC/GA,IAAM,GAAc,IAClB,IAAI,EAAI,EACR,GAAM,CACJ,UAAA,CAAS,CACT,MAAA,CAAK,CACL,OAAA,CAAM,CACN,MAAA,CAAK,CACL,QAAA,CAAO,CACP,QAAA,CAAO,CACP,YAAA,CAAW,CACX,UAAA,CAAS,CACT,YAAA,CAAW,CACX,SAAA,CAAQ,CACR,WAAY,CAAgB,CAC5B,OAAQ,CAAY,CACrB,CAAG,EACE,EAAgB,GAAA,qBAAkB,EAAC,UACnC,EAAwB,EAAM,WAAW,CAAC,GAAsB,EAAM,aAAa,CAAC,SAAU,CAClG,KAAM,SACN,QAAS,EACT,UAAW,CAAC,EAAE,EAAU,MAAM,CAAC,AACjC,EAAG,GAAQ,CAAC,EAAQ,EACd,CAAC,EAAgB,EAAgB,CAAG,GAAA,UAAW,EAAC,GAAA,eAAY,EAAC,GAAQ,GAAA,eAAY,EAAC,GAAgB,CACtG,SAAU,CAAA,EACV,gBAAiB,CACnB,GACM,EAAa,EAAM,OAAO,CAAC,KAC/B,IAAI,EAAI,SACR,AAAI,AAAC,GAAU,EAGK,EAAM,aAAa,CAAC,MAAO,CAC7C,MAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,EAAG,AAAgC,OAA/B,CAAA,EAAK,EAAc,MAAM,AAAD,GAAe,AAAO,KAAK,IAAZ,EAAgB,KAAK,EAAI,EAAG,MAAM,EAAG,GAAc,MAAA,EAAmD,KAAK,EAAI,EAAa,MAAM,EAC/N,UAAW,GAAA,UAAU,EAAC,CAAC,EAAE,EAAU,OAAO,CAAC,CAAE,CAC3C,CAAC,CAAC,EAAE,EAAU,kBAAkB,CAAC,CAAC,CAAE,GAAkB,CAAC,GAAS,CAAC,CACnE,EAAG,AAAoC,OAAnC,CAAA,EAAK,EAAc,UAAU,AAAD,GAAe,AAAO,KAAK,IAAZ,EAAgB,KAAK,EAAI,EAAG,MAAM,CAAE,MAAA,EAA2D,KAAK,EAAI,EAAiB,MAAM,CAChL,EAAgB,EAAM,aAAa,CAAC,MAAO,CACzC,UAAW,CAAC,EAAE,EAAU,aAAa,CAAC,AACxC,EAAG,EAAiB,GAAsB,EAAM,aAAa,CAAC,MAAO,CACnE,UAAW,CAAC,EAAE,EAAU,MAAM,CAAC,AACjC,EAAG,IAAS,GAAsB,EAAM,aAAa,CAAC,MAAO,CAC3D,UAAW,CAAC,EAAE,EAAU,MAAM,CAAC,AACjC,EAAG,IAbM,KAcX,EAAG,CAAC,EAAgB,EAAiB,EAAO,EAAa,EAAW,EAAM,EACpE,EAAa,EAAM,OAAO,CAAC,KAC/B,IAAI,EAAI,EACR,GAAI,CAAC,EACH,OAAO,KAET,IAAM,EAAkB,CAAC,EAAE,EAAU,OAAO,CAAC,CAC7C,OAAoB,EAAM,aAAa,CAAC,MAAO,CAC7C,UAAW,GAAA,UAAU,EAAC,EAAiB,AAAoC,OAAnC,CAAA,EAAK,EAAc,UAAU,AAAD,GAAe,AAAO,KAAK,IAAZ,EAAgB,KAAK,EAAI,EAAG,MAAM,CAAE,MAAA,EAA2D,KAAK,EAAI,EAAiB,MAAM,EAClN,MAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,EAAG,AAAgC,OAA/B,CAAA,EAAK,EAAc,MAAM,AAAD,GAAe,AAAO,KAAK,IAAZ,EAAgB,KAAK,EAAI,EAAG,MAAM,EAAG,GAAc,MAAA,EAAmD,KAAK,EAAI,EAAa,MAAM,CACjO,EAAG,GACL,EAAG,CAAC,EAAQ,EAAa,EAAU,EACnC,OAAoB,EAAM,aAAa,CAAC,EAAM,QAAQ,CAAE,KAAM,EAAyB,EAAM,aAAa,CAAC,MAAO,CAChH,UAAW,GAAA,UAAU,EAAC,CAAC,EAAE,EAAU,KAAK,CAAC,CAAE,MAAA,EAA2D,KAAK,EAAI,EAAiB,IAAI,CAAE,AAAoC,OAAnC,CAAA,EAAK,EAAc,UAAU,AAAD,GAAe,AAAO,KAAK,IAAZ,EAAgB,KAAK,EAAI,EAAG,IAAI,EAClN,MAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,EAAG,AAAgC,OAA/B,CAAA,EAAK,EAAc,MAAM,AAAD,GAAe,AAAO,KAAK,IAAZ,EAAgB,KAAK,EAAI,EAAG,IAAI,EAAG,GAAY,MAAA,EAAmD,KAAK,EAAI,EAAa,IAAI,CAC3N,EAAG,EAAwB,EAAM,aAAa,CAAC,UAAQ,CAAE,CACvD,OAAQ,CAAA,EACR,MAAO,CAAA,EACP,UAAW,CACT,KAAM,CACR,EACA,UAAW,CAAC,EAAE,EAAU,cAAc,CAAC,AACzC,GAAM,GAAW,GACnB,0ECzEA,IAAM,GAAmB,IACvB,IAAM,EAAQ,OACd,MAAO,CAAA,CACL,KAAM,CAAC,YAAY,EAAE,EAAM,CAAC,CAAC,CAC7B,MAAO,CAAC,WAAW,EAAE,EAAM,CAAC,CAAC,CAC7B,IAAK,CAAC,YAAY,EAAE,EAAM,CAAC,CAAC,CAC5B,OAAQ,CAAC,WAAW,EAAE,EAAM,CAAC,CAAC,AAChC,CAAA,CAAC,CAAC,EAAU,CACd,EACM,GAAqB,CAAC,EAAY,IAAc,CAAA,CACpD,oBAAqB,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,EAAG,GAAa,CAChE,WAAY,CACd,GACA,UAAW,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,EAAG,GAAW,CACpD,WAAY,CACd,EACF,CAAA,EACM,GAAe,CAAC,EAAM,IAAa,OAAO,MAAM,CAAC,CACrD,6BAA8B,CAC5B,UAAW,CACT,WAAY,MACd,EACA,WAAY,CACV,WAAY,CAAC,IAAI,EAAE,EAAS,CAAC,AAC/B,CACF,CACF,EAAG,GAAmB,CACpB,QAAS,CACX,EAAG,CACD,QAAS,CACX,IACM,GAAuB,CAAC,EAAW,IAAa,CAAC,GAAa,GAAK,GAAW,GAAmB,CACrG,UAAW,GAAiB,EAC9B,EAAG,CACD,UAAW,MACb,GAAG,CACG,GAAiB,IACrB,GAAM,CACJ,aAAA,CAAY,CACZ,mBAAA,CAAkB,CACnB,CAAG,EACJ,MAAO,CACL,CAAC,EAAa,CAAE,CAEd,CAAC,CAAC,EAAE,EAAa,YAAY,CAAC,CAAC,CAAE,GAAa,EAAG,GAEjD,CAAC,CAAC,EAAE,EAAa,aAAa,CAAC,CAAC,CAAE,CAAC,OAAQ,QAAS,MAAO,SAAS,CAAC,MAAM,CAAC,CAAC,EAAK,IAAc,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,EAAG,GAAM,CACpI,CAAC,CAAC,EAAE,EAAE,EAAU,CAAC,CAAC,CAAE,GAAqB,EAAW,EACtD,GAAI,CAAC,EACP,CACF,EACF,EC9CM,GAAiB,IACrB,GAAM,CACJ,eAAA,CAAc,CACd,aAAA,CAAY,CACZ,YAAA,CAAW,CACX,YAAA,CAAW,CACX,gBAAA,CAAe,CACf,mBAAA,CAAkB,CAClB,kBAAA,CAAiB,CACjB,UAAA,CAAS,CACT,QAAA,CAAO,CACP,UAAA,CAAS,CACT,WAAA,CAAU,CACV,aAAA,CAAY,CACZ,UAAA,CAAS,CACT,SAAA,CAAQ,CACR,WAAA,CAAU,CACV,SAAA,CAAQ,CACR,UAAA,CAAS,CACT,eAAA,CAAc,CACd,iBAAA,CAAgB,CAChB,kBAAA,CAAiB,CACjB,UAAA,CAAS,CACT,iBAAA,CAAgB,CAChB,mBAAA,CAAkB,CAClB,oBAAA,CAAmB,CACnB,KAAA,CAAI,CACL,CAAG,EACE,EAAa,CAAC,EAAE,EAAa,gBAAgB,CAAC,CACpD,MAAO,CACL,CAAC,EAAa,CAAE,CACd,SAAU,QACV,MAAO,EACP,OAAQ,EACR,cAAe,OACf,MAAO,EACP,SAAU,CACR,SAAU,WACV,WAAY,EACZ,QAAS,OACT,cAAe,SACf,CAAC,CAAC,CAAC,EAAE,EAAa,KAAK,CAAC,CAAC,CAAE,CACzB,UAAW,EAAM,mBAAmB,AACtC,EACA,CAAC,CAAC,CAAC,EAAE,EAAa,MAAM,CAAC,CAAC,CAAE,CAC1B,UAAW,EAAM,oBAAoB,AACvC,EACA,CAAC,CAAC,CAAC,EAAE,EAAa,IAAI,CAAC,CAAC,CAAE,CACxB,UAAW,EAAM,iBAAiB,AACpC,EACA,CAAC,CAAC,CAAC,EAAE,EAAa,OAAO,CAAC,CAAC,CAAE,CAC3B,UAAW,EAAM,mBAAmB,AACtC,CACF,EACA,WAAY,CACV,SAAU,UACZ,EAEA,CAAC,CAAC,EAAE,EAAa,KAAK,CAAC,CAAC,CAAE,CACxB,SAAU,WACV,MAAO,EACP,OAAQ,EACR,WAAY,EACZ,cAAe,MACjB,EAEA,CAAC,EAAW,CAAE,CACZ,SAAU,WACV,OAAQ,EACR,SAAU,QACV,WAAY,CAAC,IAAI,EAAE,EAAmB,CAAC,CACvC,WAAY,CACV,QAAS,MACX,CACF,EAEA,CAAC,CAAC,SAAS,EAAE,EAAW,CAAC,CAAC,CAAE,CAC1B,IAAK,EACL,OAAQ,EACR,KAAM,CACJ,aAAc,CAAA,EACd,MAAO,CACT,EACA,UAAW,EAAM,mBAAmB,AACtC,EACA,CAAC,CAAC,UAAU,EAAE,EAAW,CAAC,CAAC,CAAE,CAC3B,IAAK,EACL,MAAO,CACL,aAAc,CAAA,EACd,MAAO,CACT,EACA,OAAQ,EACR,UAAW,EAAM,oBAAoB,AACvC,EACA,CAAC,CAAC,QAAQ,EAAE,EAAW,CAAC,CAAC,CAAE,CACzB,IAAK,EACL,YAAa,EACb,UAAW,EAAM,iBAAiB,AACpC,EACA,CAAC,CAAC,WAAW,EAAE,EAAW,CAAC,CAAC,CAAE,CAC5B,OAAQ,EACR,YAAa,EACb,UAAW,EAAM,mBAAmB,AACtC,EACA,CAAC,CAAC,EAAE,EAAa,QAAQ,CAAC,CAAC,CAAE,CAC3B,QAAS,OACT,cAAe,SACf,MAAO,OACP,OAAQ,OACR,SAAU,OACV,WAAY,EACZ,cAAe,MACjB,EAEA,CAAC,CAAC,EAAE,EAAa,OAAO,CAAC,CAAC,CAAE,CAC1B,QAAS,OACT,KAAM,EACN,WAAY,SACZ,QAAS,CAAC,EAAE,GAAA,OAAI,EAAC,GAAS,CAAC,EAAE,GAAA,OAAI,EAAC,GAAW,CAAC,CAC9C,SAAU,EACV,WAAY,EACZ,aAAc,CAAC,EAAE,GAAA,OAAI,EAAC,GAAW,CAAC,EAAE,EAAS,CAAC,EAAE,EAAW,CAAC,CAC5D,UAAW,CACT,QAAS,OACT,KAAM,EACN,WAAY,SACZ,SAAU,EACV,UAAW,CACb,CACF,EACA,CAAC,CAAC,EAAE,EAAa,MAAM,CAAC,CAAC,CAAE,CACzB,KAAM,MACR,EACA,CAAC,CAAC,EAAE,EAAa,MAAM,CAAC,CAAC,CAAE,OAAO,MAAM,CAAC,CACvC,QAAS,cACT,MAAO,EAAK,GAAY,GAAG,CAAC,GAAW,KAAK,GAC5C,OAAQ,EAAK,GAAY,GAAG,CAAC,GAAW,KAAK,GAC7C,aAAc,EACd,eAAgB,SAChB,WAAY,SACZ,gBAAiB,EACjB,MAAO,EACP,WAAY,EACZ,SAAU,EACV,UAAW,SACX,WAAY,EACZ,UAAW,SACX,cAAe,OACf,eAAgB,OAChB,WAAY,cACZ,OAAQ,EACR,OAAQ,UACR,WAAY,CAAC,IAAI,EAAE,EAAkB,CAAC,CACtC,cAAe,OACf,UAAW,CACT,MAAO,EACP,gBAAiB,EACjB,eAAgB,MAClB,EACA,WAAY,CACV,gBAAiB,CACnB,CACF,EAAG,GAAA,gBAAa,EAAC,IACjB,CAAC,CAAC,EAAE,EAAa,MAAM,CAAC,CAAC,CAAE,CACzB,KAAM,EACN,OAAQ,EACR,WAAY,EAAM,gBAAgB,CAClC,SAAU,EACV,WAAY,CACd,EAEA,CAAC,CAAC,EAAE,EAAa,KAAK,CAAC,CAAC,CAAE,CACxB,KAAM,EACN,SAAU,EACV,UAAW,EACX,QAAS,EACT,SAAU,OACV,CAAC,CAAC,EAAE,EAAa,cAAc,CAAC,CAAC,CAAE,CACjC,MAAO,OACP,OAAQ,OACR,QAAS,OACT,eAAgB,QAClB,CACF,EAEA,CAAC,CAAC,EAAE,EAAa,OAAO,CAAC,CAAC,CAAE,CAC1B,WAAY,EACZ,QAAS,CAAC,EAAE,GAAA,OAAI,EAAC,GAAoB,CAAC,EAAE,GAAA,OAAI,EAAC,GAAqB,CAAC,CACnE,UAAW,CAAC,EAAE,GAAA,OAAI,EAAC,GAAW,CAAC,EAAE,EAAS,CAAC,EAAE,EAAW,CAAC,AAC3D,EAEA,QAAS,CACP,UAAW,KACb,CACF,CACF,EACF,MAOA,GAAe,GAAA,gBAAa,EAAC,SAAU,IACrC,IAAM,EAAc,GAAA,aAAU,EAAC,EAAO,CAAC,GACvC,MAAO,CAAC,GAAe,GAAc,GAAe,GAAa,CACnE,EATqC,GAAU,CAAA,CAC7C,YAAa,EAAM,eAAe,CAClC,mBAAoB,EAAM,SAAS,CACnC,oBAAqB,EAAM,OAAO,AACpC,CAAA,GC5MI,GAAS,IAAI,EAAI,IAAI,CAAC,MAAM,EAAI,SAAU,CAAC,CAAE,CAAC,EAChD,IAAI,EAAI,CAAC,EACT,IAAK,IAAI,KAAK,EAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAG,IAAM,AAAe,EAAf,EAAE,OAAO,CAAC,IAAQ,CAAA,CAAC,CAAC,EAAE,CAAG,CAAC,CAAC,EAAE,AAAD,EAC/F,GAAI,AAAK,MAAL,GAAa,AAAwC,YAAxC,OAAO,OAAO,qBAAqB,CAAiB,IAAK,IAAI,EAAI,EAAG,EAAI,OAAO,qBAAqB,CAAC,GAAI,EAAI,EAAE,MAAM,CAAE,IAChH,EAAlB,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAS,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAG,CAAC,CAAC,EAAE,GAAG,CAAA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,AAAD,EAElG,OAAO,EACT,EAeA,IAAM,GAAmB,CACvB,SAAU,GACZ,EACM,GAAS,IAEb,GAAM,CACF,cAAA,CAAa,CACb,MAAA,CAAK,CACL,OAAA,CAAM,CACN,KAAA,EAAO,SAAS,CAChB,KAAA,EAAO,CAAA,CAAI,CACX,KAAA,EAAO,EAAgB,CACvB,KAAA,CAAI,CACJ,gBAAA,CAAe,CACf,QAAA,CAAO,CACP,UAAW,CAAkB,CAC7B,aAAc,CAAqB,CACnC,MAAA,CAAK,CACL,UAAA,CAAS,CAET,QAAA,CAAO,CACP,mBAAA,CAAkB,CAClB,UAAA,CAAS,CACT,YAAA,CAAW,CACX,oBAAA,CAAmB,CACnB,eAAA,CAAc,CACd,gBAAA,CAAe,CAChB,CAAG,EACJ,EAAO,GAAO,EAAO,CAAC,gBAAiB,QAAS,SAAU,OAAQ,OAAQ,OAAQ,OAAQ,kBAAmB,UAAW,YAAa,eAAgB,QAAS,YAAa,UAAW,qBAAsB,YAAa,cAAe,sBAAuB,iBAAkB,kBAAkB,EAC/R,CACJ,kBAAA,CAAiB,CACjB,aAAA,CAAY,CACZ,UAAA,CAAS,CACT,UAAW,CAAgB,CAC3B,MAAO,CAAY,CACnB,WAAY,CAAiB,CAC7B,OAAQ,CAAa,CACtB,CAAG,GAAA,qBAAkB,EAAC,UACjB,EAAY,EAAa,SAAU,GACnC,CAAC,EAAY,EAAQ,EAAU,CAAG,GAAS,GAC3C,EAEN,AAA0B,KAAA,IAA1B,GAAuC,EAAoB,IAAM,EAAkB,SAAS,IAAI,EAAI,EAC9F,EAAkB,GAAA,UAAU,EAAC,CACjC,UAAW,CAAC,EACZ,CAAC,CAAC,EAAE,EAAU,IAAI,CAAC,CAAC,CAAE,AAAc,QAAd,CACxB,EAAG,EAAe,EAAQ,GAYpB,EAAc,EAAM,OAAO,CAAC,IAAM,MAAA,EAAqC,EAAQ,AAAS,UAAT,EAAmB,IAAM,IAAK,CAAC,EAAO,EAAK,EAC1H,EAAe,EAAM,OAAO,CAAC,IAAM,MAAA,EAAuC,EAAS,AAAS,UAAT,EAAmB,IAAM,IAAK,CAAC,EAAQ,EAAK,EAE/H,EAAa,CACjB,WAAY,GAAA,oBAAiB,EAAC,EAAW,eACzC,aAAc,CAAA,EACd,YAAa,CAAA,EACb,YAAa,CAAA,EACb,eAAgB,GAClB,EAUM,EAAW,GAAA,cAAW,IAEtB,CAAC,EAAQ,EAAc,CAAG,GAAA,YAAS,EAAC,SAAU,EAAK,MAAM,EAEzD,CACJ,WAAY,EAAiB,CAAC,CAAC,CAC/B,OAAQ,EAAa,CAAC,CAAC,CACxB,CAAG,EACJ,OAAO,EAAwB,EAAM,aAAa,CAAC,UAAe,CAAE,CAClE,KAAM,CAAA,EACN,MAAO,CAAA,CACT,EAAgB,EAAM,aAAa,CAAC,UAAa,CAAC,QAAQ,CAAE,CAC1D,MAAO,CACT,EAAgB,EAAM,aAAa,CAAC,GAAU,OAAO,MAAM,CAAC,CAC1D,UAAW,EACX,QAAS,EACT,WAAY,EACZ,OA1BkB,GAAoB,CAAA,CACtC,WAAY,GAAA,oBAAiB,EAAC,EAAW,CAAC,aAAa,EAAE,EAAgB,CAAC,EAC1E,aAAc,CAAA,EACd,YAAa,CAAA,EACb,YAAa,CAAA,EACb,eAAgB,GAClB,CAAA,CAqBA,EAAG,EAAM,CACP,WAAY,CACV,KAAM,GAAA,UAAU,EAAC,EAAe,IAAI,CAAE,EAAkB,IAAI,EAC5D,QAAS,GAAA,UAAU,EAAC,EAAe,OAAO,CAAE,EAAkB,OAAO,EACrE,QAAS,GAAA,UAAU,EAAC,EAAe,OAAO,CAAE,EAAkB,OAAO,CACvE,EACA,OAAQ,CACN,KAAM,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,EAAG,EAAW,IAAI,EAAG,GAAY,EAAc,IAAI,EACpG,QAAS,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,EAAG,EAAW,OAAO,EAAG,GAAc,EAAc,OAAO,EAC/G,QAAS,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,EAAG,EAAW,OAAO,EAAG,GAAsB,EAAc,OAAO,CACzH,EACA,KAAM,MAAA,EAAmC,EAAO,EAChD,KAAM,EACN,KAAM,EACN,MAAO,EACP,OAAQ,EACR,MAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,EAAG,GAAe,GACtD,UAAW,GAAA,UAAU,EAAC,EAAkB,GACxC,cAAe,EACf,aAAc,EACd,gBAAiB,MAAA,EAAyD,EAAkB,EAC5F,SAAU,EACV,OAAQ,EAER,eAAgB,MAAA,EAAyD,EAAkB,CAC7F,GAAiB,EAAM,aAAa,CAAC,GAAa,OAAO,MAAM,CAAC,CAC9D,UAAW,CACb,EAAG,EAAM,CACP,QAAS,CACX,QACF,EAuBA,GAAO,sCAAsC,CArB3B,IAChB,GAAM,CACF,UAAW,CAAkB,CAC7B,MAAA,CAAK,CACL,UAAA,CAAS,CACT,UAAA,EAAY,OAAO,CACpB,CAAG,EACJ,EAAY,GAAO,EAAO,CAAC,YAAa,QAAS,YAAa,YAAY,EACtE,CACJ,aAAA,CAAY,CACb,CAAG,EAAM,UAAU,CAAC,gBAAa,EAC5B,EAAY,EAAa,SAAU,GACnC,CAAC,EAAY,EAAQ,EAAU,CAAG,GAAS,GAC3C,EAAM,GAAA,UAAU,EAAC,EAAW,CAAC,EAAE,EAAU,KAAK,CAAC,CAAE,CAAC,EAAE,EAAU,CAAC,EAAE,EAAU,CAAC,CAAE,EAAQ,EAAW,GACvG,OAAO,EAAwB,EAAM,aAAa,CAAC,MAAO,CACxD,UAAW,EACX,MAAO,CACT,EAAgB,EAAM,aAAa,CAAC,GAAa,OAAO,MAAM,CAAC,CAC7D,UAAW,CACb,EAAG,MACL,ECvKO,IAAI,GAAqB,IAAI,YAAS,CAAC,wBAAyB,CACrE,KAAM,CACJ,QAAS,OACT,QAAS,EACT,SAAU,QACZ,EACA,MAAO,CACL,SAAU,QACZ,EACA,OAAQ,CACN,QAAS,QACT,QAAS,CACX,CACF,GACI,GAAoB,SAA2B,CAAK,EACtD,IAAI,EAAe,EAAgB,EAAgB,EAAgB,EAAgB,EAAgB,EAAgB,EAAgB,EAAgB,EAAiB,EAAiB,EACrL,MAAO,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,gBAAgB,CAAE,WAAY,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,iBAAiB,MAAM,CAAC,EAAM,YAAY,EAAG,CAChM,WAAY,AAAC,CAAA,AAAmC,OAAlC,CAAA,EAAgB,EAAM,MAAM,AAAD,GAAe,AAAkB,KAAK,IAAvB,GAA4B,AAA0C,OAAzC,CAAA,EAAgB,EAAc,KAAK,AAAD,GAAe,AAAkB,KAAK,IAAvB,EAA2B,KAAK,EAAI,EAAc,mBAAmB,AAAD,GAAM,aAClN,GAAI,EAAM,YAAY,CAAE,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CACtK,SAAU,WACV,UAAW,aACX,SAAU,CACR,SAAU,WACV,OAAQ,GACR,UAAW,MACb,CACF,EAAG,KAAK,MAAM,CAAC,EAAM,MAAM,CAAE,0BAA2B,CACtD,SAAU,WACV,QAAS,OACT,cAAe,SACf,OAAQ,OACR,cAAe,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,uBAAuB,CACvN,aAAc,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,sBAAsB,CACrN,gBAAiB,aAAa,MAAM,CAAC,EAAM,UAAU,EACrD,gBAAiB,EACnB,GAAI,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,SAAU,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,0BAA2B,CAC3H,SAAU,EAAM,UAAU,CAC1B,cAAe,CACjB,GAAI,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,mBAAmB,MAAM,CAAC,EAAM,MAAM,CAAE,8BAA+B,CACjG,MAAO,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,sBAAsB,AAChN,IAAK,SAAU,CACb,SAAU,WACV,QAAS,OACT,WAAY,SACZ,eAAgB,gBAChB,cAAe,GACf,aAAc,GACd,MAAO,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,aAAa,CACrM,OAAQ,UACR,eAAgB,aAAa,MAAM,CAAC,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,oBAAoB,EACzO,MAAO,CACL,QAAS,OACT,WAAY,SACZ,eAAgB,SAChB,UAAW,GACX,SAAU,GACV,QAAS,CACP,QAAS,eACT,OAAQ,GACR,cAAe,QACjB,EACA,OAAQ,CACN,QAAS,eACT,OAAQ,GACR,YAAa,EACb,gBAAiB,EACjB,kBAAmB,EACnB,MAAO,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,kBAAkB,CAC1M,cAAe,GACf,kBAAmB,MACnB,wBAAyB,OACzB,WAAY,IACZ,SAAU,GACV,WAAY,OACZ,cAAe,QACjB,CACF,EACA,cAAe,GAAA,SAAe,EAAC,CAC7B,cAAe,iBACf,OAAQ,EACR,QAAS,EACX,EAAG,GAAG,MAAM,CAAC,EAAM,gBAAgB,CAAE,qBAAsB,CACzD,eAAgB,EAChB,SAAU,GACV,WAAY,mDACd,EACF,GAAI,YAAa,CACf,QAAS,OACT,WAAY,SACZ,eAAgB,gBAChB,YAAa,EACb,aAAc,EACd,MAAO,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,aAAa,CACrM,cAAe,CACb,cAAe,iBACf,aAAc,EACd,cAAe,EACf,SAAU,GACV,WAAY,4BACd,EACA,SAAU,CACR,MAAO,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,sBAAsB,CAC9M,cAAe,CACb,eAAgB,EAChB,cAAe,MACjB,EACA,SAAU,CACR,cAAe,EACf,aAAc,EACd,WAAY,OACZ,SAAU,GACV,OAAQ,UACR,aAAc,EAAM,YAAY,CAChC,UAAW,CACT,WAAY,EAAM,gBAAgB,AACpC,CACF,CACF,EACA,WAAY,CACV,SAAU,GACV,cAAe,EACf,aAAc,EACd,QAAS,OACT,WAAY,SACZ,IAAK,EAAM,QAAQ,CACnB,aAAc,EAAM,YAAY,CAChC,MAAO,CACL,OAAQ,SACV,EACA,UAAW,CACT,WAAY,EAAM,gBAAgB,AACpC,CACF,CACF,GAAI,wBAAyB,CAC3B,iBAAkB,IAAI,MAAM,CAAC,EAAM,uBAAuB,CAAG,GAAI,MACjE,SAAU,UACZ,GAAI,UAAW,CACb,eAAgB,GAChB,YAAa,EACb,aAAc,GACd,YAAa,CACX,iBAAkB,EACpB,CACF,GAAI,UAAW,CACb,MAAO,OACP,GAAI,CACF,OAAQ,MACV,CACF,GAAI,cAAe,CACjB,OAAQ,OACR,UAAW,OACX,WAAY,aACd,GAAI,WAAY,CACd,MAAO,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA8C,OAA7C,CAAA,EAAkB,EAAgB,KAAK,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,sBAAsB,CACpN,gBAAiB,GACjB,SAAU,EAAM,QAAQ,CACxB,cAAe,GACf,kBAAmB,MACnB,wBAAyB,MAC3B,IAAK,GAAG,MAAM,CAAC,EAAM,YAAY,EAAE,MAAM,CAAC,EAAM,YAAY,CAAE,UAAW,CACvE,SAAU,QACV,gBAAiB,EACjB,iBAAkB,EAClB,OAAQ,MACR,OAAQ,OACR,QAAS,CACP,OAAQ,eAAe,MAAM,CAAC,AAAC,CAAA,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,kBAAkB,AAAD,GAAM,GAAI,OAClP,gBAAiB,GAAG,MAAM,CAAC,AAAC,CAAA,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,kBAAkB,AAAD,GAAM,GAAI,KACjP,CACF,IACF,ECpKI,GAAmB,SAA0B,CAAK,EAEpD,IDmKuB,ECpKnB,EACA,EAAW,EAAM,QAAQ,CAC3B,EAAa,EAAM,UAAU,CAC7B,EAAY,EAAM,SAAS,CAC3B,EAAa,EAAM,UAAU,CAC7B,EAAQ,EAAM,KAAK,CACnB,EAAY,EAAM,SAAS,CAC3B,EAAO,EAAM,IAAI,CACjB,EAAY,EAAM,SAAS,CAC3B,EAAe,EAAM,YAAY,CAEjC,EAAQ,AADQ,GAAA,YAAU,EAAC,aAAW,EAClB,KAAK,CAC3B,GAAA,WAAS,EAAC,WACS,CAAA,IAAb,GACF,CAAA,MAAA,GAAgD,EAAW,CAAA,EAAI,EAGnE,EAAG,CAAC,EAAS,EACb,IAAI,EAAY,GAAA,UAAI,EAAC,EAAO,CAAC,YAAa,QAAQ,EAEhD,EAAY,AADU,SAAK,CAAC,UAAU,CAAC,UAAc,CAAC,aAAa,EACrC,SAAS,CACrC,GD+ImB,EC/IE,GAAG,MAAM,CAAC,EAAW,UDiJvC,GAAA,WAAY,EAAC,qBAAsB,SAAU,CAAK,EAKvD,MAAO,CAAC,GAJa,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,CAAC,EAAG,CAC/D,aAAc,IAAI,MAAM,CAAC,GACzB,wBCnJyB,EDoJ3B,IAC0C,CAC5C,ICpJE,EAAU,EAAU,OAAO,CAC3B,EAAS,EAAU,MAAM,CACvB,EAAiB,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAW,UAAW,EAAW,GAC3E,GAAI,EACF,OAAO,KAET,IAAI,EAAkB,GAAA,wBAAqB,EAAC,CAAC,EAAW,WACtD,OAAO,MAAA,EAA+C,KAAK,EAAI,EAAW,CAAA,GAC5E,GACA,OAAO,EAAQ,EAAwB,GAAA,KAAI,EAAC,GAAQ,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAC9E,UAAW,AAAc,QAAd,EAAsB,QAAU,OAC3C,UAAW,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,EAAW,iBAAkB,EAC/D,EAAG,GAAkB,CAAC,EAAG,CACvB,MAAO,GAAA,SAAa,EAAC,CACnB,QAAS,EACT,OAAQ,OACV,EAAG,GACH,QAAS,WACP,MAAA,GAAgD,EAAW,CAAA,GAC7D,EACA,aAAc,CAAA,EACd,SAAU,CAAA,EACV,aAAc,GAAgB,CAAA,EAC9B,MAAO,EACP,OAAQ,CACN,KAAM,CACJ,OAAQ,QACR,QAAS,EACT,QAAS,OACT,cAAe,MACf,gBAAiB,AAAmC,OAAlC,CAAA,EAAgB,EAAM,MAAM,AAAD,GAAe,AAAkB,KAAK,IAAvB,GAA4B,AAA0C,OAAzC,CAAA,EAAgB,EAAc,KAAK,AAAD,GAAe,AAAkB,KAAK,IAAvB,EAA2B,KAAK,EAAI,EAAc,mBAAmB,AACjN,CACF,EACA,SAAuB,GAAA,KAAI,EAAC,GAAW,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAY,CAAC,EAAG,CACrF,SAAU,CAAA,EACV,UAAW,EACX,UAAW,CAAA,GAAmB,EAC9B,WAAY,CAAA,EACZ,gBAAiB,CACnB,GACF,IAAmB,GAAA,KAAI,EAAC,GAAW,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAC7D,UAAW,EACX,gBAAiB,CACnB,EAAG,GAAY,CAAC,EAAG,CACjB,MAAO,CACT,KACF,oCC/EW,GAAkB,SAAyB,CAAQ,CAAE,CAAU,CAAE,CAAa,EAGvF,GAAI,EAAe,CACjB,IAAI,EAAU,GAAA,UAAkB,EAAC,EAAc,IAAI,IAAI,IAAI,CAAC,SAAU,CAAG,EACvE,GAAI,CACF,GAAI,EAAI,UAAU,CAAC,QACjB,MAAO,CAAA,EAET,MAAO,GAAA,QAAK,EAAC,GAAK,GACpB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,GAAG,CAAC,MAAO,EAAK,GACjB,CAAA,EACT,CACF,GACA,GAAI,EACF,OAAO,EAAc,GAAG,CAAC,GAE7B,CAIA,GAAI,EAAY,CACd,IAAI,EAAW,OAAO,IAAI,CAAC,GAAY,IAAI,CAAC,SAAU,CAAG,EACvD,GAAI,CACF,GAAI,MAAA,GAAkC,EAAI,UAAU,CAAC,QACnD,MAAO,CAAA,EAET,MAAO,GAAA,QAAK,EAAC,GAAK,GACpB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,GAAG,CAAC,MAAO,EAAK,GACjB,CAAA,EACT,CACF,GACA,GAAI,EACF,OAAO,CAAU,CAAC,EAAS,CAE/B,CACA,MAAO,CACL,KAAM,EACR,EACF,EAOW,GAAmB,SAA0B,CAAK,CAAE,CAAW,EACxE,IAAI,EAAkB,EAAM,QAAQ,CAElC,EAAa,EAAM,UAAU,CAC7B,EAAgB,EAAM,aAAa,CACnC,EAAgB,EAAM,aAAa,CACnC,EAAQ,EAAM,KAAK,CACnB,EAAc,EAAM,IAAI,CAItB,EAAY,EAAc,GAAK,GAAS,GACxC,EAAiB,GAVR,AAAoB,KAAK,IAAzB,EAA6B,IAAM,EAUD,EAAY,GAC3D,GAAI,CAAC,EACH,MAAO,CACL,MAAO,EACP,GAAI,GACJ,SAAU,CACZ,EAEF,IAAI,EAAW,EAAe,IAAI,OAOlC,CANoB,CAAA,IAAhB,AAbK,CAAA,AAAgB,KAAK,IAArB,EAAyB,CAC9B,OAAQ,CAAA,CACV,EAAI,CAAU,EAWP,MAAM,EAAc,EAAe,MAAM,EAAI,GACpD,CAAA,EAAW,EAAc,CACvB,GAAI,EAAe,MAAM,EAAI,GAC7B,eAAgB,EAAe,IAAI,AACrC,EAAC,EAEE,GAOD,GAAe,CAAC,EACX,CACL,MAAO,EACP,GAAI,EAAe,MAAM,EAAI,GAC7B,SAAU,CACZ,EAEK,CACL,MAAO,GAAG,MAAM,CAAC,EAAU,OAAO,MAAM,CAAC,GACzC,GAAI,EAAe,MAAM,EAAI,GAC7B,SAAU,CACZ,EAjBS,CACL,MAAO,EACP,GAAI,EAAe,MAAM,EAAI,GAC7B,SAAU,CACZ,EAcJ,EC9FA,GAAe,GAAA,SAAa,EAAC,CAAC,ECFf,CACb,wBAAyB,qBACzB,6BAA8B,kBAC9B,8BAA+B,mBAC/B,iCAAkC,oBAClC,4BAA6B,gBAC7B,kCAAmC,QACnC,kCAAmC,QACnC,yBAA0B,cAC1B,8BAA+B,WAC/B,iCAAkC,UAClC,gCAAiC,gBACjC,8BAA+B,OAC/B,+BAAgC,cAChC,kCAAmC,sBACnC,kCAAmC,gBACnC,kCAAmC,YACnC,gCAAiC,gBACjC,4BAA6B,gBAC7B,gCAAiC,UACjC,kCAAmC,WACnC,6BAA8B,kBAC9B,+BAAgC,oBAChC,sCAAuC,SACvC,oCAAqC,OACrC,sCAAuC,SACvC,0CAA2C,cAC3C,uBAAwB,mBACxB,sBAAuB,kBACvB,sBAAuB,kBACvB,yBAA0B,cAC1B,0BAA2B,eAC3B,2BAA4B,gBAC5B,gCAAiC,4BACjC,yBAA0B,+BAC1B,8BAA+B,sCAC/B,4BAA6B,iBAC7B,uBAAwB,YACxB,mBAAoB,eACpB,sBAAuB,gBACvB,uBAAwB,4EACxB,8BAA+B,6EACjC,GCxCA,GAAe,GAAA,SAAa,EAAC,CAAC,ECFf,CACb,wBAAyB,wBACzB,6BAA8B,aAC9B,8BAA+B,cAC/B,4BAA6B,qBAC7B,kCAAmC,QACnC,kCAAmC,SACnC,yBAA0B,kBAC1B,8BAA+B,gBAC/B,iCAAkC,UAClC,gCAAiC,qBACjC,8BAA+B,QAC/B,+BAAgC,eAChC,kCAAmC,qBACnC,kCAAmC,sBACnC,kCAAmC,WACnC,gCAAiC,eACjC,6BAA8B,6BAC9B,uBAAwB,gBACxB,sBAAuB,kBACvB,sBAAuB,aACvB,yBAA0B,cAC1B,0BAA2B,gBAC3B,2BAA4B,sBAC5B,gCAAiC,oCACjC,yBAA0B,0CAC1B,8BAA+B,4DAC/B,4BAA6B,qBAC7B,uBAAwB,iBACxB,mBAAoB,qBACpB,sBAAuB,iBACvB,uBAAwB,uFACxB,8BAA+B,kHACjC,GC/BA,GAAe,GAAA,SAAa,EAAC,CAAC,ECFf,CACb,wBAAyB,kCACzB,6BAA8B,4BAC9B,8BAA+B,kCAC/B,4BAA6B,kCAC7B,kCAAmC,eACnC,kCAAmC,eACnC,yBAA0B,4BAC1B,8BAA+B,WAC/B,iCAAkC,UAClC,gCAAiC,gBACjC,8BAA+B,OAC/B,+BAAgC,cAChC,kCAAmC,qBACnC,kCAAmC,gBACnC,kCAAmC,YACnC,gCAAiC,gBACjC,6BAA8B,8CAC9B,+BAAgC,kCAChC,sCAAuC,eACvC,oCAAqC,eACrC,sCAAuC,qBACvC,0CAA2C,4BAC3C,uBAAwB,+CACxB,sBAAuB,yCACvB,sBAAuB,kCACvB,yBAA0B,4BAC1B,0BAA2B,4BAC3B,2BAA4B,wCAC5B,gCAAiC,0GACjC,yBAA0B,4DAC1B,8BAA+B,0GAC/B,4BAA6B,4BAC7B,uBAAwB,kCACxB,mBAAoB,kCACpB,sBAAuB,mCACvB,uBAAwB,oIACxB,8BAA+B,yLACjC,GChCI,GAAU,CACZ,QCLa,GAAA,SAAa,EAAC,CAAC,ECFf,CACb,wBAAyB,uCACzB,6BAA8B,uCAC9B,8BAA+B,uCAC/B,iCAAkC,qDAClC,4BAA6B,uCAC7B,kCAAmC,eACnC,kCAAmC,eACnC,yBAA0B,qBAC1B,8BAA+B,eAC/B,iCAAkC,eAClC,gCAAiC,eACjC,8BAA+B,eAC/B,+BAAgC,qBAChC,kCAAmC,6CACnC,kCAAmC,eACnC,kCAAmC,qBACnC,gCAAiC,eACjC,6BAA8B,2BAC9B,4BAA6B,uCAC7B,gCAAiC,2BACjC,kCAAmC,2BACnC,+BAAgC,2BAChC,sCAAuC,eACvC,oCAAqC,eACrC,sCAAuC,eACvC,0CAA2C,qBAC3C,uBAAwB,uCACxB,sBAAuB,uCACvB,sBAAuB,uCACvB,yBAA0B,uCAC1B,0BAA2B,sBAC3B,2BAA4B,uCAC5B,gCAAiC,+DACjC,yBAA0B,wCAC1B,8BAA+B,+CAC/B,4BAA6B,2BAC7B,uBAAwB,2BACxB,mBAAoB,2BACpB,sBAAuB,uCACvB,uBAAwB,+GACxB,8BAA+B,oNACjC,GFlCE,QGNa,GAAA,SAAa,EAAC,CAAC,ECFf,CACb,wBAAyB,uCACzB,6BAA8B,uCAC9B,iCAAkC,qDAClC,8BAA+B,uCAC/B,4BAA6B,uCAC7B,kCAAmC,eACnC,kCAAmC,eACnC,yBAA0B,qBAC1B,8BAA+B,eAC/B,iCAAkC,eAClC,gCAAiC,eACjC,8BAA+B,eAC/B,+BAAgC,qBAChC,kCAAmC,6CACnC,kCAAmC,qBACnC,kCAAmC,qBACnC,gCAAiC,eACjC,6BAA8B,2BAC9B,uBAAwB,uCACxB,sBAAuB,uCACvB,sBAAuB,uCACvB,yBAA0B,uCAC1B,0BAA2B,sBAC3B,2BAA4B,uCAC5B,gCAAiC,+DACjC,yBAA0B,wCAC1B,8BAA+B,+CAC/B,4BAA6B,2BAC7B,uBAAwB,2BACxB,mBAAoB,2BACpB,sBAAuB,uCACvB,uBAAwB,+GACxB,8BAA+B,oNACjC,GJzBE,QAAS,GACT,QAAS,GACT,QAAS,EACX,EKRI,GAAa,WACf,IAAI,SACJ,AAAI,AAAmB,KAAA,MAAoB,UAAO,CAC3C,AAAC,CAAA,AAAyB,OAAxB,CAAA,GAAiB,GAAe,AAAa,KAAK,IAAlB,GAAuB,AAA8B,OAA7B,CAAA,EAAW,EAAS,GAAG,AAAD,GAAe,AAAa,KAAK,IAAlB,EAAsB,KAAK,EAAI,EAAS,YAAY,AAAD,GAAM,UAAO,CACxK,EASI,GAAkB,SAAyB,CAAK,EAClD,IAAI,EAAa,EAAe,EAAgB,EAAgB,EAAgB,EAAgB,EAAgB,EAAgB,EAAgB,EAAgB,EAAiB,EAAiB,EAAiB,EAAiB,EAAiB,EAAiB,EAAiB,EAAiB,EAAiB,EAAiB,EAAW,EAAiB,EAAiB,EAAiB,EAAiB,EAAiB,EAAiB,EAAiB,EAAiB,EAAiB,EAAiB,SAC/f,AAAI,AAAiC,OAAhC,CAAA,EAAc,IAAW,GAAe,AAAgB,KAAK,IAArB,GAA0B,EAAY,UAAU,CAAC,KACrF,CAAC,EAEH,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,EAAM,YAAY,CAAE,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAC7G,MAAO,OACP,OAAQ,MACV,EAAG,GAAG,MAAM,CAAC,EAAM,gBAAgB,CAAE,cAAgB,CAAA,EAAY,CAC/D,MAAO,AAAmC,OAAlC,CAAA,EAAgB,EAAM,MAAM,AAAD,GAAe,AAAkB,KAAK,IAAvB,GAA4B,AAA0C,OAAzC,CAAA,EAAgB,EAAc,KAAK,AAAD,GAAe,AAAkB,KAAK,IAAvB,EAA2B,KAAK,EAAI,EAAc,aAAa,AACjM,EAAG,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,EAAW,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,aAAc,CAClN,gBAAiB,wBACjB,MAAO,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,aAAa,AACvM,GAAI,KAAK,MAAM,CAAC,EAAM,MAAM,CAAE,WAAY,CACxC,gBAAiB,cACjB,MAAO,MACT,GAAI,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,+BAA+B,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,CACtG,MAAO,SACT,GAAI,IAAI,MAAM,CAAC,EAAM,MAAM,CAAE,SAAU,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CACrE,MAAO,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,aAAa,AACvM,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,cAAe,CACxC,IAAK,CACH,WAAY,iBACd,CACF,GAAI,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,gBAAiB,CAC3C,MAAO,SACT,IAAK,IAAI,MAAM,CAAC,EAAM,MAAM,CAAE,gBAAiB,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,0BAA0B,MAAM,CAAC,EAAM,MAAM,CAAE,8BAA+B,CACvK,QAAS,MACX,IAAK,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,cAAc,MAAM,CAAC,EAAM,MAAM,CAAE,gBAAiB,CAC/E,gBAAiB,uBACnB,GAAI,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,iCAAiC,MAAM,CAAC,EAAM,MAAM,CAAE,8BAA+B,CAC/G,gBAAiB,uBACnB,GAAI,IAAI,MAAM,CAAC,EAAM,MAAM,CAAE,eAAgB,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,oCAAoC,MAAM,CAAC,EAAM,MAAM,CAAE,oCAAoC,MAAM,CAAC,EAAM,MAAM,CAAE,wCAAwC,MAAM,CAAC,EAAM,MAAM,CAAE,6BAA8B,GAAA,SAAe,EAAC,CACpT,MAAO,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,mBAAmB,CAC3M,aAAc,EAAM,YAAY,AAClC,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,CACjD,MAAO,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,mBAAmB,AAC7M,KAAM,IAAI,MAAM,CAAC,EAAM,MAAM,CAAE,cAAc,MAAM,CAAC,EAAM,MAAM,CAAE,qBAAsB,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,CAC1K,gBAAiB,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,uBAAuB,CACzN,aAAc,EAAM,YAAY,AAClC,GAAI,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,oCAAoC,MAAM,CAAC,EAAM,MAAM,CAAE,oCAAoC,MAAM,CAAC,EAAM,MAAM,CAAE,6BAA8B,GAAA,SAAe,EAAC,CAC1L,MAAO,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,mBAAmB,CAC3M,aAAc,EAAM,YAAY,CAChC,gBAAiB,GAAG,MAAM,CAAC,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA6C,OAA5C,CAAA,EAAiB,EAAe,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,oBAAoB,CAAE,cACrO,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,CACjD,MAAO,AAAoC,OAAnC,CAAA,EAAiB,EAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,mBAAmB,AAC7M,KAAM,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,CACpD,MAAO,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA8C,OAA7C,CAAA,EAAkB,EAAgB,KAAK,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,qBAAqB,AACrN,GAAI,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,EAAW,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,0BAA2B,CAChJ,MAAO,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA8C,OAA7C,CAAA,EAAkB,EAAgB,KAAK,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,qBAAqB,AACrN,GAAI,IAAI,MAAM,CAAC,EAAM,MAAM,CAAE,cAAc,MAAM,CAAC,EAAM,MAAM,CAAE,kBAAkB,MAAM,CAAC,EAAM,MAAM,CAAE,sBAAuB,CAC5H,MAAO,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA8C,OAA7C,CAAA,EAAkB,EAAgB,KAAK,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,qBAAqB,AACrN,GAAI,IAAI,MAAM,CAAC,EAAM,MAAM,CAAE,kBAAmB,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,0BAA2B,CACrH,aAAc,EAAM,YAAY,CAChC,MAAO,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA8C,OAA7C,CAAA,EAAkB,EAAgB,KAAK,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,qBAAqB,AACrN,IAAK,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,0BAA0B,MAAM,CAAC,EAAM,MAAM,CAAE,0BAA0B,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,CACjJ,MAAO,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA8C,OAA7C,CAAA,EAAkB,EAAgB,KAAK,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,mBAAmB,AACnN,GAAI,IAAI,MAAM,CAAC,EAAM,MAAM,CAAE,oBAAqB,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,iCAAiC,MAAM,CAAC,EAAM,MAAM,CAAE,oCAAoC,MAAM,CAAC,EAAM,MAAM,CAAE,kCAAkC,MAAM,CAAC,EAAM,MAAM,CAAE,wBAAyB,CAC3U,aAAc,EACd,WAAY,OACZ,MAAO,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,mBAAmB,CAClN,gBAAiB,GAAG,MAAM,CAAC,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,oBAAoB,CAAE,cAC3O,GAAI,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,gCAAgC,MAAM,CAAC,EAAM,MAAM,CAAE,mCAAmC,MAAM,CAAC,EAAM,MAAM,CAAE,oCAAoC,MAAM,CAAC,EAAM,MAAM,CAAE,0BAA2B,GAAA,SAAe,EAAC,CAC3O,gBAAiB,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,uBAAuB,CAChO,aAAc,EAAM,YAAY,CAChC,WAAY,OACZ,MAAO,GAAG,MAAM,CAAC,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,qBAAqB,CAAE,cAClO,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,CACjD,MAAO,GAAG,MAAM,CAAC,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,qBAAqB,CAAE,cAClO,IAAK,KAAK,MAAM,CAAC,EAAM,MAAM,CAAE,kBAAkB,MAAM,CAAC,EAAM,MAAM,CAAE,iBAAkB,CACtF,cAAe,GACf,aAAc,CAChB,GAAI,KAAK,MAAM,CAAC,EAAM,MAAM,CAAE,yBAAyB,MAAM,CAAC,EAAM,MAAM,CAAE,wBAAyB,CACnG,QAAS,MACX,GAAE,GAAK,GAAG,MAAM,CAAC,EAAM,gBAAgB,CAAE,6BAA8B,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,IAAI,MAAM,CAAC,EAAM,MAAM,CAAE,SAAU,GAAA,SAAe,EAAC,CAC5J,MAAO,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,aAAa,AAC9M,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,gBAAiB,CAC1C,MAAO,SACT,IAAK,IAAI,MAAM,CAAC,EAAM,MAAM,CAAE,eAAgB,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,oCAAoC,MAAM,CAAC,EAAM,MAAM,CAAE,oCAAoC,MAAM,CAAC,EAAM,MAAM,CAAE,wCAAwC,MAAM,CAAC,EAAM,MAAM,CAAE,6BAA8B,GAAA,SAAe,EAAC,CACrU,MAAO,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,mBAAmB,CAClN,aAAc,EAAM,YAAY,CAChC,WAAY,OACZ,gBAAiB,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,uBAAuB,AAClO,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,CACjD,MAAO,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,mBAAmB,AACpN,IAAK,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,CACnD,MAAO,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,qBAAqB,CACpN,aAAc,EAAM,YAAY,CAChC,gBAAiB,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA+C,OAA9C,CAAA,EAAkB,EAAgB,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,uBAAuB,AAClO,MAAO,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,aAAa,MAAM,CAAC,EAAM,MAAM,CAAE,gBAAiB,CAChF,gBAAiB,uBACnB,GAAI,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAClH,gBAAiB,4BACjB,0BAA2B,YAC3B,eAAgB,WAClB,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,SAAU,GAAA,SAAe,EAAC,CACnD,WAAY,yBACZ,gBAAiB,wBACnB,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,iCAAiC,MAAM,CAAC,EAAM,MAAM,CAAE,8BAA+B,CAC9G,gBAAiB,uBACnB,IAAK,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,CACnD,MAAO,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA8C,OAA7C,CAAA,EAAkB,EAAgB,KAAK,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,qBAAqB,AACrN,GAAI,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,0BAA2B,CACrD,MAAO,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA8C,OAA7C,CAAA,EAAkB,EAAgB,KAAK,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,qBAAqB,AACrN,GAAI,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,cAAc,MAAM,CAAC,EAAM,MAAM,CAAE,qBAAsB,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,CACvK,gBAAiB,sBACjB,aAAc,EAAM,YAAY,CAChC,MAAO,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA8C,OAA7C,CAAA,EAAkB,EAAgB,KAAK,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,qBAAqB,AACrN,GAAI,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,kCAAkC,MAAM,CAAC,EAAM,MAAM,CAAE,kCAAkC,MAAM,CAAC,EAAM,MAAM,CAAE,6BAA8B,GAAA,SAAe,EAAC,CACtL,MAAO,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA8C,OAA7C,CAAA,EAAkB,EAAgB,KAAK,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,mBAAmB,CACjN,aAAc,EAAM,YAAY,AAClC,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,uBAAwB,CACjD,MAAO,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA8C,OAA7C,CAAA,EAAkB,EAAgB,KAAK,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,mBAAmB,AACnN,MACF,EACI,GAAoB,SAA2B,CAAK,EACtD,IAAI,EAAiB,EAAiB,EAAiB,EACvD,MAAO,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,GAAG,MAAM,CAAC,EAAM,MAAM,CAAE,WAAY,CAC7E,gBAAiB,wBACnB,GAAI,EAAM,YAAY,CAAE,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,KAAK,MAAM,CAAC,EAAM,MAAM,CAAE,WAAY,CAChI,QAAS,OACT,gBAAiB,cACjB,MAAO,MACT,GAAI,GAAG,MAAM,CAAC,EAAM,YAAY,CAAE,YAAa,CAC7C,QAAS,OACT,cAAe,SACf,MAAO,OACP,gBAAiB,AAAC,CAAA,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAAsD,OAArD,CAAA,EAAkB,EAAgB,aAAa,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,oBAAoB,AAAD,GAAM,cAC1O,SAAU,WACV,aAAc,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAAsD,OAArD,CAAA,EAAkB,EAAgB,aAAa,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,gCAAgC,CAC7O,cAAe,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAAsD,OAArD,CAAA,EAAkB,EAAgB,aAAa,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,iCAAiC,CAC/O,uBAAwB,CACtB,QAAS,CACX,CACF,GAAI,GAAG,MAAM,CAAC,EAAM,YAAY,CAAE,cAAe,CAC/C,MAAO,OACP,QAAS,OACT,cAAe,SACf,SAAU,EACV,UAAW,EACX,gBAAiB,aACnB,GAAI,GAAG,MAAM,CAAC,EAAM,YAAY,CAAE,YAAa,CAC7C,cAAe,OACf,SAAU,QACV,SAAU,SACV,gBAAiB,EACjB,iBAAkB,EAClB,OAAQ,EACR,OAAQ,OACR,MAAO,OACP,WAAY,AAAqC,OAApC,CAAA,EAAkB,EAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,QAAQ,AACzH,IACF,mBCnKW,GAAa,WACtB,IAAI,SACJ,AAAI,AAAmB,KAAA,MAAoB,UAAO,CAC3C,AAAC,CAAA,AAAyB,OAAxB,CAAA,GAAiB,GAAe,AAAa,KAAK,IAAlB,GAAuB,AAA8B,OAA7B,CAAA,EAAW,EAAS,GAAG,AAAD,GAAe,AAAa,KAAK,IAAlB,EAAsB,KAAK,EAAI,EAAS,YAAY,AAAD,GAAM,UAAO,CACxK,EAGI,GAAoB,SAA2B,CAAK,CAAE,CAAC,CAAE,CAAM,EACjE,IACE,EAAiB,AADR,EACa,cAAc,CACpC,EAAQ,AAFC,EAEI,KAAK,CAClB,EAAO,AAHE,EAGG,IAAI,CAOlB,OAAO,AANI,EAAO,SAAS,CAAC,SAAU,CAAC,EACrC,OAEE,EAAE,QAAQ,GAAK,EAAM,IAAI,CAE7B,KAAO,EAAO,MAAM,CAAG,EACI,GAAA,KAAI,EAAC,OAAQ,CACtC,SAAU,GAAS,CACrB,GAAkB,GAAA,KAAI,EAAC,OAAQ,CAC7B,QAAS,EAAO,WACd,OAAO,SAAS,IAAI,CAAG,EACzB,EAAI,KAAA,EACJ,SAAU,GAAS,CACrB,GACF,EACI,GAAkB,SAAyB,CAAI,CAAE,CAAK,EACxD,IAAI,EAAgB,EAAM,aAAa,CACrC,EAAO,EAAM,IAAI,QACnB,AAAI,EAAK,MAAM,EAAI,GAAiB,AAAC,CAAA,MAAA,EAAmC,KAAK,EAAI,EAAK,MAAM,AAAD,IAAO,CAAA,EACzF,EAAc,CACnB,GAAI,EAAK,MAAM,CACf,eAAgB,EAAK,IAAI,AAC3B,GAEK,EAAK,IAAI,CAClB,EACW,GAAgB,SAAuB,CAAa,CAAE,CAAG,EAClE,IAAI,EAAiB,EAAc,GAAG,CAAC,GACvC,GAAI,CAAC,EAAgB,CAInB,IAAI,EAAa,AADN,CAAA,MAAM,IAAI,CAAC,EAAc,IAAI,KAAO,EAAE,AAAD,EAC1B,IAAI,CAAC,SAAU,CAAI,EACvC,GAAI,CACF,GAAI,MAAA,GAAoC,EAAK,UAAU,CAAC,QAAS,MAAO,CAAA,EACxE,MAAO,GAAA,QAAK,EAAC,EAAK,OAAO,CAAC,IAAK,KAAK,GACtC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,GAAG,CAAC,OAAQ,EAAM,GACnB,CAAA,EACT,CACF,GAGI,GAAY,CAAA,EAAiB,EAAc,GAAG,CAAC,EAAU,EAC/D,CACA,OAAO,GAAkB,CACvB,KAAM,EACR,EACF,EAiCW,GAAqB,SAA4B,CAAK,EAC/D,IAAI,EA9BG,CACL,SAHa,AAgCoC,EAhC9B,QAAQ,CAI3B,cAHgB,AA+BiC,EA/B3B,aAAa,AAIrC,EA4BE,EAAW,EAAsB,QAAQ,CACzC,EAAgB,EAAsB,aAAa,QAIrD,AAAI,GAAY,EAAS,QAAQ,EAAI,EA3BV,AAFR,AC5Ed,CAAA,SAAmB,CAAG,EAC3B,GAAI,CAAC,GAAO,AAAQ,MAAR,EACV,MAAO,CAAC,IAAI,CAEd,IAAI,EAAU,EAAI,KAAK,CAAC,KAAK,MAAM,CAAC,SAAU,CAAC,EAC7C,OAAO,EACT,GACA,OAAO,EAAQ,GAAG,CAAC,SAAU,CAAO,CAAE,CAAK,EACzC,MAAO,IAAI,MAAM,CAAC,EAAQ,KAAK,CAAC,EAAG,EAAQ,GAAG,IAAI,CAAC,MACrD,GACF,CAAA,EDkE+B,MA8BG,EA9BoD,KAAK,EAAI,AA8B7D,EA9B4E,QAAQ,EAE5E,GAAG,CAAC,SAAU,CAAG,EACvD,IAAI,EAAoB,GA2BgB,EA3Ba,GACjD,EAAO,GAAgB,EA0B4B,GAzBnD,EAAmB,EAAkB,gBAAgB,CACzD,OAAO,GAAQ,CAAC,EAAmB,CACjC,SAAU,EACV,eAAgB,EAChB,MAAO,EACP,UAAW,EAAkB,SAAS,AACxC,EAAI,CACF,SAAU,GACV,eAAgB,GAChB,MAAO,EACT,EACF,GAAG,MAAM,CAAC,SAAU,CAAI,EACtB,OAAO,GAAQ,EAAK,QAAQ,CAC9B,GAcO,EAAE,CACX,EAGW,GAAqB,SAA4B,CAAK,CAAE,CAAU,EAG3E,IAAI,EAAmB,EAAM,gBAAgB,CAC3C,EAAkB,EAAM,UAAU,CAGlC,EAAkB,AADR,CAAA,EAAW,eAAe,EAAI,CAAC,CAAA,EACjB,SAAS,CAG/B,EAAc,GAAmB,GAEjC,EAAa,SAAoB,CAAI,EAEvC,IAAK,IADD,EAAiB,GAAmB,GAC/B,EAAO,UAAU,MAAM,CAAE,EAAO,AAAI,MAAM,EAAO,EAAI,EAAO,EAAI,GAAI,EAAO,EAAG,EAAO,EAAM,IAClG,CAAI,CAAC,EAAO,EAAE,CAAG,SAAS,CAAC,EAAK,CAElC,OAAO,MAAA,EAAuD,KAAK,EAAI,EAAe,KAAK,CAAC,KAAK,EAAG,CAAC,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAO,CAAC,EAAG,CAG9I,KAAM,EAAK,QAAQ,EAAI,EAAK,IAAI,AAClC,GAAG,CAAC,MAAM,CAAC,IACb,EACI,EAAQ,EAUZ,OARI,GACF,CAAA,EAAQ,EAAiB,GAAS,EAAE,GAAK,KAAA,CAAQ,EAG/C,CAAA,GAAS,EAAM,MAAM,CArBX,CAAA,AAAoB,KAAK,IAAzB,EAA6B,EAAI,CAAc,GAqBpB,AAAqB,CAAA,IAArB,CAAyB,GAChE,CAAA,EAAQ,KAAA,CAAQ,EAGX,GAAA,kBAAe,EAAC,KAAc,SAAW,GAAK,CACnD,MAAO,EACP,WAAY,CACd,EAAI,CACF,OAAQ,EACR,WAAY,CACd,EACF,EE5II,GAAc,SAAS,EAAY,CAAM,CAAE,CAAI,CAAE,CAAa,CAAE,CAAc,EAChF,IAAI,EAAkB,GAAe,EAAQ,AAAC,CAAA,MAAA,EAAmC,KAAK,EAAI,EAAK,MAAM,AAAD,GAAM,CAAA,EAAO,EAAe,CAAA,GAC9H,EAAW,EAAgB,QAAQ,CACnC,EAAa,EAAgB,UAAU,QACzC,AAAK,EAOE,EAAY,EAAe,GAAW,EAAM,EAAe,KAAA,GANzD,CACL,UAAU,CAfP,GAAA,UAAkB,EAeG,GAfQ,MAAM,CAAC,SAAU,CAAG,CAAE,CAAI,EAC5D,IAAI,EAAQ,GAAA,SAAc,EAAC,EAAM,GAC/B,EAAM,CAAK,CAAC,EAAE,CACd,EAAM,CAAK,CAAC,EAAE,CAGhB,OADA,CAAG,CAAC,EAAI,CAAG,EACJ,EACT,EAAG,CAAC,GASA,cAAe,EACf,SAAU,CACZ,EAGJ,+CCrBI,GAA4B,SAAmC,CAAW,EAC5E,IAAI,EAAY,GAAA,UAAQ,EAAC,CAAC,GACxB,EAAa,GAAA,SAAc,EAAC,EAAW,GACvC,EAAyB,CAAU,CAAC,EAAE,CACtC,EAA4B,CAAU,CAAC,EAAE,CAa3C,MAZA,GAAA,WAAS,EAAC,WACR,EAA0B,GAAA,gBAAa,EAAC,CAEtC,OAAQ,AAAgC,WAAhC,GAAA,UAAO,EAAC,EAAY,MAAM,EAAiB,EAAY,MAAM,CAAG,KAAA,EACxE,SAAU,EAAY,QAAQ,CAC9B,WAAY,EAAY,UAAU,CAClC,aAAc,EAAY,YAAY,CACtC,iBAAkB,EAAY,gBAAgB,CAC9C,aAAc,EAAY,YAAY,CACtC,YAAa,EAAY,WAAW,AACtC,IACF,EAAG,CAAC,EAAY,MAAM,CAAE,EAAY,QAAQ,CAAE,EAAY,UAAU,CAAE,EAAY,YAAY,CAAE,EAAY,gBAAgB,CAAE,EAAY,YAAY,CAAE,EAAY,WAAW,CAAC,EACzK,EACT,EChBI,GAAY,CAAC,KAAM,iBAAiB,CACtC,GAAa,CAAC,cAAe,WAAY,SAAS,CA6BhD,GAAc,EACd,GAAe,SAAsB,CAAK,CAAE,CAAa,EAC3D,IAAI,QACJ,AAAI,AAAuB,CAAA,IAAvB,EAAM,YAAY,EAAc,EAAM,IAAI,CACrC,KAEW,GAAA,KAAI,EAAC,GAAQ,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAC3D,cAAe,CACjB,EAAG,GAAQ,CAAC,EAAG,CACb,QAAS,AAAqC,OAApC,CAAA,EAAiB,EAAM,OAAO,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,MAAM,AAClH,IACF,EAUI,GAAkB,SAAyB,CAAK,CAAE,CAAa,EAEjE,IAgCM,EAhCF,EAAS,EAAM,MAAM,CACvB,EAAW,EAAM,QAAQ,CACzB,EAAe,EAAM,YAAY,CACjC,EAAW,EAAM,QAAQ,CACzB,EAAa,EAAM,UAAU,CAC7B,EAA6B,EAAM,0BAA0B,CAC7D,EAAa,EAAM,UAAU,CAC/B,GAAI,AAAqB,CAAA,IAArB,EAAM,UAAU,EAAc,EAAM,IAAI,CAC1C,OAAO,KAET,IAAI,EAAW,EAAM,QAAQ,CAG7B,GAAI,GAAe,CAAA,AAAa,CAAA,IAAb,GAAsB,AAAW,QAAX,CAAe,GAAM,CAAC,EAAU,CACvE,IAfE,EAmBI,EAJF,EAAO,GAAgB,EAEzB,EAAM,AADE,GAAA,SAAc,EAAC,EAAM,EAClB,CAAC,EAAE,CAGd,EAFE,GAEU,CAAA,AAAuC,OAAtC,CAAA,EAAkB,EAAM,QAAQ,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAEjF,OAFkF,CAAA,EAAkB,EAAgB,IAAI,CAAC,SAAU,CAAI,EAC7I,OAAO,EAAK,GAAG,GAAK,EACtB,EAAC,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,QAAQ,AAAD,GAAM,EAAE,CAIzF,CAEA,IAAI,EAAgB,GAAc,GAAY,EAAE,EAChD,GAAI,GAAiB,AAAC,CAAA,MAAA,EAAqD,KAAK,EAAI,EAAc,MAAM,AAAD,EAAK,GAAM,CAAA,GAAc,CAAyB,EACvJ,OAAO,KAET,GAAI,AAAW,QAAX,GAAoB,CAAC,EAEvB,MAAoB,GAAA,KAAI,EAAC,GAAW,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAC9D,cAAe,CACjB,EAAG,GAAQ,CAAC,EAAG,CACb,KAAM,CAAA,EACN,QAAS,AAAsC,OAArC,CAAA,EAAkB,EAAM,OAAO,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,KAAK,AACpH,IAEF,IAAI,EAA0B,GAAA,KAAI,EAAC,GAAW,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CACxE,cAAe,CACjB,EAAG,GAAQ,CAAC,EAAG,CAEb,SAAU,EACV,QAAS,AAAsC,OAArC,CAAA,EAAkB,EAAM,OAAO,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,KAAK,AACpH,WACA,AAAI,EACK,EAAW,EAAO,GAEpB,EACT,EACI,GAAyB,SAAgC,CAAS,CAAE,CAAK,EAC3E,IAAI,EAAkB,EAAM,eAAe,CACvC,EAAgB,GAAiB,GACrC,GAAI,AAAoB,CAAA,IAApB,EACF,MAAO,CACL,MAAO,EAAM,KAAK,EAAI,GACtB,GAAI,GACJ,SAAU,EACZ,EAEF,GAAI,EAAiB,CACnB,IAAI,EAAQ,EAAgB,EAAW,EAAc,KAAK,CAAE,GAC5D,GAAI,AAAiB,UAAjB,OAAO,EACT,OAAO,GAAiB,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAgB,CAAC,EAAG,CAC1E,MAAO,CACT,IAEF,GAAA,UAAO,EAAC,AAAiB,UAAjB,OAAO,EAAoB,+DACrC,CACA,OAAO,EACT,EAaI,GAAgB,SAAuB,CAAK,EAE9C,IA8DQ,EAhK+B,EC9CG,EACtC,ED8IA,EAAkB,EAAoB,EAAe,EAAgB,EAAgB,EAAgB,EAAgB,EAAgB,EAAgB,EAAgB,EAAgB,EAAiB,EAAiB,EACvN,EAAQ,GAAS,CAAC,EACpB,EAAW,EAAM,QAAQ,CACzB,EAAkB,EAAM,UAAU,CAClC,EAAiB,EAAM,QAAQ,CAC/B,EAAW,AAAmB,KAAK,IAAxB,EAA4B,CACrC,SAAU,GACZ,EAAI,EACJ,EAAe,EAAM,YAAY,CACjC,EAAQ,EAAM,KAAK,CACnB,EAAmB,EAAM,gBAAgB,CACzC,EAAQ,EAAM,KAAK,CACnB,EAAkB,EAAM,UAAU,CAClC,EAAO,EAAM,IAAI,CACjB,EAAgB,EAAM,aAAa,CACnC,EAAwB,EAAM,gBAAgB,CAC9C,EAAiB,EAAM,cAAc,CACrC,EAAY,EAAM,SAAS,CAC3B,EAAkB,EAAM,eAAe,CACvC,EAAqB,EAAM,aAAa,CACxC,EAAU,EAAM,OAAO,CACrB,EAAa,GAAA,SAAO,EAAC,kBACvB,AAAI,IACA,AAAiB,QAAjB,EAAM,MAAM,CAAmB,IAC5B,KACT,EAAG,CAAC,EAAM,MAAM,CAAE,EAAgB,EAC9B,EAAU,GAAA,YAAU,EAAC,UAAc,CAAC,aAAa,EACjD,EAAY,AAAyC,OAAxC,CAAA,EAAmB,EAAM,SAAS,AAAD,GAAe,AAAqB,KAAK,IAA1B,EAA8B,EAAmB,EAAQ,YAAY,CAAC,OACnI,EAAsB,GAAA,oBAAkB,EAAC,CAAA,EAAO,CAChD,MAAO,MAAA,EAAmC,KAAK,EAAI,EAAK,OAAO,CAC/D,SAAU,MAAA,EAAmC,KAAK,EAAI,EAAK,eAAe,AAC5E,GACA,GAAuB,GAAA,SAAc,EAAC,EAAqB,GAC3D,GAAc,EAAoB,CAAC,EAAE,CACrC,GAAiB,EAAoB,CAAC,EAAE,CAGtC,GAAY,GAAA,UAAQ,EAAC,WAErB,OADA,IAAe,EACR,cAAc,MAAM,CAAC,IAC9B,GAEA,GAAY,AADC,GAAA,SAAc,EAAC,GAAW,EACjB,CAAC,EAAE,CAOvB,GAAgB,GAAA,aAAW,EAAC,SAAU,CAAK,EAC7C,IAAI,EAAK,EAAM,EAAE,CACf,EAAiB,EAAM,cAAc,CACrC,EAAa,GAAA,SAAwB,EAAC,EAAO,IAC/C,GAAI,EACF,OAAO,EAAmB,GAAA,SAAa,EAAC,CACtC,GAAI,EACJ,eAAgB,CAClB,EAAG,IAEL,IAAI,EVvLC,EAAO,CANd,AAAK,GAAA,WAAS,IAEP,AADI,OAAO,YAAY,CAAC,OAAO,CAAC,eACxB,OAAO,QAAQ,EAAI,UAAU,QAAQ,CAF3B,QAMF,EAAI,EAAO,CAAC,QAAQ,CUwLzC,OAAO,CAAO,CAAC,EAAG,CAAG,CAAO,CAAC,EAAG,CAAG,EACrC,EAAG,CAAC,EAAmB,EACnB,GAAU,GAAA,UAAM,EAAC,CAAC,GAAW,MAAA,EAAmC,KAAK,EAAI,EAAK,MAAM,CAAC,EACjF,EAAQ,GAAA,SAAiB,EAAe,GAAA,SAAmB,IAAG,IAAI,CAAC,SAAS,EAAQ,CAAK,MACvF,EACO,EAAQ,EACnB,MAAO,GAAA,SAAmB,IAAG,IAAI,CAAC,SAAkB,CAAQ,EAC1D,OAAU,OAAQ,EAAS,IAAI,CAAG,EAAS,IAAI,EAC7C,KAAK,EAIH,OAHkC,EAAS,AAAnC,GAAA,SAAc,EAAC,EAAO,EAAkB,CAAC,EAAE,CACnD,GAAe,CAAA,GACf,EAAS,IAAI,CAAG,EACT,MAAA,GAAoC,AAAmC,OAAlC,CAAA,EAAgB,EAAK,OAAO,AAAD,GAAe,AAAkB,KAAK,IAAvB,EAA2B,KAAK,EAAI,EAAc,IAAI,CAAC,EAAM,GAAU,CAAC,EAAG,AAAC,CAAA,MAAA,EAAqC,KAAK,EAAI,EAAM,QAAQ,AAAD,GAAO,CAAA,MAAA,EAAqC,KAAK,EAAI,EAAM,MAAM,AAAD,GAAM,EAAE,EACvS,KAAK,EAGH,OAFA,EAAgB,EAAS,IAAI,CAC7B,GAAe,CAAA,GACR,EAAS,MAAM,CAAC,SAAU,GACnC,KAAK,EACL,IAAK,MACH,OAAO,EAAS,IAAI,GACxB,CACF,EAAG,GACL,IACO,SAAU,CAAE,EACjB,OAAO,EAAM,KAAK,CAAC,IAAI,CAAE,WAC3B,GACG,CACH,kBAAmB,CAAA,EACnB,mBAAoB,CAAA,EACpB,sBAAuB,CAAA,CACzB,GACA,GAAO,GAAQ,IAAI,CACnB,GAAS,GAAQ,MAAM,CACvB,GAAY,GAAQ,SAAS,CAC/B,GAAA,WAAS,EAAC,WACR,GAAe,IAEjB,EAAG,CAAC,GAAU,EACd,IACE,GAAQ,AADU,GAAA,eAAY,IACR,KAAK,CAC7B,GAAA,WAAS,EAAC,WACR,OAAO,WACD,cAAiB,KAAK,GAAM,MAAM,CAAC,IACzC,EAEF,EAAG,EAAE,EAIL,IAAI,GAAQ,AAHO,GAAA,SAAO,EAAC,WACzB,OAAO,GAAY,IAAS,CAAA,MAAA,EAAqC,KAAK,EAAI,EAAM,QAAQ,AAAD,GAAO,CAAA,MAAA,EAAqC,KAAK,EAAI,EAAM,MAAM,AAAD,GAAM,EAAE,CAAE,EAAM,GAAe,GACxL,EAAG,CAAC,GAAe,EAAM,EAAgB,GAAM,MAAA,EAAqC,KAAK,EAAI,EAAM,QAAQ,CAAE,MAAA,EAAqC,KAAK,EAAI,EAAM,MAAM,CAAC,GAC5I,CAAC,EAC3B,GAAa,GAAM,UAAU,CAC7B,GAAgB,GAAM,aAAa,CACnC,GAAiB,GAAM,QAAQ,CAC/B,GAAW,AAAmB,KAAK,IAAxB,GAA4B,EAAE,CAAG,GAC1C,GAA8B,MAAjB,GAAoC,EAAK,OAAO,EAC/D,CAAA,EAAU,OAAO,CAAG,CAClB,OAAQ,WACN,KACF,CACF,CAAA,EAEF,IAAI,GAAa,GAAA,SAAO,EAAC,WACvB,OAAO,GAAa,EAAS,QAAQ,EAAI,IAAK,IAAY,EAAE,CAAE,CAAA,GAChE,EAAG,CAAC,EAAS,QAAQ,CAAE,GAAS,EAC5B,GAAgB,GAAA,SAAO,EAAC,WAC1B,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,GAAW,GAAG,CAAC,SAAU,CAAI,EACrD,OAAO,EAAK,GAAG,EAAI,EAAK,IAAI,EAAI,GAClC,KACF,EAAG,CAAC,GAAW,EAGX,GAAc,EAAU,CAAC,GAAW,MAAM,CAAG,EAAE,EAAI,CAAC,EACpD,GAAyB,GAA0B,IACnD,GAAwB,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,IAClE,GAAc,GAAsB,WAAW,CAE/C,IADW,GAAsB,QAAQ,CAC3B,GAAsB,MAAM,EAC1C,GAAO,GAAA,SAAwB,EAAC,GAAuB,IACrD,GAAU,IACV,GAAW,GAAA,SAAO,EAAC,WACrB,MAAO,AAAC,CAAA,AAAY,OAAZ,IAAoB,AAAY,OAAZ,EAAe,GAAM,CAAC,EAAM,aAAa,CACvE,EAAG,CAAC,GAAS,EAAM,aAAa,CAAC,EAK7B,GAAiB,AAAgB,QAAhB,IAAyB,CAAC,GAC3C,GAAkB,GAAA,UAAc,EAAC,kBACjC,AAAI,AAAqB,KAAA,IAArB,EAAuC,IAEvC,IACA,AAAY,OAAZ,GAEN,EAAG,CACD,MAAO,EAAM,SAAS,CACtB,SAAU,CACZ,GACA,GAAmB,GAAA,SAAc,EAAC,GAAiB,GACnD,GAAY,EAAgB,CAAC,EAAE,CAC/B,GAAa,EAAgB,CAAC,EAAE,CAG9B,GAAe,GAAA,UAAI,EAAC,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAChE,UAAW,CACb,EAAG,GAAQ,CAAC,EAAG,CACb,WAAY,CACd,EAAG,IAAyB,CAAC,EAAG,CAC9B,cAAe,GACf,WAAY,GACZ,KAAM,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAO,CAAC,EAAG,CAC/C,KAAM,GAAkB,CAAA,MAAA,EAAmC,KAAK,EAAI,EAAK,IAAI,AAAD,EAC5E,QAAS,EACX,GACA,OAAQ,EACV,GAAI,CAAC,YAAa,QAAS,mBAAmB,EAG1C,GAAgB,GAAuB,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CACrE,SAAU,EAAS,QAAQ,AAC7B,EAAG,IAAe,CAAC,EAAG,CACpB,cAAe,EACjB,GAAI,GAGA,GAAkB,GAAmB,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,IAAe,CAAC,EAAG,CAC1F,iBAAkB,EAAM,gBAAgB,CACxC,cAAe,EACjB,GAAI,GAGA,GAAe,GAAgB,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,IAAe,CAAC,EAAG,CACpF,SAAU,GACV,WAAY,GACZ,SAAU,GACV,UAAW,EACb,GAAI,IAGA,GAAY,GAAa,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,IAAe,CAAC,EAAG,CAC9E,SAAU,KACV,aAAc,CAAC,CAAC,GAChB,SAAU,GACV,SAAU,GACV,UAAW,GACX,WAAY,EACd,GAAI,IAGA,GAhTJ,AAAI,AAAuB,CAAA,IAAvB,CADmC,EAiTV,GAAA,SAAa,EAAC,CACzC,SAAU,GACV,UAAW,EACb,EAAG,KAnTO,YAAY,EAAc,EAAM,IAAI,CACrC,KAEL,EAAM,YAAY,CACb,EAAM,YAAY,CAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAqB,GAAA,KAAI,EAAC,gBAAM,CAAE,CAAC,IAE1E,KA+SL,GAA0B,AADV,GAAA,YAAU,EAAC,eAAY,EACD,gBAAgB,CAGpD,GAAmB,AAA0B,KAAA,IAA1B,EAAsC,EAAwB,GACjF,GAAqB,GAAG,MAAM,CAAC,EAAW,WAC1C,MLhMG,WAAY,EAAC,YAAa,SAAU,CAAK,EAC9C,IAAI,EAAiB,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,CAAC,EAAG,CAC/D,aAAc,IAAI,MAAM,CK8LH,GL7LvB,GACA,MAAO,CAAC,GAAkB,GAAiB,GAAgB,GAAgB,CAC7E,GK4LE,GAAU,GAAU,OAAO,CAC3B,GAAS,GAAU,MAAM,CAGvB,GAAY,GAAA,UAAU,EAAC,EAAM,SAAS,CAAE,GAAQ,iBAAkB,GAAoB,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,GAAA,SAAe,EAAC,CAAC,EAAG,UAAU,MAAM,CAAC,IAAU,IAAU,GAAG,MAAM,CAAC,GAAoB,aAAc,AAAgB,QAAhB,IAAwB,GAAG,MAAM,CAAC,GAAoB,gBAAiB,IAAmB,GAAG,MAAM,CAAC,GAAoB,iBAAkB,IAAc,GAAG,MAAM,CAAC,GAAoB,KAAK,MAAM,CAAC,IAAc,KAG3c,GAAyC,GA7OpC,AA6OoD,GA7OxC,GA6OmD,EA3OjE,EA8OH,GAAiB,CACnB,SAAU,UACZ,EAGI,CAAA,IAAoB,GAAgB,EAAa,SAAS,AAAD,GAC3D,CAAA,GAAe,SAAS,CAAG,CAAA,EAI7B,GAAA,WAAS,EAAC,WACR,IAAI,EACJ,AAA+C,OAA9C,CAAA,EAAsB,EAAM,YAAY,AAAD,GAAe,AAAwB,KAAK,IAA7B,GAAkC,EAAoB,IAAI,CAAC,EAAO,EAAM,QAAQ,EAEzI,EAAG,CAAC,EAAS,QAAQ,CAAE,AAA6C,OAA5C,CAAA,EAAqB,EAAS,QAAQ,AAAD,GAAe,AAAuB,KAAK,IAA5B,EAAgC,KAAK,EAAI,EAAmB,MAAM,CAAC,EAC/I,IAAI,GAAa,GAAA,UAAQ,EAAC,CAAA,GACxB,GAAa,GAAA,SAAc,EAAC,GAAY,GACxC,GAAmB,EAAU,CAAC,EAAE,CAChC,GAAsB,EAAU,CAAC,EAAE,CAIjC,GAAa,GAAA,UAAQ,EAAC,GACxB,GAAa,GAAA,SAAc,EAAC,GAAY,GACxC,GAAmB,EAAU,CAAC,EAAE,CAChC,GAAsB,EAAU,CAAC,EAAE,CC7YK,ED8YV,EAAM,KAAK,EAAI,CAAA,EC7Y3C,EAAY,AAA8B,UAA9B,OAAO,AD6YN,GC7YgB,QAAQ,CAAgB,AD6YxC,GC7YkD,KAAK,CAAG,EAC3E,GAAA,WAAS,EAAC,WACJ,GAAA,WAAS,KAAM,GACjB,CAAA,SAAS,KAAK,CAAG,CAAQ,EAE7B,EAAG,CAAC,ADwYa,GCxYH,KAAK,CAAE,EAAU,EDyY/B,IACE,GAAQ,AADS,GAAA,YAAU,EAAC,aAAW,EAClB,KAAK,CACxB,GAAiB,GAAA,SAAO,EAAC,kBAC3B,AAAI,GAAmB,EAAgB,MAAM,CAAG,EACvC,MAAA,EAAyD,KAAK,EAAI,EAAgB,GAAG,CAAC,SAAU,CAAI,CAAE,CAAK,EAChH,MAAoB,GAAA,KAAI,EAAC,MAAO,CAC9B,IAAK,EAAK,GAAG,CACb,MAAO,GAAA,SAAa,EAAC,CACnB,SAAU,UACZ,EAAG,EACL,EAAG,GACL,GAEK,KACT,EAAG,CAAC,EAAgB,EACpB,OAAO,GAAsB,GAAA,KAAI,EAAC,eAAY,CAAC,QAAQ,CAAE,CACvD,MAAO,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,IAAe,CAAC,EAAG,CACxD,WAAY,GACZ,SAAU,GACV,SAAU,GACV,UAAW,GACX,iBAAkB,GAClB,oBAAqB,GACrB,iBAAkB,CAAA,EAClB,MAAO,GAAc,QAAQ,CAC7B,aAAc,CAAC,CAAC,GAChB,UAAW,CAAC,CAAC,GACb,WAAY,GACZ,UAAW,CAAC,CAAC,GACb,iBAAkB,GAClB,oBAAqB,GACrB,cAAe,GACf,WAAY,GACZ,cAAe,GACf,YAAa,EACf,GACA,SAAU,EAAM,IAAI,CAAgB,GAAA,KAAI,EAAC,UAAS,CAAE,CAClD,SAAU,CACZ,GAAkB,GAAA,MAAK,EAAC,MAAO,CAC7B,UAAW,GACX,SAAU,CAAC,IAAkB,AAAmC,OAAlC,CAAA,EAAgB,GAAM,MAAM,AAAD,GAAe,AAAkB,KAAK,IAAvB,GAA4B,EAAc,QAAQ,CAAgB,GAAA,KAAI,EAAC,MAAO,CACpJ,UAAW,GAAA,UAAU,EAAC,GAAG,MAAM,CAAC,GAAoB,YAAa,IACjE,SAAU,EACZ,GAAK,KAAmB,GAAA,MAAK,EAAC,UAAM,CAAE,CACpC,MAAO,GAAA,SAAa,EAAC,CACnB,UAAW,OAEX,cAAe,GAAe,MAAQ,KAAA,CACxC,EAAG,GACH,SAAU,CAAc,GAAA,KAAI,EAAC,UAAc,CAEzC,CACA,MAAO,CACL,OAAQ,GAAA,gBAAc,IACtB,MAAO,CACL,gBAAiB,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,GAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,UAAU,AAAD,GAAO,CAAA,MAAA,GAAqC,KAAK,EAAI,GAAM,eAAe,AAAD,CACvR,EACA,WAAY,CACV,KAAM,GAAA,iBAAe,EAAC,CACpB,YAAa,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,GAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,mBAAmB,AAAD,GAAM,cACvN,eAAgB,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,GAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,mBAAmB,AAAD,GAAM,cAC1N,WAAY,GAAM,YAAY,CAC9B,oBAAqB,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,GAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,uBAAuB,AAAD,GAAO,CAAA,MAAA,GAAqC,KAAK,EAAI,GAAM,gBAAgB,AAAD,EACvS,iBAAkB,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,GAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,oBAAoB,AAAD,GAAO,CAAA,MAAA,GAAqC,KAAK,EAAI,GAAM,gBAAgB,AAAD,EACjS,kBAAmB,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,GAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,qBAAqB,AAAD,GAAO,CAAA,MAAA,GAAqC,KAAK,EAAI,GAAM,iBAAiB,AAAD,EACpS,8BAA+B,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,GAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,uBAAuB,AAAD,GAAO,CAAA,MAAA,GAAqC,KAAK,EAAI,GAAM,gBAAgB,AAAD,EACjT,oBAAqB,EACrB,qBAAsB,EACtB,yBAA0B,EAC1B,cAAe,AAAC,CAAA,AAAoC,OAAnC,CAAA,EAAiB,GAAM,MAAM,AAAD,GAAe,AAAmB,KAAK,IAAxB,GAA6B,AAA4C,OAA3C,CAAA,EAAiB,EAAe,KAAK,AAAD,GAAe,AAAmB,KAAK,IAAxB,EAA4B,KAAK,EAAI,EAAe,aAAa,AAAD,GAAO,CAAA,MAAA,GAAqC,KAAK,EAAI,GAAM,kBAAkB,AAAD,EACzR,mBAAoB,AAAC,CAAA,AAAqC,OAApC,CAAA,EAAkB,GAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA8C,OAA7C,CAAA,EAAkB,EAAgB,KAAK,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,sBAAsB,AAAD,GAAM,sBAEvO,sBAAuB,AAAC,CAAA,AAAqC,OAApC,CAAA,EAAkB,GAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAA8C,OAA7C,CAAA,EAAkB,EAAgB,KAAK,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,qBAAqB,AAAD,GAAM,mBACzO,QAAS,MAAA,GAAqC,KAAK,EAAI,GAAM,eAAe,CAC5E,cAAe,MAAA,GAAqC,KAAK,EAAI,GAAM,eAAe,CAClF,kBAAmB,cACnB,YAAa,MAAA,GAAqC,KAAK,EAAI,GAAM,eAAe,AAClF,EACF,CACF,EACA,SAAU,EACZ,GAAiB,GAAA,MAAK,EAAC,MAAO,CAC5B,MAAO,GACP,UAAW,GAAG,MAAM,CAAC,GAAoB,eAAe,MAAM,CAAC,IAAQ,IAAI,GAC3E,SAAU,CAAC,GAAwB,GAAA,KAAI,EAAC,GAAa,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAC/E,iBAAkB,GAClB,iBAAkB,EACpB,EAAG,IAAO,CAAC,EAAG,CACZ,UAAW,CAAC,CAAC,GACb,UAAW,GACX,MAAO,EACP,SAAU,EAAuB,GAAA,KAAI,EAAC,cAAW,CAAE,CAAC,GAAK,CAC3D,IAAK,GAAW,IAAiC,GAAA,KAAI,EAAC,MAAO,CAC3D,UAAW,GAAG,MAAM,CAAC,GAAoB,eACzC,MAAO,CACL,OAAQ,GACR,iBAAkB,AAAqC,OAApC,CAAA,EAAkB,GAAM,MAAM,AAAD,GAAe,AAAoB,KAAK,IAAzB,GAA8B,AAAsD,OAArD,CAAA,EAAkB,EAAgB,aAAa,AAAD,GAAe,AAAoB,KAAK,IAAzB,EAA6B,KAAK,EAAI,EAAgB,gCAAgC,AACnP,CACF,GAAG,AACL,GAAG,AACL,GAAG,AACL,EACF,IACF,EACI,GAAY,SAAmB,CAAK,EACtC,IAAI,EAAe,EAAM,YAAY,CACjC,EAAY,AAAmB,KAAA,IAAnB,EAAM,QAAQ,CAAiB,CAC7C,KAAM,AAAmB,aAAnB,EAAM,QAAQ,AACtB,EAAI,CAAC,EACL,MAAoB,GAAA,KAAI,EAAC,UAAc,CAAE,CACvC,MAAO,EAAe,CACpB,MAAO,CACL,aAAc,CAChB,CACF,EAAI,KAAA,EACJ,SAAuB,GAAA,KAAI,EAAC,mBAAiB,CAAE,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAY,CAAC,EAAG,CAC7F,MAAO,EAAM,KAAK,CAClB,UAAW,EAAM,SAAS,CAC1B,SAAuB,GAAA,KAAI,EAAC,GAAe,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CACrE,KAAmB,GAAA,KAAI,EAAC,GAAM,CAAC,EACjC,EAAG,IAAkB,CAAC,EAAG,CACvB,SAAU,GAAA,WAAS,IAAK,OAAO,QAAQ,CAAG,KAAA,CAC5C,EAAG,GACL,GACF,GACF,EEzgBA,IAAM,GAAqB,IAEvB,WAAC,OACC,MAAM,6BACN,MAAM,KACN,OAAO,KACP,QAAQ,wBAER,WAAC,kBACC,WAAC,kBACC,GAAG,mBACH,GAAG,UACH,GAAG,WACH,GAAG,KACH,GAAG,oBAEH,UAAC,QAAK,OAAO,KAAK,UAAU,YAC5B,UAAC,QAAK,OAAO,OAAO,UAAU,eAEhC,WAAC,kBACC,GAAG,mBACH,GAAG,UACH,GAAG,UACH,GAAG,KACH,GAAG,qBAEH,UAAC,QAAK,OAAO,KAAK,UAAU,YAC5B,UAAC,QAAK,OAAO,SAAS,UAAU,YAChC,UAAC,QAAK,OAAO,OAAO,UAAU,eAEhC,WAAC,kBACC,GAAG,mBACH,GAAG,UACH,GAAG,UACH,GAAG,WACH,GAAG,qBAEH,UAAC,QAAK,OAAO,KAAK,UAAU,YAC5B,UAAC,QAAK,OAAO,UAAU,UAAU,YACjC,UAAC,QAAK,OAAO,OAAO,UAAU,eAEhC,WAAC,kBACC,GAAG,mBACH,GAAG,UACH,GAAG,SACH,GAAG,WACH,GAAG,qBAEH,UAAC,QAAK,OAAO,KAAK,UAAU,YAC5B,UAAC,QAAK,OAAO,UAAU,UAAU,YACjC,UAAC,QAAK,OAAO,OAAO,UAAU,kBAGlC,UAAC,KAAE,KAAK,OAAO,SAAS,UAAU,OAAO,OAAO,YAAY,aAC1D,UAAC,KAAE,UAAU,8BACX,UAAC,KAAE,UAAU,4BACX,WAAC,eACC,WAAC,KAAE,SAAS,oBACV,WAAC,eACC,UAAC,QACC,KAAK,yBACL,EAAE,8bAEJ,UAAC,QACC,KAAK,yBACL,EAAE,0ZAGN,UAAC,QACC,KAAK,yBACL,EAAE,qRAGN,UAAC,WACC,GAAG,UACH,GAAG,UACH,KAAK,yBACL,GAAG,OACH,GAAG,uDC5EnB,IAAM,GAOD,AAAC,QAIH,EAEiB,QAJlB,AAAC,CAAC,EAAM,KAAK,EAAK,CAAA,EAAM,OAAO,EAAI,EAAM,QAAQ,AAAD,GAE/C,SAAA,EAAA,EAAM,KAAK,YAAX,SAAA,EAAa,YAAY,GAAK,CAAA,EAAM,YAAY,EAAI,EAAM,YAAY,AAAD,GAErE,AAAC,CAAA,CAAC,EAAM,KAAK,WAAI,EAAA,EAAM,KAAK,YAAX,SAAA,EAAa,YAAY,CAAD,GACxC,UAAC,UAAM,EACL,OAAQ,EAAM,KAAK,CAAG,MAAQ,MAC9B,MAAO,EAAM,KAAK,CAAG,MAAQ,MAC7B,SAAU,EAAM,KAAK,CAAG,qEAAgB,2EACxC,MACE,UAAC,UAAM,EAAC,KAAK,UAAU,QAAS,IAAM,SAAO,CAAC,IAAI,CAAC,cAAM,gCAO/D,EAAM,QAAQ,uHCrBZ,GAAuB,EAAM,UAAU,CANtB,SAAwB,CAAK,CAAE,CAAG,EACrD,OAAoB,EAAM,aAAa,CAAC,UAAQ,CAAE,GAAA,SAAa,EAAC,GAAA,SAAa,EAAC,CAAC,EAAG,GAAQ,CAAC,EAAG,CAC5F,IAAK,EACL,KAAM,UAAiB,AACzB,IACF,qCCYA,IAAM,GAAe,CAAC,EAAkB,KACtC,GAAI,AAAkB,IAAlB,EAAO,MAAM,CACf,MAAO,EAAE,CAGX,IAAI,EAAY,EAAE,CAClB,IAAK,IAAM,KAAS,EAAQ,CAC1B,IAAM,EAAW,CAAC,GAAG,CAAK,AAAC,EACvB,EAAS,GACP,MAAM,OAAO,CAAC,EAAS,MAAM,GAC/B,EAAU,IAAI,IAAI,GAAa,EAAS,MAAM,CAAE,KAG9C,MAAM,OAAO,CAAC,EAAS,QAAQ,IACjC,EAAS,QAAQ,CAAG,GAAa,EAAS,QAAQ,CAAE,GACpD,EAAS,MAAM,CAAG,EAAS,QAAQ,EAErC,EAAU,IAAI,CAAC,IAEnB,CAEA,OAAO,EACT,EAGM,GAAY,AAAC,GACjB,AAAI,AAAkB,IAAlB,EAAO,MAAM,CACR,EAAE,CAEJ,EAAO,GAAG,CAAC,IAEhB,IAAM,EAAW,CAAC,GAAG,CAAK,EAa1B,OAZI,EAAM,UAAU,EAClB,CAAA,EAAS,IAAI,CAAG,EAAM,UAAU,AAAD,EAG7B,MAAM,OAAO,CAAC,EAAM,MAAM,GAC5B,CAAA,EAAS,MAAM,CAAG,GAAU,EAAM,MAAM,CAAA,EAGtC,MAAM,OAAO,CAAC,EAAM,QAAQ,GAC9B,CAAA,EAAS,QAAQ,CAAG,GAAU,EAAM,QAAQ,CAAA,EAGvC,EACT,OAGF,GAAe,AAAC,IACd,IAAM,EAAW,GAAA,aAAW,IACtB,EAAW,GAAA,aAAW,IACtB,CAAE,aAAA,CAAY,CAAE,cAAA,CAAa,CAAE,CAAG,GAAA,YAAU,IAC5C,EAAc,AAAC,WAAQ,EAAI,GAAA,WAAQ,EAAC,mBAAsB,CAC9D,aAAc,KAAA,EACd,QAAS,CAAA,EACT,gBAAiB,IACnB,EACM,CAAE,aAAA,CAAY,CAAE,QAAA,CAAO,CAAE,gBAAA,CAAe,CAAE,CAAG,EAC7C,EAAa,CACnB,OAAU,CAAA,EACV,SAAY,QACZ,aAAgB,UAChB,OAAU,OACV,aAAgB,QAChB,YAAe,CAAA,EACf,YAAe,CAAA,EACf,UAAa,CAAA,EACb,MAAS,mDACT,IAAO,CAAA,EACP,KAAQ,YACR,YAAe,GACf,MAAS,CAAC,CACZ,EAEQ,EAAgB,EAAc,YAAY,CAAC,CAC/C,IAAK,SACL,KAAM,SACN,aAAc,CACZ,GAAG,CAAW,AAChB,CACF,GAIM,EAAY,GAAa,EAAa,MAAM,CAAC,GAAS,AAAa,0BAAb,EAAM,EAAE,EAA+B,AAAC,GAC3F,AAAC,CAAC,CAAC,EAAM,QAAQ,EAAI,AAAa,0BAAb,EAAM,EAAE,EAAiC,CAAC,CAAC,EAAM,SAAS,EAElF,CAAC,EAAM,CAAG,GAAA,wBAAqB,EAAC,GAAU,IAE1C,EAAe,GAAA,SAAO,EAAC,SAAM,EAAA,EAAA,iBAAA,EAAA,GAAA,aAAW,EAAC,EAAM,QAAQ,CAAE,EAAS,QAAQ,aAA7C,iBAAA,EAAA,EAAgD,GAAG,YAAnD,iBAAA,EAAA,OAAA,cAAA,SAAA,EAAyD,KAAK,GAAE,CAAC,EAAS,QAAQ,CAAC,EAEtH,MACE,UAAC,IACC,MAAO,EACP,SAAU,EACV,MAAO,EAAW,KAAK,EAAI,oBAC3B,SAAS,OACT,WAAY,IACZ,kBAAmB,AAAC,IAClB,EAAE,eAAe,GACjB,EAAE,cAAc,GAChB,EAAS,KACX,EACA,cAAe,EAAW,aAAa,EA9BvB,KAAA,EA+BhB,KAAM,CAAE,OAAQ,EAAW,MAAM,AAAC,EAClC,KAAM,GACN,eAAgB,CAAC,EAAe,IAC9B,AAAI,EAAc,KAAK,EAAI,EAAc,QAAQ,CACxC,EAEL,EAAc,IAAI,EAAI,EAAS,QAAQ,GAAK,EAAc,IAAI,CAG9D,UAAC,MAAI,EAAC,GAAI,EAAc,IAAI,CAAC,OAAO,CAAC,KAAM,IAAK,OAAQ,EAAc,MAAM,UACzE,IAIA,EAET,WAAY,CAAC,EAAO,EAAG,KACrB,GAAM,CAAE,eAAA,CAAc,CAAE,MAAA,CAAK,CAAE,KAAA,CAAI,CAAE,CAAG,EAClC,EAAQ,GAAS,EACjB,EAAO,CAAM,CAAC,EAAO,MAAM,CAAG,EAAE,QACtC,AAAI,GACE,CAAA,EAAK,IAAI,GAAK,GAAQ,EAAK,QAAQ,GAAK,CAAG,EACtC,UAAC,iBAAM,IAGX,UAAC,MAAI,EAAC,GAAI,WAAO,IAC1B,EACA,oBAAoB,IACpB,WAAW,IACX,WAAW,IACV,GAAG,CAAa,CACjB,mBACE,AAAqC,CAAA,IAArC,EAAc,kBAAkB,EAC/B,CAAA,AAAC,IACA,IAAM,EAAM,AC1Jf,SAAgC,CAKrC,MASmB,EAA6B,EACvB,EAWX,EAMyB,MAmCnC,EA7DJ,GAAI,EAAK,aAAa,CAAC,WAAW,CAChC,OAAO,EAAK,aAAa,CAAC,WAAW,CACnC,EAAK,YAAY,CACjB,EAAK,eAAe,CACpB,EAAK,aAAa,EAItB,IAAM,EAAa,SAAA,EAAA,EAAK,YAAY,YAAjB,SAAA,EAAmB,MAAM,YAAI,EAAA,EAAK,YAAY,YAAjB,SAAA,EAAmB,IAAI,GAAI,EAAK,aAAa,CAAC,MAAM,CAC9F,EAAmB,SAAA,EAAA,EAAK,YAAY,YAAjB,SAAA,EAAmB,MAAM,IAAK,CAAA,EAEjD,EACJ,EACE,WAAC,QAAK,UAAU,qCACb,AAAC,EAWI,KATF,UAAC,UAAM,EACL,KAAK,QACL,UAAU,2BACV,IACE,SAAA,EAAA,EAAK,YAAY,YAAjB,SAAA,EAAmB,MAAM,GACzB,iFAEF,IAAI,WAGV,UAAC,QAAK,UAhBU,EAAmB,2DAA6D,0CAgB/D,EAAA,EAAK,YAAY,YAAjB,SAAA,EAAmB,IAAI,MAExD,KAGN,GAAI,EAAK,OAAO,CACd,MACE,UAAC,OAAI,UAAU,mCACb,UAAC,UAAI,EAAC,KAAK,QAAQ,MAAQ,CAAE,WAAY,EAAG,YAAa,CAAE,MAM/D,GAAG,CAAC,EAAQ,OAAO,KAErB,IAAM,EAAW,CACf,UAAW,yBACX,aAAc,EAAE,CAChB,MAAO,CACL,CACE,IAAK,SACL,MACE,iCACE,UAAC,OAAiB,8BAItB,QAAS,SACP,EAAA,QAAA,WAAA,EAAA,EAAM,aAAa,YAAnB,WAAA,EAAA,EAAqB,MAAM,YAA3B,GAAA,OAAA,EAA8B,EAAK,YAAY,EACjD,CACF,EACD,AACH,EAuBA,OAnBE,EADE,UAAO,CAAC,UAAU,CAAC,OAAS,UAAO,CAAC,UAAU,CAAC,SACjC,CAAE,KAAM,CAAS,EACxB,UAAO,CAAC,UAAU,CAAC,MACZ,CACd,QACE,UAAC,UAAI,WACF,EAAS,KAAK,CAAC,GAAG,CAAC,AAAC,GACnB,UAAC,UAAI,CAAC,IAAI,EAAgB,QAAS,EAAK,OAAO,UAC5C,EAAK,KAAK,EADG,EAAK,GAAG,IAMhC,EAEgB,CAAE,QAAS,UAAC,UAAI,EAAE,GAAG,CAAQ,EAAK,EAMlD,UAAC,OAAI,UAAU,2CACZ,EAAK,aAAa,CAAC,MAAM,CACxB,UAAC,UAAQ,EAAE,GAAG,CAAa,CAAE,iBAAiB,uCAC3C,IAGH,IAIR,EDuD4C,CAChC,cAAA,EACA,QAAA,EACA,aAAA,EACA,gBAAA,CACF,UACA,AAAI,EAAc,kBAAkB,CAC3B,EAAc,kBAAkB,CAAC,EAAa,EAAK,CAExD,WAAA,EACA,cAAA,EACA,QAAA,EACA,aAAA,EACA,gBAAA,CACF,GAEK,EACT,CAAA,WAGF,UAAC,IACC,MAAO,EACP,OAAO,OAAE,SAAA,EAAe,OAAO,CAC/B,QAAQ,OAAE,SAAA,EAAe,QAAQ,CACjC,YAAY,OAAE,SAAA,EAAe,YAAY,CACzC,YAAY,OAAE,SAAA,EAAe,YAAY,UAExC,EAAc,cAAc,CACzB,EAAc,cAAc,CAAC,UAAC,QAAM,KAAK,GACzC,UAAC,QAAM,SAKnB"}