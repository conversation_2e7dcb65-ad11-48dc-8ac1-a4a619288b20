(("undefined"!=typeof globalThis?globalThis:self)["makoChunk_teamauth-frontend"]=("undefined"!=typeof globalThis?globalThis:self)["makoChunk_teamauth-frontend"]||[]).push([["d273031b"],{d273031b:function(e,t,n){n.d(t,"__esModule",{value:!0}),n.e(t,{default:function(){return og;}});var o,a,l,i=n("777fffbe"),r=n("852bbaa9"),d=n("87723398"),u=n("3a3f20d4"),s=n("2d45ae60"),c=r._(s),p=n("3c077b9c"),f=i._(p),m=n("c7c3b00f"),h=i._(m),v=n("4ae1fa7d"),g=i._(v),y=n("5e9893d8"),x=i._(y),b=n("f3b4956f"),C=i._(b),k=n("6dd0d42e"),j=i._(k),w=n("cc7e375b"),S=n("3f6f84d7"),M=n("c2cfd219"),I=n("33528d98");function R(e){var t="undefined"==typeof window,n=(0,c.useState)(function(){return!t&&window.matchMedia(e).matches;}),o=(0,C.default)(n,2),a=o[0],l=o[1];return(0,c.useLayoutEffect)(function(){if(!t){var n=window.matchMedia(e),o=function(e){return l(e.matches);};return n.addListener(o),function(){return n.removeListener(o);};}},[e]),a;}var B={xs:{maxWidth:575,matchMedia:"(max-width: 575px)"},sm:{minWidth:576,maxWidth:767,matchMedia:"(min-width: 576px) and (max-width: 767px)"},md:{minWidth:768,maxWidth:991,matchMedia:"(min-width: 768px) and (max-width: 991px)"},lg:{minWidth:992,maxWidth:1199,matchMedia:"(min-width: 992px) and (max-width: 1199px)"},xl:{minWidth:1200,maxWidth:1599,matchMedia:"(min-width: 1200px) and (max-width: 1599px)"},xxl:{minWidth:1600,matchMedia:"(min-width: 1600px)"}},N=function(){var e=void 0;return"undefined"==typeof window?e:e=Object.keys(B).find(function(e){var t=B[e].matchMedia;return!!window.matchMedia(t).matches;});},T=function(){var e=R(B.md.matchMedia),t=R(B.lg.matchMedia),n=R(B.xxl.matchMedia),o=R(B.xl.matchMedia),a=R(B.sm.matchMedia),l=R(B.xs.matchMedia),i=(0,c.useState)(N()),r=(0,C.default)(i,2),d=r[0],u=r[1];return(0,c.useEffect)(function(){if(n){u("xxl");return;}if(o){u("xl");return;}if(t){u("lg");return;}if(e){u("md");return;}if(a){u("sm");return;}if(l){u("xs");return;}u("md");},[e,t,n,o,a,l]),d;},O=n("a8927710"),E=n("8e3dd103");function H(e,t){return t>>>e|t<<32-e;}var P=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298];function A(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n;}function L(){var e,t,n,a,i,r,d,u,s,c,p,f,m,h,v,g,y=Array(16);d=o[0],u=o[1],s=o[2],c=o[3],p=o[4],f=o[5],m=o[6],h=o[7];for(var x=0;x<16;x++)y[x]=l[(x<<2)+3]|l[(x<<2)+2]<<8|l[(x<<2)+1]<<16|l[x<<2]<<24;for(var b=0;b<64;b++)v=h+(H(6,e=p)^H(11,e)^H(25,e))+((t=p)&f^~t&m)+P[b],b<16?v+=y[b]:v+=function(e,t){var n,o;return e[15&t]+=(H(17,n=e[t+14&15])^H(19,n)^n>>>10)+e[t+9&15]+(H(7,o=e[t+1&15])^H(18,o)^o>>>3);}(y,b),g=(H(2,n=d)^H(13,n)^H(22,n))+((a=d)&(i=u)^a&(r=s)^i&r),h=m,m=f,f=p,p=A(c,v),c=s,s=u,u=d,d=A(v,g);o[0]+=d,o[1]+=u,o[2]+=s,o[3]+=c,o[4]+=p,o[5]+=f,o[6]+=m,o[7]+=h;}function z(e){return(z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e;}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e;})(e);}var D=["pro_layout_parentKeys","children","icon","flatMenu","indexRoute","routes"];function _(e){var t="function"==typeof Map?new Map:void 0;return(_=function(e){if(null===e||-1===Function.toString.call(e).indexOf("[native code]"))return e;if("function"!=typeof e)throw TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,n);}function n(){return F(e,arguments,G(this).constructor);}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),$(n,e);})(e);}function F(e,t,n){return(F=W()?Reflect.construct.bind():function(e,t,n){var o=[null];o.push.apply(o,t);var a=new(Function.bind.apply(e,o));return n&&$(a,n.prototype),a;}).apply(null,arguments);}function W(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0;}catch(e){return!1;}}function $(e,t){return($=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e;})(e,t);}function G(e){return(G=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e);})(e);}function K(e){return function(e){if(Array.isArray(e))return V(e);}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e);}(e)||U(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");}();}function U(e,t){if(e){if("string"==typeof e)return V(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return V(e,t);}}function V(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o;}function X(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable;})),n.push.apply(n,o);}return n;}function Y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?X(Object(n),!0).forEach(function(t){var o;o=n[t],t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o;}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):X(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t));});}return e;}var Z="routes";function q(e){return e.split("?")[0].split("#")[0];}var J=function(e){if(!e.startsWith("http"))return!1;try{return new URL(e),!0;}catch(e){return!1;}},Q=function(e){var t,n=e.path;if(!n||"/"===n)try{return"/".concat((t=JSON.stringify(e),o=Array(8),a=[,,],l=Array(64),a[0]=a[1]=0,o[0]=1779033703,o[1]=3144134277,o[2]=1013904242,o[3]=2773480762,o[4]=1359893119,o[5]=2600822924,o[6]=528734635,o[7]=1541459225,!function(e,t){var n,o,i=0;o=a[0]>>3&63;var r=63&t;for((a[0]+=t<<3)<t<<3&&a[1]++,a[1]+=t>>29,n=0;n+63<t;n+=64){for(var d=o;d<64;d++)l[d]=e.charCodeAt(i++);L(),o=0;}for(var u=0;u<r;u++)l[u]=e.charCodeAt(i++);}(t,t.length),!function(){var e=a[0]>>3&63;if(l[e++]=128,e<=56)for(var t=e;t<56;t++)l[t]=0;else{for(var n=e;n<64;n++)l[n]=0;L();for(var o=0;o<56;o++)l[o]=0;}l[56]=a[1]>>>24&255,l[57]=a[1]>>>16&255,l[58]=a[1]>>>8&255,l[59]=255&a[1],l[60]=a[0]>>>24&255,l[61]=a[0]>>>16&255,l[62]=a[0]>>>8&255,l[63]=255&a[0],L();}(),function(){for(var e=new String,t=0;t<8;t++)for(var n=28;n>=0;n-=4)e+="0123456789abcdef".charAt(o[t]>>>n&15);return e;}()));}catch(e){}return n?q(n):n;},ee=function(e,t){var n=e.name,o=e.locale;return(!("locale"in e)||!1!==o)&&!!n&&(e.locale||"".concat(t,".").concat(n));},et=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/";return e.endsWith("/*")?e.replace("/*","/"):(e||t).startsWith("/")||J(e)?e:"/".concat(t,"/").concat(e).replace(/\/\//g,"/").replace(/\/\//g,"/");},en=function(e,t){var n=e.menu,o=void 0===n?{}:n,a=e.indexRoute,l=e.path,i=e.children||[],r=o.name,d=void 0===r?e.name:r,u=o.icon,s=void 0===u?e.icon:u,c=o.hideChildren,p=void 0===c?e.hideChildren:c,f=o.flatMenu,m=void 0===f?e.flatMenu:f,h=a&&"redirect"!==Object.keys(a).join(",")?[Y({path:void 0===l?"":l,menu:o},a)].concat(i||[]):i,v=Y({},e);if(d&&(v.name=d),s&&(v.icon=s),h&&h.length){if(p)return delete v.children,v;var g=ea(Y(Y({},t),{},{data:h}),e);if(m)return g;delete v[Z];}return v;},eo=function(e){return Array.isArray(e)&&e.length>0;};function ea(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{path:"/"},n=e.data,o=e.formatMessage,a=e.parentName,l=e.locale;return n&&Array.isArray(n)?n.filter(function(e){return!!e&&(!!eo(e.children)||!!e.path||!!e.originPath||!!e.layout||!e.redirect&&(e.unaccessible,!1));}).filter(function(e){var t,n;return null!=e&&null!==(t=e.menu)&&void 0!==t&&!!t.name||null!=e&&!!e.flatMenu||null!=e&&null!==(n=e.menu)&&void 0!==n&&!!n.flatMenu||!1!==e.menu;}).map(function(e){var t=Y(Y({},e),{},{path:e.path||e.originPath});return!t.children&&t[Z]&&(t.children=t[Z],delete t[Z]),t.unaccessible&&delete t.name,"*"===t.path&&(t.path="."),"/*"===t.path&&(t.path="."),!t.path&&t.originPath&&(t.path=t.originPath),t;}).map(function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{path:"/"},i=n.children||n[Z]||[],r=et(n.path,t?t.path:"/"),d=n.name,u=ee(n,a||"menu"),s=!1!==u&&!1!==l&&o&&u?o({id:u,defaultMessage:d}):d,c=t.pro_layout_parentKeys,p=(t.children,t.icon,t.flatMenu,t.indexRoute,t.routes,function(e,t){if(null==e)return{};var n,o,a=function(e,t){if(null==e)return{};var n,o,a={},l=Object.keys(e);for(o=0;o<l.length;o++)n=l[o],t.indexOf(n)>=0||(a[n]=e[n]);return a;}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(o=0;o<l.length;o++)n=l[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n]);}return a;}(t,D)),f=new Set([].concat(K(void 0===c?[]:c),K(n.parentKeys||[])));t.key&&f.add(t.key);var m=Y(Y(Y({},p),{},{menu:void 0},n),{},{path:r,locale:u,key:n.key||Q(Y(Y({},n),{},{path:r})),pro_layout_parentKeys:Array.from(f).filter(function(e){return e&&"/"!==e;})});if(s?m.name=s:delete m.name,void 0===m.menu&&delete m.menu,eo(i)){var h=ea(Y(Y({},e),{},{data:i,parentName:u||""}),m);eo(h)&&(m.children=h);}return en(m,e);}).flat(1):[];}var el=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter(function(e){return e&&(e.name||eo(e.children))&&!e.hideInMenu&&!e.redirect;}).map(function(t){var n=Y({},t),o=n.children||t[Z]||[];if(delete n[Z],eo(o)&&!n.hideChildrenInMenu&&o.some(function(e){return e&&!!e.name;})){var a=e(o);if(a.length)return Y(Y({},n),{},{children:a});}return Y({},t);}).filter(function(e){return e;});},ei=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&$(e,t);}(a,e);var t,n,o=(t=W(),function(){var e,n=G(a);return e=t?Reflect.construct(n,arguments,G(this).constructor):n.apply(this,arguments),function(e,t){if(t&&("object"===z(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e;}(e);}(this,e);});function a(){return!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function");}(this,a),o.apply(this,arguments);}return n=[{key:"get",value:function(e){var t;try{var n,o=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=U(e))){n&&(e=n);var o=0,a=function(){};return{s:a,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]};},e:function(e){throw e;},f:a};}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");}var l,i=!0,r=!1;return{s:function(){n=n.call(e);},n:function(){var e=n.next();return i=e.done,e;},e:function(e){r=!0,l=e;},f:function(){try{i||null==n.return||n.return();}finally{if(r)throw l;}}};}(this.entries());try{for(o.s();!(n=o.n()).done;){var a,l=(a=n.value,function(e){if(Array.isArray(e))return e;}(a)||function(e,t){var n,o,a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var l=[],i=!0,r=!1;try{for(a=a.call(e);!(i=(n=a.next()).done)&&(l.push(n.value),2!==l.length);i=!0);}catch(e){r=!0,o=e;}finally{try{i||null==a.return||a.return();}finally{if(r)throw o;}}return l;}}(a,2)||U(a,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");}()),i=l[0],r=l[1],d=q(i);if(!J(i)&&(0,E.pathToRegexp)(d,[]).test(e)){t=r;break;}}}catch(e){o.e(e);}finally{o.f();}}catch(e){t=void 0;}return t;}}],function(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o);}}(a.prototype,n),Object.defineProperty(a,"prototype",{writable:!1}),a;}(_(Map)),er=function(e){var t=new ei;return!function e(n,o){n.forEach(function(n){var a=n.children||n[Z]||[];eo(a)&&e(a,n);var l=et(n.path,o?o.path:"/");t.set(q(l),n);});}(e),t;},ed=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.map(function(t){var n=t.children||t[Z];if(eo(n)&&e(n).length)return Y({},t);var o=Y({},t);return delete o[Z],delete o.children,o;}).filter(function(e){return e;});},eu=function(e,t,n,o){var a=ea({data:e,formatMessage:n,locale:t}),l=o?ed(a):el(a);return{breadcrumb:er(a),menuData:l};};function es(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable;})),n.push.apply(n,o);}return n;}function ec(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?es(Object(n),!0).forEach(function(t){var o;o=n[t],t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o;}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):es(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t));});}return e;}var ep=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n={};return t.forEach(function(t){var o=ec({},t);if(o&&o.key){!o.children&&o[Z]&&(o.children=o[Z],delete o[Z]);var a=o.children||[];n[q(o.path||o.key||"/")]=ec({},o),n[o.key||o.path||"/"]=ec({},o),a&&(n=ec(ec({},n),e(a)));}}),n;},ef=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;return e.filter(function(e){if("/"===e&&"/"===t)return!0;if("/"!==e&&"/*"!==e&&e&&!J(e)){var o=q(e);try{if(n&&(0,E.pathToRegexp)("".concat(o)).test(t)||(0,E.pathToRegexp)("".concat(o),[]).test(t)||(0,E.pathToRegexp)("".concat(o,"/(.*)")).test(t))return!0;}catch(e){}}return!1;}).sort(function(e,n){return e===t?10:n===t?-10:e.substr(1).split("/").length-n.substr(1).split("/").length;});},em=function(e,t,n,o){var a=ep(t),l=ef(Object.keys(a),e||"/",o);return!l||l.length<1?[]:(n||(l=[l[l.length-1]]),l.map(function(e){var t=a[e]||{pro_layout_parentKeys:"",key:""},n=new Map,o=(t.pro_layout_parentKeys||[]).map(function(e){return n.has(e)?null:(n.set(e,!0),a[e]);}).filter(function(e){return e;});return t.key&&o.push(t),o;}).flat(1));},eh=n("82874f1b"),ev=i._(eh),eg=n("d9034eee"),ey=i._(eg),ex=n("a838006a"),eb=i._(ex),eC=n("68c0d659"),ek=i._(eC),ej=n("117bce1f"),ew=i._(ej),eS=n("fc2f6a91"),eM=i._(eS);n("4af93778");var eI=n("8429ff43"),eR=r._(eI),eB=n("39204bca"),eN=i._(eB),eT=n("197c8e0e"),eO=i._(eT),eE=n("69ab2388"),eH=i._(eE),eP=n("b203d523"),eA=i._(eP),eL=n("8806ed77"),ez=i._(eL),eD=n("d21331c7"),e_=i._(eD),eF=function(e){(0,eA.default)(n,e);var t=(0,ez.default)(n);function n(){var e;(0,eN.default)(this,n);for(var o=arguments.length,a=Array(o),l=0;l<o;l++)a[l]=arguments[l];return e=t.call.apply(t,[this].concat(a)),(0,f.default)((0,eH.default)(e),"state",{hasError:!1,errorInfo:""}),e;}return(0,eO.default)(n,[{key:"componentDidCatch",value:function(e,t){console.log(e,t);}},{key:"render",value:function(){return this.state.hasError?(0,d.jsx)(e_.default,{status:"error",title:"Something went wrong.",extra:this.state.errorInfo}):this.props.children;}}],[{key:"getDerivedStateFromError",value:function(e){return{hasError:!0,errorInfo:e.message};}}]),n;}(c.default.Component),eW=function(e){var t=(0,c.useContext)(w.ProProvider).hashId,n=e.style,o=e.prefixCls,a=e.children,l=e.hasPageContainer,i=(0,eb.default)("".concat(o,"-content"),t,(0,f.default)((0,f.default)({},"".concat(o,"-has-header"),e.hasHeader),"".concat(o,"-content-has-page-container"),(void 0===l?0:l)>0)),r=e.ErrorBoundary||eF;return!1===e.ErrorBoundary?(0,d.jsx)(ey.default.Content,{className:i,style:n,children:a}):(0,d.jsx)(r,{children:(0,d.jsx)(ey.default.Content,{className:i,style:n,children:a})});},e$=function(){return(0,d.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 200 200",children:[(0,d.jsxs)("defs",{children:[(0,d.jsxs)("linearGradient",{x1:"62.1023273%",y1:"0%",x2:"108.19718%",y2:"37.8635764%",id:"linearGradient-1",children:[(0,d.jsx)("stop",{stopColor:"#4285EB",offset:"0%"}),(0,d.jsx)("stop",{stopColor:"#2EC7FF",offset:"100%"})]}),(0,d.jsxs)("linearGradient",{x1:"69.644116%",y1:"0%",x2:"54.0428975%",y2:"108.456714%",id:"linearGradient-2",children:[(0,d.jsx)("stop",{stopColor:"#29CDFF",offset:"0%"}),(0,d.jsx)("stop",{stopColor:"#148EFF",offset:"37.8600687%"}),(0,d.jsx)("stop",{stopColor:"#0A60FF",offset:"100%"})]}),(0,d.jsxs)("linearGradient",{x1:"69.6908165%",y1:"-12.9743587%",x2:"16.7228981%",y2:"117.391248%",id:"linearGradient-3",children:[(0,d.jsx)("stop",{stopColor:"#FA816E",offset:"0%"}),(0,d.jsx)("stop",{stopColor:"#F74A5C",offset:"41.472606%"}),(0,d.jsx)("stop",{stopColor:"#F51D2C",offset:"100%"})]}),(0,d.jsxs)("linearGradient",{x1:"68.1279872%",y1:"-35.6905737%",x2:"30.4400914%",y2:"114.942679%",id:"linearGradient-4",children:[(0,d.jsx)("stop",{stopColor:"#FA8E7D",offset:"0%"}),(0,d.jsx)("stop",{stopColor:"#F74A5C",offset:"51.2635191%"}),(0,d.jsx)("stop",{stopColor:"#F51D2C",offset:"100%"})]})]}),(0,d.jsx)("g",{stroke:"none",strokeWidth:1,fill:"none",fillRule:"evenodd",children:(0,d.jsx)("g",{transform:"translate(-20.000000, -20.000000)",children:(0,d.jsx)("g",{transform:"translate(20.000000, 20.000000)",children:(0,d.jsxs)("g",{children:[(0,d.jsxs)("g",{fillRule:"nonzero",children:[(0,d.jsxs)("g",{children:[(0,d.jsx)("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C99.2571609,26.9692191 101.032305,26.9692191 102.20193,28.1378823 L129.985225,55.8983314 C134.193707,60.1033528 141.017005,60.1033528 145.225487,55.8983314 C149.433969,51.69331 149.433969,44.8756232 145.225487,40.6706018 L108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-1)"}),(0,d.jsx)("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C100.999864,25.6271836 105.751642,20.541824 112.729652,19.3524487 C117.915585,18.4685261 123.585219,20.4140239 129.738554,25.1889424 C125.624663,21.0784292 118.571995,14.0340304 108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-2)"})]}),(0,d.jsx)("path",{d:"M153.685633,135.854579 C157.894115,140.0596 164.717412,140.0596 168.925894,135.854579 L195.959977,108.842726 C200.659183,104.147384 200.659183,96.5636133 195.960527,91.8688194 L168.690777,64.7181159 C164.472332,60.5180858 157.646868,60.5241425 153.435895,64.7316526 C149.227413,68.936674 149.227413,75.7543607 153.435895,79.9593821 L171.854035,98.3623765 C173.02366,99.5310396 173.02366,101.304724 171.854035,102.473387 L153.685633,120.626849 C149.47715,124.83187 149.47715,131.649557 153.685633,135.854579 Z",fill:"url(#linearGradient-3)"})]}),(0,d.jsx)("ellipse",{fill:"url(#linearGradient-4)",cx:"100.519339",cy:"100.436681",rx:"23.6001926",ry:"23.580786"})]})})})})]});},eG=n("92860d83"),eK=function e(t){return(t||[]).reduce(function(t,n){return(n.key&&t.push(n.key),n.children||n.routes)?t.concat(e(n.children||n.routes)||[]):t;},[]);};function eU(e){return e.map(function(e){var t=e.children||[],n=(0,j.default)({},e);if(!n.children&&n.routes&&(n.children=n.routes),!n.name||n.hideInMenu)return null;if(n&&null!=n&&n.children){if(!n.hideChildrenInMenu&&t.some(function(e){return e&&e.name&&!e.hideInMenu;}))return(0,j.default)((0,j.default)({},e),{},{children:eU(t)});delete n.children;}return delete n.routes,n;}).filter(function(e){return e;});}var eV=n("c30b25f9"),eX=i._(eV),eY=n("c7c31e40"),eZ=n("cb04eb22"),eq=i._(eZ),eJ=n("390b7910"),eQ=i._(eJ),e0=n("3028ea74"),e1=i._(e0),e2=n("acb5f477"),e5=i._(e2);function e4(){return(0,d.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 12 12",fill:"currentColor","aria-hidden":"true",children:(0,d.jsx)("path",{d:"M6.432 7.967a.448.448 0 01-.318.133h-.228a.46.46 0 01-.318-.133L2.488 4.85a.305.305 0 010-.43l.427-.43a.293.293 0 01.42 0L6 6.687l2.665-2.699a.299.299 0 01.426 0l.42.431a.305.305 0 010 .43L6.432 7.967z"})});}var e8=n("21e273ab"),e6=function(e){var t,n,o;return(0,f.default)({},e.componentCls,{position:"absolute",insetBlockStart:"18px",zIndex:"101",width:"24px",height:"24px",fontSize:["14px","16px"],textAlign:"center",borderRadius:"40px",insetInlineEnd:"-13px",transition:"transform 0.3s",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",color:null===(t=e.layout)||void 0===t||null===(t=t.sider)||void 0===t?void 0:t.colorTextCollapsedButton,backgroundColor:null===(n=e.layout)||void 0===n||null===(n=n.sider)||void 0===n?void 0:n.colorBgCollapsedButton,boxShadow:"0 2px 8px -2px rgba(0,0,0,0.05), 0 1px 4px -1px rgba(25,15,15,0.07), 0 0 1px 0 rgba(0,0,0,0.08)","&:hover":{color:null===(o=e.layout)||void 0===o||null===(o=o.sider)||void 0===o?void 0:o.colorTextCollapsedButtonHover,boxShadow:"0 4px 16px -4px rgba(0,0,0,0.05), 0 2px 8px -2px rgba(25,15,15,0.07), 0 1px 2px 0 rgba(0,0,0,0.08)"},".anticon":{fontSize:"14px"},"& > svg":{transition:"transform  0.3s",transform:"rotate(90deg)"},"&-collapsed":{"& > svg":{transform:"rotate(-90deg)"}}});},e3=["isMobile","collapsed"],e9=function(e){var t,n=e.isMobile,o=e.collapsed,a=(0,x.default)(e,e3),l=(t=e.className,(0,e8.useStyle)("SiderMenuCollapsedIcon",function(e){return[e6((0,j.default)((0,j.default)({},e),{},{componentCls:".".concat(t)}))];})),i=l.wrapSSR,r=l.hashId;return n&&o?null:i((0,d.jsx)("div",(0,j.default)((0,j.default)({},a),{},{className:(0,eb.default)(e.className,r,(0,f.default)((0,f.default)({},"".concat(e.className,"-collapsed"),o),"".concat(e.className,"-is-mobile"),n)),children:(0,d.jsx)(e4,{})})));},e7=n("c2fedd46"),te=i._(e7),tt=n("3ad4ab70"),tn=i._(tt),to=n("459b858f"),ta=n("b1eda58b"),tl=i._(ta),ti=n("c6b3bfb6"),tr=["className","component","viewBox","spin","rotate","tabIndex","onClick","children"],td=c.forwardRef(function(e,t){var n=e.className,o=e.component,a=e.viewBox,l=e.spin,i=e.rotate,r=e.tabIndex,d=e.onClick,u=e.children,s=(0,x.default)(e,tr),p=c.useRef(),m=(0,to.useComposeRef)(p,t);(0,ti.warning)(!!(o||u),"Should have `component` prop or `children`."),(0,ti.useInsertStyles)(p);var h=c.useContext(tl.default),v=h.prefixCls,g=void 0===v?"anticon":v,y=h.rootClassName,b=(0,eb.default)(y,g,(0,f.default)({},"".concat(g,"-spin"),!!l&&!!o),n),C=(0,eb.default)((0,f.default)({},"".concat(g,"-spin"),!!l)),k=(0,j.default)((0,j.default)({},ti.svgBaseProps),{},{className:C,style:i?{msTransform:"rotate(".concat(i,"deg)"),transform:"rotate(".concat(i,"deg)")}:void 0,viewBox:a});a||delete k.viewBox;var w=r;return void 0===w&&d&&(w=-1),c.createElement("span",(0,tn.default)({role:"img"},s,{ref:m,tabIndex:w,onClick:d,className:b}),o?c.createElement(o,k,u):u?((0,ti.warning)(!!a||1===c.Children.count(u)&&c.isValidElement(u)&&"use"===c.Children.only(u).type,"Make sure that you provide correct `viewBox` prop (default `0 0 1024 1024`) to the icon."),c.createElement("svg",(0,tn.default)({},k,{viewBox:a}),u)):null);});td.displayName="AntdIcon";var tu=["type","children"],ts=new Set;function tc(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=e[t];if("string"==typeof n&&n.length&&!ts.has(n)){var o=document.createElement("script");o.setAttribute("src",n),o.setAttribute("data-namespace",n),e.length>t+1&&(o.onload=function(){tc(e,t+1);},o.onerror=function(){tc(e,t+1);}),ts.add(n),document.body.appendChild(o);}}function tp(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.scriptUrl,n=e.extraCommonProps,o=void 0===n?{}:n;t&&"undefined"!=typeof document&&"undefined"!=typeof window&&"function"==typeof document.createElement&&(Array.isArray(t)?tc(t.reverse()):tc([t]));var a=c.forwardRef(function(e,t){var n=e.type,a=e.children,l=(0,x.default)(e,tu),i=null;return e.type&&(i=c.createElement("use",{xlinkHref:"#".concat(n)})),a&&(i=a),c.createElement(td,(0,tn.default)({},o,l,{ref:t}),i);});return a.displayName="Iconfont",a;}var tf=n("b0e5ad37"),tm=n("b28bd1ac"),th=i._(tm),tv=n("2a4858ed"),tg=i._(tv),ty={navTheme:"light",layout:"side",contentWidth:"Fluid",fixedHeader:!1,fixSiderbar:!0,iconfontUrl:"",colorPrimary:"#1677FF",splitMenus:!1},tx=function(e,t){var n,o,a=t.includes("horizontal")?null===(n=e.layout)||void 0===n?void 0:n.header:null===(o=e.layout)||void 0===o?void 0:o.sider;return(0,j.default)((0,j.default)((0,f.default)({},"".concat(e.componentCls),(0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)({background:"transparent",color:null==a?void 0:a.colorTextMenu,border:"none"},"".concat(e.componentCls,"-menu-item"),{transition:"none !important"}),"".concat(e.componentCls,"-submenu-has-icon"),(0,f.default)({},"> ".concat(e.antCls,"-menu-sub"),{paddingInlineStart:10})),"".concat(e.antCls,"-menu-title-content"),{width:"100%",height:"100%",display:"inline-flex"}),"".concat(e.antCls,"-menu-title-content"),{"&:first-child":{width:"100%"}}),"".concat(e.componentCls,"-item-icon"),{display:"flex",alignItems:"center"}),"&&-collapsed",(0,f.default)((0,f.default)((0,f.default)({},"".concat(e.antCls,"-menu-item, \n        ").concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-item, \n        ").concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-submenu > ").concat(e.antCls,"-menu-submenu-title, \n        ").concat(e.antCls,"-menu-submenu > ").concat(e.antCls,"-menu-submenu-title"),{paddingInline:"0 !important",marginBlock:"4px !important"}),"".concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-submenu-selected > ").concat(e.antCls,"-menu-submenu-title, \n        ").concat(e.antCls,"-menu-submenu-selected > ").concat(e.antCls,"-menu-submenu-title"),{backgroundColor:null==a?void 0:a.colorBgMenuItemSelected,borderRadius:e.borderRadiusLG}),"".concat(e.componentCls,"-group"),(0,f.default)({},"".concat(e.antCls,"-menu-item-group-title"),{paddingInline:0}))),"&-item-title",(0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)({display:"flex",flexDirection:"row",alignItems:"center",gap:e.marginXS},"".concat(e.componentCls,"-item-text"),{maxWidth:"100%",textOverflow:"ellipsis",overflow:"hidden",wordBreak:"break-all",whiteSpace:"nowrap"}),"&-collapsed",(0,f.default)((0,f.default)({minWidth:40,height:40},"".concat(e.componentCls,"-item-icon"),{height:"16px",width:"16px",lineHeight:"16px !important",".anticon":{lineHeight:"16px !important",height:"16px"}}),"".concat(e.componentCls,"-item-text-has-icon"),{display:"none !important"})),"&-collapsed-level-0",{flexDirection:"column",justifyContent:"center"}),"&".concat(e.componentCls,"-group-item-title"),{gap:e.marginXS,height:18,overflow:"hidden"}),"&".concat(e.componentCls,"-item-collapsed-show-title"),(0,f.default)({lineHeight:"16px",gap:0},"&".concat(e.componentCls,"-item-title-collapsed"),(0,f.default)((0,f.default)({display:"flex"},"".concat(e.componentCls,"-item-icon"),{height:"16px",width:"16px",lineHeight:"16px !important",".anticon":{lineHeight:"16px!important",height:"16px"}}),"".concat(e.componentCls,"-item-text"),{opacity:"1 !important",display:"inline !important",textAlign:"center",fontSize:12,height:12,lineHeight:"12px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",width:"100%",margin:0,padding:0,marginBlockStart:4})))),"&-group",(0,f.default)({},"".concat(e.antCls,"-menu-item-group-title"),{fontSize:12,color:e.colorTextLabel,".anticon":{marginInlineEnd:8}})),"&-group-divider",{color:e.colorTextSecondary,fontSize:12,lineHeight:20})),t.includes("horizontal")?{}:(0,f.default)({},"".concat(e.antCls,"-menu-submenu").concat(e.antCls,"-menu-submenu-popup"),(0,f.default)({},"".concat(e.componentCls,"-item-title"),{alignItems:"flex-start"}))),{},(0,f.default)({},"".concat(e.antCls,"-menu-submenu-popup"),{backgroundColor:"rgba(255, 255, 255, 0.42)","-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)"}));},tb=function(e){var t=(0,c.useState)(e.collapsed),n=(0,C.default)(t,2),o=n[0],a=n[1],l=(0,c.useState)(!1),i=(0,C.default)(l,2),r=i[0],u=i[1];return((0,c.useEffect)(function(){u(!1),setTimeout(function(){a(e.collapsed);},400);},[e.collapsed]),e.disable)?e.children:(0,d.jsx)(tg.default,{title:e.title,open:!!o&&!!e.collapsed&&r,placement:"right",onOpenChange:u,children:e.children});},tC=tp({scriptUrl:ty.iconfontUrl}),tk=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"icon-",n=arguments.length>2?arguments[2]:void 0;if("string"==typeof e&&""!==e){if((0,tf.isUrl)(e)||/\w.(png|jpg|jpeg|svg|webp|gif|bmp)$/i.test(e))return(0,d.jsx)("img",{width:16,src:e,alt:"icon",className:n},e);if(e.startsWith(t))return(0,d.jsx)(tC,{type:e});}return e;},tj=function(e){return e&&"string"==typeof e?e.substring(0,1).toUpperCase():null;},tw=(0,eO.default)(function e(t){var n=this;(0,eN.default)(this,e),(0,f.default)(this,"props",void 0),(0,f.default)(this,"getNavMenuItems",function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,o=arguments.length>2?arguments[2]:void 0;return e.map(function(e){return n.getSubMenuOrItem(e,t,o);}).filter(function(e){return e;}).flat(1);}),(0,f.default)(this,"getSubMenuOrItem",function(e,t,o){var a=n.props,l=a.subMenuItemRender,i=a.baseClassName,r=a.prefixCls,u=a.collapsed,s=a.menu,c=a.iconPrefixes,p=a.layout,m=(null==s?void 0:s.type)==="group"&&"top"!==p,h=n.props.token,v=n.getIntlName(e),g=(null==e?void 0:e.children)||(null==e?void 0:e.routes),y=m&&0===t?"group":void 0;if(Array.isArray(g)&&g.length>0){var x,b,C,k,w,S=0===t||m&&1===t,M=tk(e.icon,c,"".concat(i,"-icon ").concat(null===(x=n.props)||void 0===x?void 0:x.hashId)),I=u&&S?tj(v):null,R=(0,d.jsxs)("div",{className:(0,eb.default)("".concat(i,"-item-title"),null===(b=n.props)||void 0===b?void 0:b.hashId,(0,f.default)((0,f.default)((0,f.default)((0,f.default)({},"".concat(i,"-item-title-collapsed"),u),"".concat(i,"-item-title-collapsed-level-").concat(o),u),"".concat(i,"-group-item-title"),"group"===y),"".concat(i,"-item-collapsed-show-title"),(null==s?void 0:s.collapsedShowTitle)&&u)),children:["group"===y&&u?null:S&&M?(0,d.jsx)("span",{className:"".concat(i,"-item-icon ").concat(null===(C=n.props)||void 0===C?void 0:C.hashId).trim(),children:M}):I,(0,d.jsx)("span",{className:(0,eb.default)("".concat(i,"-item-text"),null===(k=n.props)||void 0===k?void 0:k.hashId,(0,f.default)({},"".concat(i,"-item-text-has-icon"),"group"!==y&&S&&(M||I))),children:v})]}),B=l?l((0,j.default)((0,j.default)({},e),{},{isUrl:!1}),R,n.props):R;if(m&&0===t&&n.props.collapsed&&!s.collapsedShowGroupTitle)return n.getNavMenuItems(g,t+1,t);var N=n.getNavMenuItems(g,t+1,m&&0===t&&n.props.collapsed?t:t+1);return[{type:y,key:e.key||e.path,label:B,onClick:m?void 0:e.onTitleClick,children:N,className:(0,eb.default)((0,f.default)((0,f.default)((0,f.default)({},"".concat(i,"-group"),"group"===y),"".concat(i,"-submenu"),"group"!==y),"".concat(i,"-submenu-has-icon"),"group"!==y&&S&&M))},m&&0===t?{type:"divider",prefixCls:r,className:"".concat(i,"-divider"),key:(e.key||e.path)+"-group-divider",style:{padding:0,borderBlockEnd:0,margin:n.props.collapsed?"4px":"6px 16px",marginBlockStart:n.props.collapsed?4:8,borderColor:null==h||null===(w=h.layout)||void 0===w||null===(w=w.sider)||void 0===w?void 0:w.colorMenuItemDivider}}:void 0].filter(Boolean);}return{className:"".concat(i,"-menu-item"),disabled:e.disabled,key:e.key||e.path,onClick:e.onTitleClick,label:n.getMenuItemPath(e,t,o)};}),(0,f.default)(this,"getIntlName",function(e){var t=e.name,o=e.locale,a=n.props,l=a.menu,i=a.formatMessage,r=t;return(o&&(null==l?void 0:l.locale)!==!1&&(r=null==i?void 0:i({id:o,defaultMessage:t})),n.props.menuTextRender)?n.props.menuTextRender(e,r,n.props):r;}),(0,f.default)(this,"getMenuItemPath",function(e,t,o){var a,l,i,r,u,s,c,p=n.conversionPath(e.path||"/"),m=n.props,h=m.location,v=m.isMobile,g=m.onCollapse,y=m.menuItemRender,x=m.iconPrefixes,b=n.getIntlName(e),C=n.props,k=C.baseClassName,w=C.menu,S=C.collapsed,M=(null==w?void 0:w.type)==="group",I=0===t||M&&1===t,R=I?tk(e.icon,x,"".concat(k,"-icon ").concat(null===(a=n.props)||void 0===a?void 0:a.hashId)):null,B=S&&I?tj(b):null,N=(0,d.jsxs)("div",{className:(0,eb.default)("".concat(k,"-item-title"),null===(l=n.props)||void 0===l?void 0:l.hashId,(0,f.default)((0,f.default)((0,f.default)({},"".concat(k,"-item-title-collapsed"),S),"".concat(k,"-item-title-collapsed-level-").concat(o),S),"".concat(k,"-item-collapsed-show-title"),(null==w?void 0:w.collapsedShowTitle)&&S)),children:[(0,d.jsx)("span",{className:"".concat(k,"-item-icon ").concat(null===(i=n.props)||void 0===i?void 0:i.hashId).trim(),style:{display:null!==B||R?"":"none"},children:R||(0,d.jsx)("span",{className:"anticon",children:B})}),(0,d.jsx)("span",{className:(0,eb.default)("".concat(k,"-item-text"),null===(r=n.props)||void 0===r?void 0:r.hashId,(0,f.default)({},"".concat(k,"-item-text-has-icon"),I&&(R||B))),children:b})]},p),T=(0,tf.isUrl)(p);if(T&&(N=(0,d.jsxs)("span",{onClick:function(){var e,t;null===(e=window)||void 0===e||null===(t=e.open)||void 0===t||t.call(e,p,"_blank");},className:(0,eb.default)("".concat(k,"-item-title"),null===(u=n.props)||void 0===u?void 0:u.hashId,(0,f.default)((0,f.default)((0,f.default)((0,f.default)({},"".concat(k,"-item-title-collapsed"),S),"".concat(k,"-item-title-collapsed-level-").concat(o),S),"".concat(k,"-item-link"),!0),"".concat(k,"-item-collapsed-show-title"),(null==w?void 0:w.collapsedShowTitle)&&S)),children:[(0,d.jsx)("span",{className:"".concat(k,"-item-icon ").concat(null===(s=n.props)||void 0===s?void 0:s.hashId).trim(),style:{display:null!==B||R?"":"none"},children:R||(0,d.jsx)("span",{className:"anticon",children:B})}),(0,d.jsx)("span",{className:(0,eb.default)("".concat(k,"-item-text"),null===(c=n.props)||void 0===c?void 0:c.hashId,(0,f.default)({},"".concat(k,"-item-text-has-icon"),I&&(R||B))),children:b})]},p)),y){var O=(0,j.default)((0,j.default)({},e),{},{isUrl:T,itemPath:p,isMobile:v,replace:p===(void 0===h?{pathname:"/"}:h).pathname,onClick:function(){return g&&g(!0);},children:void 0});return 0===t?(0,d.jsx)(tb,{collapsed:S,title:b,disable:e.disabledTooltip,children:y(O,N,n.props)}):y(O,N,n.props);}return 0===t?(0,d.jsx)(tb,{collapsed:S,title:b,disable:e.disabledTooltip,children:N}):N;}),(0,f.default)(this,"conversionPath",function(e){return e&&0===e.indexOf("http")?e:"/".concat(e||"").replace(/\/+/g,"/");}),this.props=t;}),tS=function(e,t){var n=t.layout,o=t.collapsed,a={};return e&&!o&&["side","mix"].includes(n||"mix")&&(a={openKeys:e}),a;},tM=function(e){var t=e.mode,n=e.className,o=e.handleOpenChange,a=e.style,l=e.menuData,i=e.prefixCls,r=e.menu,u=e.matchMenuKeys,s=e.iconfontUrl,p=e.selectedKeys,m=e.onSelect,h=e.menuRenderType,v=e.openKeys,g=(0,c.useContext)(w.ProProvider),y=g.dark,x=g.token,b="".concat(i,"-base-menu-").concat(t),k=(0,c.useRef)([]),S=(0,O.useMountMergeState)(null==r?void 0:r.defaultOpenAll),M=(0,C.default)(S,2),I=M[0],R=M[1],B=(0,O.useMountMergeState)(function(){return null!=r&&r.defaultOpenAll?eK(l)||[]:!1!==v&&[];},{value:!1===v?void 0:v,onChange:o}),N=(0,C.default)(B,2),T=N[0],E=N[1],H=(0,O.useMountMergeState)([],{value:p,onChange:m?function(e){m&&e&&m(e);}:void 0}),P=(0,C.default)(H,2),A=P[0],L=P[1];(0,c.useEffect)(function(){(null==r||!r.defaultOpenAll)&&!1!==v&&u&&(E(u),L(u));},[u.join("-")]),(0,c.useEffect)(function(){s&&(tC=tp({scriptUrl:s}));},[s]),(0,c.useEffect)(function(){if(u.join("-")!==(A||[]).join("-")&&L(u),I||!1===v||u.join("-")===(T||[]).join("-"))null!=r&&r.ignoreFlatMenu&&I?E(eK(l)):R(!1);else{var e=u;(null==r?void 0:r.autoClose)===!1&&(e=Array.from(new Set([].concat((0,te.default)(u),(0,te.default)(T||[]))))),E(e);}},[u.join("-")]);var z=(0,c.useMemo)(function(){return tS(T,e);},[T&&T.join(","),e.layout,e.collapsed]),D=(0,e8.useStyle)("ProLayoutBaseMenu"+t,function(e){return[tx((0,j.default)((0,j.default)({},e),{},{componentCls:".".concat(b)}),t||"inline")];}),_=D.wrapSSR,F=D.hashId,W=(0,c.useMemo)(function(){return new tw((0,j.default)((0,j.default)({},e),{},{token:x,menuRenderType:h,baseClassName:b,hashId:F}));},[e,x,h,b,F]);if(null!=r&&r.loading)return(0,d.jsx)("div",{style:null!=t&&t.includes("inline")?{padding:24}:{marginBlockStart:16},children:(0,d.jsx)(th.default,{active:!0,title:!1,paragraph:{rows:null!=t&&t.includes("inline")?6:1}})});!1!==e.openKeys||e.handleOpenChange||(k.current=u);var $=e.postMenuData?e.postMenuData(l):l;return $&&(null==$?void 0:$.length)<1?null:_((0,c.createElement)(eQ.default,(0,j.default)((0,j.default)({},z),{},{_internalDisableMenuItemTitleTooltip:!0,key:"Menu",mode:t,inlineIndent:16,defaultOpenKeys:k.current,theme:y?"dark":"light",selectedKeys:A,style:(0,j.default)({backgroundColor:"transparent",border:"none"},a),className:(0,eb.default)(n,F,b,(0,f.default)((0,f.default)({},"".concat(b,"-horizontal"),"horizontal"===t),"".concat(b,"-collapsed"),e.collapsed)),items:W.getNavMenuItems($,0,0),onOpenChange:function(t){e.collapsed||E(t);}},e.menuProps)));},tI=["title","render"],tR=c.default.memo(function(e){return(0,d.jsx)(d.Fragment,{children:e.children});}),tB=ey.default.Sider,tN=ey.default._InternalSiderContext,tT=void 0===tN?{Provider:tR}:tN,tO=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"menuHeaderRender",n=e.logo,o=e.title,a=e.layout,l=e[t];if(!1===l)return null;var i=(0,eY.defaultRenderLogo)(n),r=(0,d.jsx)("h1",{children:null!=o?o:"Ant Design Pro"});return l?l(i,e.collapsed?null:r,e):e.isMobile?null:("mix"!==a||"menuHeaderRender"!==t)&&(e.collapsed?(0,d.jsx)("a",{children:i},"title"):(0,d.jsxs)("a",{children:[i,r]},"title"));},tE=function(e){var t,n,o,a,l,i=e.collapsed,r=e.originCollapsed,u=e.fixSiderbar,s=e.menuFooterRender,p=e.onCollapse,m=e.theme,h=e.siderWidth,v=e.isMobile,g=e.onMenuHeaderClick,y=e.breakpoint,b=void 0===y?"lg":y,C=e.style,k=e.layout,S=e.menuExtraRender,M=void 0!==S&&S,I=e.links,R=e.menuContentRender,B=e.collapsedButtonRender,N=e.prefixCls,T=e.avatarProps,O=e.rightContentRender,E=e.actionsRender,H=e.onOpenChange,P=e.stylish,A=e.logoStyle,L=(0,c.useContext)(w.ProProvider).hashId,z=(0,c.useMemo)(function(){return!v&&"mix"!==k;},[v,k]),D="".concat(N,"-sider"),_=(t="".concat(D,".").concat(D,"-stylish"),o=(n={stylish:P,proLayoutCollapsedWidth:64}).stylish,a=n.proLayoutCollapsedWidth,(0,e8.useStyle)("ProLayoutSiderMenuStylish",function(e){var n=(0,j.default)((0,j.default)({},e),{},{componentCls:".".concat(t),proLayoutCollapsedWidth:a});return o?[(0,f.default)({},"div".concat(e.proComponentsCls,"-layout"),(0,f.default)({},"".concat(n.componentCls),null==o?void 0:o(n)))]:[];})),F=(0,eb.default)("".concat(D),L,(0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)({},"".concat(D,"-fixed"),u),"".concat(D,"-fixed-mix"),"mix"===k&&!v&&u),"".concat(D,"-collapsed"),e.collapsed),"".concat(D,"-layout-").concat(k),k&&!v),"".concat(D,"-light"),"dark"!==m),"".concat(D,"-mix"),"mix"===k&&!v),"".concat(D,"-stylish"),!!P)),W=tO(e),$=M&&M(e),G=(0,c.useMemo)(function(){return!1!==R&&(0,c.createElement)(tM,(0,j.default)((0,j.default)({},e),{},{key:"base-menu",mode:i&&!v?"vertical":"inline",handleOpenChange:H,style:{width:"100%"},className:"".concat(D,"-menu ").concat(L).trim()}));},[D,L,R,H,e]),K=(I||[]).map(function(e,t){return{className:"".concat(D,"-link"),label:e,key:t};}),U=(0,c.useMemo)(function(){return R?R(e,G):G;},[R,G,e]),V=(0,c.useMemo)(function(){if(!T)return null;var t=T.title,n=T.render,o=(0,x.default)(T,tI),a=(0,d.jsxs)("div",{className:"".concat(D,"-actions-avatar"),children:[null!=o&&o.src||null!=o&&o.srcSet||o.icon||o.children?(0,d.jsx)(eq.default,(0,j.default)({size:28},o)):null,T.title&&!i&&(0,d.jsx)("span",{children:t})]});return n?n(T,a,e):a;},[T,D,i]),X=(0,c.useMemo)(function(){return E?(0,d.jsx)(e1.default,{align:"center",size:4,direction:i?"vertical":"horizontal",className:(0,eb.default)(["".concat(D,"-actions-list"),i&&"".concat(D,"-actions-list-collapsed"),L]),children:[null==E?void 0:E(e)].flat(1).map(function(e,t){return(0,d.jsx)("div",{className:"".concat(D,"-actions-list-item ").concat(L).trim(),children:e},t);})}):null;},[E,D,i]),Y=(0,c.useMemo)(function(){return(0,d.jsx)(eY.AppsLogoComponents,{onItemClick:e.itemClick,appListRender:e.appListRender,appList:e.appList,prefixCls:e.prefixCls});},[e.appList,e.appListRender,e.prefixCls]),Z=(0,c.useMemo)(function(){if(!1===B)return null;var e=(0,d.jsx)(e9,{isMobile:v,collapsed:r,className:"".concat(D,"-collapsed-button"),onClick:function(){null==p||p(!r);}});return B?B(i,e):e;},[B,v,r,D,i,p]),q=(0,c.useMemo)(function(){return V||X?(0,d.jsxs)("div",{className:(0,eb.default)("".concat(D,"-actions"),L,i&&"".concat(D,"-actions-collapsed")),children:[V,X]}):null;},[X,V,D,i,L]),J=(0,c.useMemo)(function(){var t;return null!=e&&null!==(t=e.menu)&&void 0!==t&&t.hideMenuWhenCollapsed&&i?"".concat(D,"-hide-menu-collapsed"):null;},[D,i,null==e||null===(l=e.menu)||void 0===l?void 0:l.hideMenuWhenCollapsed]),Q=s&&(null==s?void 0:s(e)),ee=(0,d.jsxs)(d.Fragment,{children:[W&&(0,d.jsxs)("div",{className:(0,eb.default)([(0,eb.default)("".concat(D,"-logo"),L,(0,f.default)({},"".concat(D,"-logo-collapsed"),i))]),onClick:z?g:void 0,id:"logo",style:A,children:[W,Y]}),$&&(0,d.jsx)("div",{className:(0,eb.default)(["".concat(D,"-extra"),!W&&"".concat(D,"-extra-no-logo"),L]),children:$}),(0,d.jsx)("div",{style:{flex:1,overflowY:"auto",overflowX:"hidden"},children:U}),(0,d.jsxs)(tT.Provider,{value:{},children:[I?(0,d.jsx)("div",{className:"".concat(D,"-links ").concat(L).trim(),children:(0,d.jsx)(eQ.default,{inlineIndent:16,className:"".concat(D,"-link-menu ").concat(L).trim(),selectedKeys:[],openKeys:[],theme:m,mode:"inline",items:K})}):null,z&&(0,d.jsxs)(d.Fragment,{children:[q,!X&&O?(0,d.jsx)("div",{className:(0,eb.default)("".concat(D,"-actions"),L,(0,f.default)({},"".concat(D,"-actions-collapsed"),i)),children:null==O?void 0:O(e)}):null]}),Q&&(0,d.jsx)("div",{className:(0,eb.default)(["".concat(D,"-footer"),L,(0,f.default)({},"".concat(D,"-footer-collapsed"),i)]),children:Q})]})]});return _.wrapSSR((0,d.jsxs)(d.Fragment,{children:[u&&!v&&!J&&(0,d.jsx)("div",{style:(0,j.default)({width:i?64:h,overflow:"hidden",flex:"0 0 ".concat(i?64:h,"px"),maxWidth:i?64:h,minWidth:i?64:h,transition:"all 0.2s ease 0s"},C)}),(0,d.jsxs)(tB,{collapsible:!0,trigger:null,collapsed:i,breakpoint:!1===b?void 0:b,onCollapse:function(e){v||null==p||p(e);},collapsedWidth:64,style:C,theme:m,width:h,className:(0,eb.default)(F,L,J),children:[J?(0,d.jsx)("div",{className:"".concat(D,"-hide-when-collapsed ").concat(L).trim(),style:{height:"100%",width:"100%",opacity:J?0:1},children:ee}):ee,Z]})]}));},tH=function(e){var t=(0,c.useRef)(null);return t.current=e,(0,c.useCallback)(function(){for(var e,n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return null===(e=t.current)||void 0===e?void 0:e.call.apply(e,[t].concat((0,te.default)(o)));},[]);},tP=n("16888dd8"),tA=i._(tP),tL=function(e){var t,n,o,a,l;return(0,f.default)({},e.componentCls,{"&-header-actions":{display:"flex",height:"100%",alignItems:"center","&-item":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingBlock:0,paddingInline:2,color:null===(t=e.layout)||void 0===t||null===(t=t.header)||void 0===t?void 0:t.colorTextRightActionsItem,fontSize:"16px",cursor:"pointer",borderRadius:e.borderRadius,"> *":{paddingInline:6,paddingBlock:6,borderRadius:e.borderRadius,"&:hover":{backgroundColor:null===(n=e.layout)||void 0===n||null===(n=n.header)||void 0===n?void 0:n.colorBgRightActionsItemHover}}},"&-avatar":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingInlineStart:e.padding,paddingInlineEnd:e.padding,cursor:"pointer",color:null===(o=e.layout)||void 0===o||null===(o=o.header)||void 0===o?void 0:o.colorTextRightActionsItem,"> div":{height:"44px",color:null===(a=e.layout)||void 0===a||null===(a=a.header)||void 0===a?void 0:a.colorTextRightActionsItem,paddingInline:8,paddingBlock:8,cursor:"pointer",display:"flex",alignItems:"center",lineHeight:"44px",borderRadius:e.borderRadius,"&:hover":{backgroundColor:null===(l=e.layout)||void 0===l||null===(l=l.header)||void 0===l?void 0:l.colorBgRightActionsItemHover}}}}});},tz=["rightContentRender","avatarProps","actionsRender","headerContentRender"],tD=["title","render"],t_=function(e){var t,n,o,a,l,i=e.rightContentRender,r=e.avatarProps,u=e.actionsRender,s=(e.headerContentRender,(0,x.default)(e,tz)),p=(0,c.useContext)(ev.default.ConfigContext).getPrefixCls,m="".concat(p(),"-pro-global-header"),v=(0,e8.useStyle)("ProLayoutRightContent",function(e){return[tL((0,j.default)((0,j.default)({},e),{},{componentCls:".".concat(m)}))];}),y=v.wrapSSR,b=v.hashId,k=(0,c.useState)("auto"),w=(0,C.default)(k,2),S=w[0],M=w[1],I=(0,c.useMemo)(function(){if(!r)return null;var e=r.title,t=r.render,n=(0,x.default)(r,tD),o=[null!=n&&n.src||null!=n&&n.srcSet||n.icon||n.children?(0,c.createElement)(eq.default,(0,j.default)((0,j.default)({},n),{},{size:28,key:"avatar"})):null,e?(0,d.jsx)("span",{style:{marginInlineStart:8},children:e},"name"):void 0];return t?t(r,(0,d.jsx)("div",{children:o}),s):(0,d.jsx)("div",{children:o});},[r]),R=u||I?function(e){var t=u&&(null==u?void 0:u(e));return t||I?Array.isArray(t)?y((0,d.jsxs)("div",{className:"".concat(m,"-header-actions ").concat(b).trim(),children:[t.filter(Boolean).map(function(e,t){var n,o=!1;return c.default.isValidElement(e)&&(o=!!(null!=e&&null!==(n=e.props)&&void 0!==n&&n["aria-hidden"])),(0,d.jsx)("div",{className:(0,eb.default)("".concat(m,"-header-actions-item ").concat(b),(0,f.default)({},"".concat(m,"-header-actions-hover"),!o)),children:e},t);}),I&&(0,d.jsx)("span",{className:"".concat(m,"-header-actions-avatar ").concat(b).trim(),children:I})]})):y((0,d.jsxs)("div",{className:"".concat(m,"-header-actions ").concat(b).trim(),children:[t,I&&(0,d.jsx)("span",{className:"".concat(m,"-header-actions-avatar ").concat(b).trim(),children:I})]})):null;}:void 0,B=(t=(0,g.default)((0,h.default)().mark(function e(t){return(0,h.default)().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:M(t);case 1:case"end":return e.stop();}},e);})),n=tH(function(e){return t.apply(this,arguments);}),o=(0,c.useRef)(),a=(0,c.useCallback)(function(){o.current&&(clearTimeout(o.current),o.current=null);},[]),l=(0,c.useCallback)((0,g.default)((0,h.default)().mark(function e(){var t,l,i,r=arguments;return(0,h.default)().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(l=Array(t=r.length),i=0;i<t;i++)l[i]=r[i];e.next=3;break;case 3:return a(),e.abrupt("return",new Promise(function(e){o.current=setTimeout((0,g.default)((0,h.default)().mark(function t(){return(0,h.default)().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.t0=e,t.next=3,n.apply(void 0,l);case 3:return t.t1=t.sent,(0,t.t0)(t.t1),t.abrupt("return");case 6:case"end":return t.stop();}},t);})),160);}));case 5:case"end":return e.stop();}},e);})),[n,a,160]),(0,c.useEffect)(function(){return a;},[a]),{run:l,cancel:a}),N=R||i;return(0,d.jsx)("div",{className:"".concat(m,"-right-content ").concat(b).trim(),style:{minWidth:S,height:"100%"},children:(0,d.jsx)("div",{style:{height:"100%"},children:(0,d.jsx)(tA.default,{onResize:function(e){var t=e.width;B.run(t);},children:N?(0,d.jsx)("div",{style:{display:"flex",alignItems:"center",height:"100%",justifyContent:"flex-end"},children:N((0,j.default)((0,j.default)({},s),{},{rightContentSize:S}))}):null})})});},tF=function(e){var t,n;return(0,f.default)({},e.componentCls,{position:"relative",width:"100%",height:"100%",backgroundColor:"transparent",".anticon":{color:"inherit"},"&-main":{display:"flex",height:"100%",paddingInlineStart:"16px","&-left":(0,f.default)({display:"flex",alignItems:"center"},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginInlineEnd:16,marginInlineStart:-8})},"&-wide":{maxWidth:1152,margin:"0 auto"},"&-logo":{position:"relative",display:"flex",height:"100%",alignItems:"center",overflow:"hidden","> *:first-child":{display:"flex",alignItems:"center",minHeight:"22px",fontSize:"22px"},"> *:first-child > img":{display:"inline-block",height:"32px",verticalAlign:"middle"},"> *:first-child > h1":{display:"inline-block",marginBlock:0,marginInline:0,lineHeight:"24px",marginInlineStart:6,fontWeight:"600",fontSize:"16px",color:null===(t=e.layout)||void 0===t||null===(t=t.header)||void 0===t?void 0:t.colorHeaderTitle,verticalAlign:"top"}},"&-menu":{minWidth:0,display:"flex",alignItems:"center",paddingInline:6,paddingBlock:6,lineHeight:"".concat(Math.max(((null===(n=e.layout)||void 0===n||null===(n=n.header)||void 0===n?void 0:n.heightLayoutHeader)||56)-12,40),"px")}});},tW=function(e){var t,n,o,a,l,i,r,u=(0,c.useRef)(null),s=e.onMenuHeaderClick,p=e.contentWidth,m=e.rightContentRender,h=e.className,v=e.style,g=e.headerContentRender,y=e.layout,x=e.actionsRender,b=(0,c.useContext)(ev.default.ConfigContext).getPrefixCls,C=(0,c.useContext)(w.ProProvider).dark,k="".concat(e.prefixCls||b("pro"),"-top-nav-header"),M=(0,e8.useStyle)("ProLayoutTopNavHeader",function(e){return[tF((0,j.default)((0,j.default)({},e),{},{componentCls:".".concat(k)}))];}),I=M.wrapSSR,R=M.hashId,B=void 0;void 0!==e.menuHeaderRender?B="menuHeaderRender":("mix"===y||"top"===y)&&(B="headerTitleRender");var N=tO((0,j.default)((0,j.default)({},e),{},{collapsed:!1}),B),T=(0,c.useContext)(w.ProProvider).token,O=(0,c.useMemo)(function(){var t,n,o,a,l,i,r,u,s,c,p,f,m,h=(0,d.jsx)(ev.default,{theme:{hashed:(0,w.isNeedOpenHash)(),components:{Layout:{headerBg:"transparent",bodyBg:"transparent"},Menu:(0,j.default)({},(0,S.coverToNewToken)({colorItemBg:(null===(t=T.layout)||void 0===t||null===(t=t.header)||void 0===t?void 0:t.colorBgHeader)||"transparent",colorSubItemBg:(null===(n=T.layout)||void 0===n||null===(n=n.header)||void 0===n?void 0:n.colorBgHeader)||"transparent",radiusItem:T.borderRadius,colorItemBgSelected:(null===(o=T.layout)||void 0===o||null===(o=o.header)||void 0===o?void 0:o.colorBgMenuItemSelected)||(null==T?void 0:T.colorBgTextHover),itemHoverBg:(null===(a=T.layout)||void 0===a||null===(a=a.header)||void 0===a?void 0:a.colorBgMenuItemHover)||(null==T?void 0:T.colorBgTextHover),colorItemBgSelectedHorizontal:(null===(l=T.layout)||void 0===l||null===(l=l.header)||void 0===l?void 0:l.colorBgMenuItemSelected)||(null==T?void 0:T.colorBgTextHover),colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemText:(null===(i=T.layout)||void 0===i||null===(i=i.header)||void 0===i?void 0:i.colorTextMenu)||(null==T?void 0:T.colorTextSecondary),colorItemTextHoverHorizontal:(null===(r=T.layout)||void 0===r||null===(r=r.header)||void 0===r?void 0:r.colorTextMenuActive)||(null==T?void 0:T.colorText),colorItemTextSelectedHorizontal:(null===(u=T.layout)||void 0===u||null===(u=u.header)||void 0===u?void 0:u.colorTextMenuSelected)||(null==T?void 0:T.colorTextBase),horizontalItemBorderRadius:4,colorItemTextHover:(null===(s=T.layout)||void 0===s||null===(s=s.header)||void 0===s?void 0:s.colorTextMenuActive)||"rgba(0, 0, 0, 0.85)",horizontalItemHoverBg:(null===(c=T.layout)||void 0===c||null===(c=c.header)||void 0===c?void 0:c.colorBgMenuItemHover)||"rgba(0, 0, 0, 0.04)",colorItemTextSelected:(null===(p=T.layout)||void 0===p||null===(p=p.header)||void 0===p?void 0:p.colorTextMenuSelected)||"rgba(0, 0, 0, 1)",popupBg:null==T?void 0:T.colorBgElevated,subMenuItemBg:null==T?void 0:T.colorBgElevated,darkSubMenuItemBg:"transparent",darkPopupBg:null==T?void 0:T.colorBgElevated}))},token:{colorBgElevated:(null===(f=T.layout)||void 0===f||null===(f=f.header)||void 0===f?void 0:f.colorBgHeader)||"transparent"}},children:(0,d.jsx)(tM,(0,j.default)((0,j.default)((0,j.default)({theme:C?"dark":"light"},e),{},{className:"".concat(k,"-base-menu ").concat(R).trim()},e.menuProps),{},{style:(0,j.default)({width:"100%"},null===(m=e.menuProps)||void 0===m?void 0:m.style),collapsed:!1,menuRenderType:"header",mode:"horizontal"}))});return g?g(e,h):h;},[null===(t=T.layout)||void 0===t||null===(t=t.header)||void 0===t?void 0:t.colorBgHeader,null===(n=T.layout)||void 0===n||null===(n=n.header)||void 0===n?void 0:n.colorBgMenuItemSelected,null===(o=T.layout)||void 0===o||null===(o=o.header)||void 0===o?void 0:o.colorBgMenuItemHover,null===(a=T.layout)||void 0===a||null===(a=a.header)||void 0===a?void 0:a.colorTextMenu,null===(l=T.layout)||void 0===l||null===(l=l.header)||void 0===l?void 0:l.colorTextMenuActive,null===(i=T.layout)||void 0===i||null===(i=i.header)||void 0===i?void 0:i.colorTextMenuSelected,null===(r=T.layout)||void 0===r||null===(r=r.header)||void 0===r?void 0:r.colorBgMenuElevated,T.borderRadius,null==T?void 0:T.colorBgTextHover,null==T?void 0:T.colorTextSecondary,null==T?void 0:T.colorText,null==T?void 0:T.colorTextBase,T.colorBgElevated,C,e,k,R,g]);return I((0,d.jsx)("div",{className:(0,eb.default)(k,R,h,(0,f.default)({},"".concat(k,"-light"),!0)),style:v,children:(0,d.jsxs)("div",{ref:u,className:(0,eb.default)("".concat(k,"-main"),R,(0,f.default)({},"".concat(k,"-wide"),"Fixed"===p&&"top"===y)),children:[N&&(0,d.jsxs)("div",{className:(0,eb.default)("".concat(k,"-main-left ").concat(R)),onClick:s,children:[(0,d.jsx)(eY.AppsLogoComponents,(0,j.default)({},e)),(0,d.jsx)("div",{className:"".concat(k,"-logo ").concat(R).trim(),id:"logo",children:N},"logo")]}),(0,d.jsx)("div",{style:{flex:1},className:"".concat(k,"-menu ").concat(R).trim(),children:O}),(m||x||e.avatarProps)&&(0,d.jsx)(t_,(0,j.default)((0,j.default)({rightContentRender:m},e),{},{prefixCls:k}))]})}));},t$=function(e){var t,n,o;return(0,f.default)({},e.componentCls,(0,f.default)((0,f.default)((0,f.default)((0,f.default)({position:"relative",background:"transparent",display:"flex",alignItems:"center",marginBlock:0,marginInline:16,height:(null===(t=e.layout)||void 0===t||null===(t=t.header)||void 0===t?void 0:t.heightLayoutHeader)||56,boxSizing:"border-box","> a":{height:"100%"}},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginInlineEnd:16}),"&-collapsed-button",{minHeight:"22px",color:null===(n=e.layout)||void 0===n||null===(n=n.header)||void 0===n?void 0:n.colorHeaderTitle,fontSize:"18px",marginInlineEnd:"16px"}),"&-logo",{position:"relative",marginInlineEnd:"16px",a:{display:"flex",alignItems:"center",height:"100%",minHeight:"22px",fontSize:"20px"},img:{height:"28px"},h1:{height:"32px",marginBlock:0,marginInline:0,marginInlineStart:8,fontWeight:"600",color:(null===(o=e.layout)||void 0===o||null===(o=o.header)||void 0===o?void 0:o.colorHeaderTitle)||e.colorTextHeading,fontSize:"18px",lineHeight:"32px"},"&-mix":{display:"flex",alignItems:"center"}}),"&-logo-mobile",{minWidth:"24px",marginInlineEnd:0}));},tG=function(e){var t=e.isMobile,n=e.logo,o=e.collapsed,a=e.onCollapse,l=e.rightContentRender,i=e.menuHeaderRender,r=e.onMenuHeaderClick,u=e.className,s=e.style,p=e.layout,m=e.children,h=e.splitMenus,v=e.menuData,g=e.prefixCls,y=(0,c.useContext)(ev.default.ConfigContext),x=y.getPrefixCls,b=y.direction,C="".concat(g||x("pro"),"-global-header"),k=(0,e8.useStyle)("ProLayoutGlobalHeader",function(e){return[t$((0,j.default)((0,j.default)({},e),{},{componentCls:".".concat(C)}))];}),w=k.wrapSSR,S=k.hashId,M=(0,eb.default)(u,C,S);if("mix"===p&&!t&&h){var I=eU((v||[]).map(function(e){return(0,j.default)((0,j.default)({},e),{},{children:void 0,routes:void 0});}));return(0,d.jsx)(tW,(0,j.default)((0,j.default)({mode:"horizontal"},e),{},{splitMenus:!1,menuData:I}));}var R=(0,eb.default)("".concat(C,"-logo"),S,(0,f.default)((0,f.default)((0,f.default)({},"".concat(C,"-logo-rtl"),"rtl"===b),"".concat(C,"-logo-mix"),"mix"===p),"".concat(C,"-logo-mobile"),t)),B=(0,d.jsx)("span",{className:R,children:(0,d.jsx)("a",{children:(0,eY.defaultRenderLogo)(n)})},"logo");return w((0,d.jsxs)("div",{className:M,style:(0,j.default)({},s),children:[t&&(0,d.jsx)("span",{className:"".concat(C,"-collapsed-button ").concat(S).trim(),onClick:function(){null==a||a(!o);},children:(0,d.jsx)(eX.default,{})}),t&&(!1===i?null:i?i(B,null):B),"mix"===p&&!t&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(eY.AppsLogoComponents,(0,j.default)({},e)),(0,d.jsx)("div",{className:R,onClick:r,children:tO((0,j.default)((0,j.default)({},e),{},{collapsed:!1}),"headerTitleRender")})]}),(0,d.jsx)("div",{style:{flex:1},children:m}),(l||e.actionsRender||e.avatarProps)&&(0,d.jsx)(t_,(0,j.default)({rightContentRender:l},e))]}));},tK=function(e){var t,n,o,a;return(0,f.default)({},"".concat(e.proComponentsCls,"-layout"),(0,f.default)({},"".concat(e.antCls,"-layout-header").concat(e.componentCls),{height:(null===(t=e.layout)||void 0===t||null===(t=t.header)||void 0===t?void 0:t.heightLayoutHeader)||56,lineHeight:"".concat((null===(n=e.layout)||void 0===n||null===(n=n.header)||void 0===n?void 0:n.heightLayoutHeader)||56,"px"),zIndex:19,width:"100%",paddingBlock:0,paddingInline:0,borderBlockEnd:"1px solid ".concat(e.colorSplit),backgroundColor:(null===(o=e.layout)||void 0===o||null===(o=o.header)||void 0===o?void 0:o.colorBgHeader)||"rgba(255, 255, 255, 0.4)",WebkitBackdropFilter:"blur(8px)",backdropFilter:"blur(8px)",transition:"background-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)","&-fixed-header":{position:"fixed",insetBlockStart:0,width:"100%",zIndex:100,insetInlineEnd:0},"&-fixed-header-scroll":{backgroundColor:(null===(a=e.layout)||void 0===a||null===(a=a.header)||void 0===a?void 0:a.colorBgScrollHeader)||"rgba(255, 255, 255, 0.8)"},"&-header-actions":{display:"flex",alignItems:"center",fontSize:"16",cursor:"pointer","& &-item":{paddingBlock:0,paddingInline:8,"&:hover":{color:e.colorText}}},"&-header-realDark":{boxShadow:"0 2px 8px 0 rgba(0, 0, 0, 65%)"},"&-header-actions-header-action":{transition:"width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)"}}));},tU=ey.default.Header,tV=function(e){var t,n,o,a,l,i,r,u=e.isMobile,s=e.fixedHeader,p=e.className,m=e.style,h=e.collapsed,v=e.prefixCls,g=e.onCollapse,y=e.layout,x=e.headerRender,b=e.headerContentRender,k=(0,c.useContext)(w.ProProvider).token,S=(0,c.useContext)(ev.default.ConfigContext),M=(0,c.useState)(!1),I=(0,C.default)(M,2),R=I[0],B=I[1],N=s||"mix"===y,T=(0,c.useCallback)(function(){var t="top"===y,n=eU(e.menuData||[]),o=(0,d.jsx)(tG,(0,j.default)((0,j.default)({onCollapse:g},e),{},{menuData:n,children:b&&b(e,null)}));return(t&&!u&&(o=(0,d.jsx)(tW,(0,j.default)((0,j.default)({mode:"horizontal",onCollapse:g},e),{},{menuData:n}))),x&&"function"==typeof x)?x(e,o):o;},[b,x,u,y,g,e]);(0,c.useEffect)(function(){var e,t=(null==S||null===(e=S.getTargetContainer)||void 0===e?void 0:e.call(S))||document.body,n=function(){var e;return t.scrollTop>((null===(e=k.layout)||void 0===e||null===(e=e.header)||void 0===e?void 0:e.heightLayoutHeader)||56)&&!R?(B(!0),!0):(R&&B(!1),!1);};if(N&&"undefined"!=typeof window)return t.addEventListener("scroll",n,{passive:!0}),function(){t.removeEventListener("scroll",n);};},[null===(l=k.layout)||void 0===l||null===(l=l.header)||void 0===l?void 0:l.heightLayoutHeader,N,R]);var O="".concat(v,"-layout-header"),E=(0,e8.useStyle)("ProLayoutHeader",function(e){return[tK((0,j.default)((0,j.default)({},e),{},{componentCls:".".concat(O)}))];}),H=E.wrapSSR,P=E.hashId,A=(t="".concat(O,".").concat(O,"-stylish"),o=(n={proLayoutCollapsedWidth:64,stylish:e.stylish}).stylish,a=n.proLayoutCollapsedWidth,(0,e8.useStyle)("ProLayoutHeaderStylish",function(e){var n=(0,j.default)((0,j.default)({},e),{},{componentCls:".".concat(t),proLayoutCollapsedWidth:a});return o?[(0,f.default)({},"div".concat(e.proComponentsCls,"-layout"),(0,f.default)({},"".concat(n.componentCls),null==o?void 0:o(n)))]:[];})),L=(0,eb.default)(p,P,O,(0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)({},"".concat(O,"-fixed-header"),N),"".concat(O,"-fixed-header-scroll"),R),"".concat(O,"-mix"),"mix"===y),"".concat(O,"-fixed-header-action"),!h),"".concat(O,"-top-menu"),"top"===y),"".concat(O,"-header"),!0),"".concat(O,"-stylish"),!!e.stylish));return"side"!==y||u?A.wrapSSR(H((0,d.jsx)(d.Fragment,{children:(0,d.jsxs)(ev.default,{theme:{hashed:(0,w.isNeedOpenHash)(),components:{Layout:{headerBg:"transparent",bodyBg:"transparent"}}},children:[N&&(0,d.jsx)(tU,{style:(0,j.default)({height:(null===(i=k.layout)||void 0===i||null===(i=i.header)||void 0===i?void 0:i.heightLayoutHeader)||56,lineHeight:"".concat((null===(r=k.layout)||void 0===r||null===(r=r.header)||void 0===r?void 0:r.heightLayoutHeader)||56,"px"),backgroundColor:"transparent",zIndex:19},m)}),(0,d.jsx)(tU,{className:L,style:m,children:T()})]})}))):null;},tX=n("fde35002"),tY=n("7086ad7d"),tZ=n("e90826bf"),tq=i._(tZ),tJ=n("c0466cb3"),tQ=i._(tJ),t0=n("4bd052e8"),t1=r._(t0),t2=n("4e5c2437"),t5=i._(t2),t4=n("3cb616c0"),t8=i._(t4),t6=n("72511d4e"),t3=i._(t6),t9=["prefixCls","className","containerRef"],t7=function(e){var t=e.prefixCls,n=e.className,o=e.containerRef,a=(0,x.default)(e,t9),l=c.useContext(t1.RefContext).panel,i=(0,to.useComposeRef)(l,o);return c.createElement("div",(0,tn.default)({className:(0,eb.default)("".concat(t,"-content"),n),role:"dialog",ref:i},(0,t3.default)(e,{aria:!0}),{"aria-modal":"true"},a));},ne=n("bfab6cb2");function nt(e){return"string"==typeof e&&String(Number(e))===e?((0,eM.default)(!1,"Invalid value type of `width` or `height` which should be number type instead."),Number(e)):e;}i._(ne);var nn={width:0,height:0,overflow:"hidden",outline:"none",position:"absolute"},no=c.forwardRef(function(e,t){var n,o,a,l=e.prefixCls,i=e.open,r=e.placement,d=e.inline,u=e.push,s=e.forceRender,p=e.autoFocus,m=e.keyboard,h=e.classNames,v=e.rootClassName,g=e.rootStyle,y=e.zIndex,x=e.className,b=e.id,k=e.style,w=e.motion,S=e.width,M=e.height,I=e.children,R=e.mask,B=e.maskClosable,N=e.maskMotion,T=e.maskClassName,O=e.maskStyle,E=e.afterOpenChange,H=e.onClose,P=e.onMouseEnter,A=e.onMouseOver,L=e.onMouseLeave,z=e.onClick,D=e.onKeyDown,_=e.onKeyUp,F=e.styles,W=e.drawerRender,$=c.useRef(),G=c.useRef(),K=c.useRef();c.useImperativeHandle(t,function(){return $.current;}),c.useEffect(function(){if(i&&p){var e;null===(e=$.current)||void 0===e||e.focus({preventScroll:!0});}},[i]);var U=c.useState(!1),V=(0,C.default)(U,2),X=V[0],Y=V[1],Z=c.useContext(t1.default),q=null!==(n=null!==(o=null===(a="boolean"==typeof u?u?{}:{distance:0}:u||{})||void 0===a?void 0:a.distance)&&void 0!==o?o:null==Z?void 0:Z.pushDistance)&&void 0!==n?n:180,J=c.useMemo(function(){return{pushDistance:q,push:function(){Y(!0);},pull:function(){Y(!1);}};},[q]);c.useEffect(function(){var e,t;i?null==Z||null===(e=Z.push)||void 0===e||e.call(Z):null==Z||null===(t=Z.pull)||void 0===t||t.call(Z);},[i]),c.useEffect(function(){return function(){var e;null==Z||null===(e=Z.pull)||void 0===e||e.call(Z);};},[]);var Q=c.createElement(t5.default,(0,tn.default)({key:"mask"},N,{visible:R&&i}),function(e,t){var n=e.className,o=e.style;return c.createElement("div",{className:(0,eb.default)("".concat(l,"-mask"),n,null==h?void 0:h.mask,T),style:(0,j.default)((0,j.default)((0,j.default)({},o),O),null==F?void 0:F.mask),onClick:B&&i?H:void 0,ref:t});}),ee="function"==typeof w?w(r):w,et={};if(X&&q)switch(r){case"top":et.transform="translateY(".concat(q,"px)");break;case"bottom":et.transform="translateY(".concat(-q,"px)");break;case"left":et.transform="translateX(".concat(q,"px)");break;default:et.transform="translateX(".concat(-q,"px)");}"left"===r||"right"===r?et.width=nt(S):et.height=nt(M);var en={onMouseEnter:P,onMouseOver:A,onMouseLeave:L,onClick:z,onKeyDown:D,onKeyUp:_},eo=c.createElement(t5.default,(0,tn.default)({key:"panel"},ee,{visible:i,forceRender:s,onVisibleChanged:function(e){null==E||E(e);},removeOnLeave:!1,leavedClassName:"".concat(l,"-content-wrapper-hidden")}),function(t,n){var o=t.className,a=t.style,i=c.createElement(t7,(0,tn.default)({id:b,containerRef:n,prefixCls:l,className:(0,eb.default)(x,null==h?void 0:h.content),style:(0,j.default)((0,j.default)({},k),null==F?void 0:F.content)},(0,t3.default)(e,{aria:!0}),en),I);return c.createElement("div",(0,tn.default)({className:(0,eb.default)("".concat(l,"-content-wrapper"),null==h?void 0:h.wrapper,o),style:(0,j.default)((0,j.default)((0,j.default)({},et),a),null==F?void 0:F.wrapper)},(0,t3.default)(e,{data:!0})),W?W(i):i);}),ea=(0,j.default)({},g);return y&&(ea.zIndex=y),c.createElement(t1.default.Provider,{value:J},c.createElement("div",{className:(0,eb.default)(l,"".concat(l,"-").concat(r),v,(0,f.default)((0,f.default)({},"".concat(l,"-open"),i),"".concat(l,"-inline"),d)),style:ea,tabIndex:-1,ref:$,onKeyDown:function(e){var t,n,o=e.keyCode,a=e.shiftKey;switch(o){case t8.default.TAB:o===t8.default.TAB&&(a||document.activeElement!==K.current?a&&document.activeElement===G.current&&(null===(n=K.current)||void 0===n||n.focus({preventScroll:!0})):null===(t=G.current)||void 0===t||t.focus({preventScroll:!0}));break;case t8.default.ESC:H&&m&&(e.stopPropagation(),H(e));}}},Q,c.createElement("div",{tabIndex:0,ref:G,style:nn,"aria-hidden":"true","data-sentinel":"start"}),eo,c.createElement("div",{tabIndex:0,ref:K,style:nn,"aria-hidden":"true","data-sentinel":"end"})));}),na=function(e){var t=e.open,n=e.prefixCls,o=e.placement,a=e.autoFocus,l=e.keyboard,i=e.width,r=e.mask,d=void 0===r||r,u=e.maskClosable,s=e.getContainer,p=e.forceRender,f=e.afterOpenChange,m=e.destroyOnClose,h=e.onMouseEnter,v=e.onMouseOver,g=e.onMouseLeave,y=e.onClick,x=e.onKeyDown,b=e.onKeyUp,k=e.panelRef,w=c.useState(!1),S=(0,C.default)(w,2),M=S[0],I=S[1],R=c.useState(!1),B=(0,C.default)(R,2),N=B[0],T=B[1];(0,tQ.default)(function(){T(!0);},[]);var O=!!N&&void 0!==t&&t,E=c.useRef(),H=c.useRef();(0,tQ.default)(function(){O&&(H.current=document.activeElement);},[O]);var P=c.useMemo(function(){return{panel:k};},[k]);if(!p&&!M&&!O&&m)return null;var A=(0,j.default)((0,j.default)({},e),{},{open:O,prefixCls:void 0===n?"rc-drawer":n,placement:void 0===o?"right":o,autoFocus:void 0===a||a,keyboard:void 0===l||l,width:void 0===i?378:i,mask:d,maskClosable:void 0===u||u,inline:!1===s,afterOpenChange:function(e){var t,n;I(e),null==f||f(e),e||!H.current||null!==(t=E.current)&&void 0!==t&&t.contains(H.current)||null===(n=H.current)||void 0===n||n.focus({preventScroll:!0});},ref:E},{onMouseEnter:h,onMouseOver:v,onMouseLeave:g,onClick:y,onKeyDown:x,onKeyUp:b});return c.createElement(t1.RefContext.Provider,{value:P},c.createElement(tq.default,{open:O||p||M,autoDestroy:!1,getContainer:s,autoLock:d&&(O||M)},c.createElement(no,A)));},nl=n("5a307f0a"),ni=i._(nl),nr=n("bbe75b70"),nd=n("a4fe2d50");n("20ade671");var nu=n("ed64e120"),ns=i._(nu),nc=n("311adbb5"),np=n("1cedd840"),nf=n("c8db3fa0"),nm=r._(nf);let nh=e=>{var t,n;let{prefixCls:o,title:a,footer:l,extra:i,loading:r,onClose:d,headerStyle:u,bodyStyle:s,footerStyle:p,children:f,classNames:m,styles:h}=e,v=(0,nc.useComponentConfig)("drawer"),g=c.useCallback(e=>c.createElement("button",{type:"button",onClick:d,className:`${o}-close`},e),[d]),[y,x]=(0,nm.default)((0,nm.pickClosable)(e),(0,nm.pickClosable)(v),{closable:!0,closeIconRender:g}),b=c.useMemo(()=>{var e,t;return a||y?c.createElement("div",{style:Object.assign(Object.assign(Object.assign({},null===(e=v.styles)||void 0===e?void 0:e.header),u),null==h?void 0:h.header),className:(0,eb.default)(`${o}-header`,{[`${o}-header-close-only`]:y&&!a&&!i},null===(t=v.classNames)||void 0===t?void 0:t.header,null==m?void 0:m.header)},c.createElement("div",{className:`${o}-header-title`},x,a&&c.createElement("div",{className:`${o}-title`},a)),i&&c.createElement("div",{className:`${o}-extra`},i)):null;},[y,x,i,u,o,a]),C=c.useMemo(()=>{var e,t;if(!l)return null;let n=`${o}-footer`;return c.createElement("div",{className:(0,eb.default)(n,null===(e=v.classNames)||void 0===e?void 0:e.footer,null==m?void 0:m.footer),style:Object.assign(Object.assign(Object.assign({},null===(t=v.styles)||void 0===t?void 0:t.footer),p),null==h?void 0:h.footer)},l);},[l,p,o]);return c.createElement(c.Fragment,null,b,c.createElement("div",{className:(0,eb.default)(`${o}-body`,null==m?void 0:m.body,null===(t=v.classNames)||void 0===t?void 0:t.body),style:Object.assign(Object.assign(Object.assign({},null===(n=v.styles)||void 0===n?void 0:n.body),s),null==h?void 0:h.body)},r?c.createElement(th.default,{active:!0,title:!1,paragraph:{rows:5},className:`${o}-body-skeleton`}):f),C);};var nv=n("081a20ed"),ng=n("8cdc778b"),ny=n("1a2a1fdd"),nx=n("4469bd89");let nb=e=>{let t="100%";return({left:`translateX(-${t})`,right:`translateX(${t})`,top:`translateY(-${t})`,bottom:`translateY(${t})`})[e];},nC=(e,t)=>({"&-enter, &-appear":Object.assign(Object.assign({},e),{"&-active":t}),"&-leave":Object.assign(Object.assign({},t),{"&-active":e})}),nk=(e,t)=>Object.assign({"&-enter, &-appear, &-leave":{"&-start":{transition:"none"},"&-active":{transition:`all ${t}`}}},nC({opacity:e},{opacity:1})),nj=(e,t)=>[nk(.7,t),nC({transform:nb(e)},{transform:"none"})],nw=e=>{let{componentCls:t,motionDurationSlow:n}=e;return{[t]:{[`${t}-mask-motion`]:nk(0,n),[`${t}-panel-motion`]:["left","right","top","bottom"].reduce((e,t)=>Object.assign(Object.assign({},e),{[`&-${t}`]:nj(t,n)}),{})}};},nS=e=>{let{borderRadiusSM:t,componentCls:n,zIndexPopup:o,colorBgMask:a,colorBgElevated:l,motionDurationSlow:i,motionDurationMid:r,paddingXS:d,padding:u,paddingLG:s,fontSizeLG:c,lineHeightLG:p,lineWidth:f,lineType:m,colorSplit:h,marginXS:v,colorIcon:g,colorIconHover:y,colorBgTextHover:x,colorBgTextActive:b,colorText:C,fontWeightStrong:k,footerPaddingBlock:j,footerPaddingInline:w,calc:S}=e,M=`${n}-content-wrapper`;return{[n]:{position:"fixed",inset:0,zIndex:o,pointerEvents:"none",color:C,"&-pure":{position:"relative",background:l,display:"flex",flexDirection:"column",[`&${n}-left`]:{boxShadow:e.boxShadowDrawerLeft},[`&${n}-right`]:{boxShadow:e.boxShadowDrawerRight},[`&${n}-top`]:{boxShadow:e.boxShadowDrawerUp},[`&${n}-bottom`]:{boxShadow:e.boxShadowDrawerDown}},"&-inline":{position:"absolute"},[`${n}-mask`]:{position:"absolute",inset:0,zIndex:o,background:a,pointerEvents:"auto"},[M]:{position:"absolute",zIndex:o,maxWidth:"100vw",transition:`all ${i}`,"&-hidden":{display:"none"}},[`&-left > ${M}`]:{top:0,bottom:0,left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowDrawerLeft},[`&-right > ${M}`]:{top:0,right:{_skip_check_:!0,value:0},bottom:0,boxShadow:e.boxShadowDrawerRight},[`&-top > ${M}`]:{top:0,insetInline:0,boxShadow:e.boxShadowDrawerUp},[`&-bottom > ${M}`]:{bottom:0,insetInline:0,boxShadow:e.boxShadowDrawerDown},[`${n}-content`]:{display:"flex",flexDirection:"column",width:"100%",height:"100%",overflow:"auto",background:l,pointerEvents:"auto"},[`${n}-header`]:{display:"flex",flex:0,alignItems:"center",padding:`${(0,nv.unit)(u)} ${(0,nv.unit)(s)}`,fontSize:c,lineHeight:p,borderBottom:`${(0,nv.unit)(f)} ${m} ${h}`,"&-title":{display:"flex",flex:1,alignItems:"center",minWidth:0,minHeight:0}},[`${n}-extra`]:{flex:"none"},[`${n}-close`]:Object.assign({display:"inline-flex",width:S(c).add(d).equal(),height:S(c).add(d).equal(),borderRadius:t,justifyContent:"center",alignItems:"center",marginInlineEnd:v,color:g,fontWeight:k,fontSize:c,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",textDecoration:"none",background:"transparent",border:0,cursor:"pointer",transition:`all ${r}`,textRendering:"auto","&:hover":{color:y,backgroundColor:x,textDecoration:"none"},"&:active":{backgroundColor:b}},(0,ng.genFocusStyle)(e)),[`${n}-title`]:{flex:1,margin:0,fontWeight:e.fontWeightStrong,fontSize:c,lineHeight:p},[`${n}-body`]:{flex:1,minWidth:0,minHeight:0,padding:s,overflow:"auto",[`${n}-body-skeleton`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center"}},[`${n}-footer`]:{flexShrink:0,padding:`${(0,nv.unit)(j)} ${(0,nv.unit)(w)}`,borderTop:`${(0,nv.unit)(f)} ${m} ${h}`},"&-rtl":{direction:"rtl"}}};};var nM=(0,ny.genStyleHooks)("Drawer",e=>{let t=(0,nx.mergeToken)(e,{});return[nS(t),nw(t)];},e=>({zIndexPopup:e.zIndexPopupBase,footerPaddingBlock:e.paddingXS,footerPaddingInline:e.padding})),nI=this&&this.__rest||function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n;};let nR={distance:180},nB=e=>{let{rootClassName:t,width:n,height:o,size:a="default",mask:l=!0,push:i=nR,open:r,afterOpenChange:d,onClose:u,prefixCls:s,getContainer:p,style:f,className:m,visible:h,afterVisibleChange:v,maskStyle:g,drawerStyle:y,contentWrapperStyle:x,destroyOnClose:b,destroyOnHidden:C}=e,k=nI(e,["rootClassName","width","height","size","mask","push","open","afterOpenChange","onClose","prefixCls","getContainer","style","className","visible","afterVisibleChange","maskStyle","drawerStyle","contentWrapperStyle","destroyOnClose","destroyOnHidden"]),{getPopupContainer:j,getPrefixCls:w,direction:S,className:M,style:I,classNames:R,styles:B}=(0,nc.useComponentConfig)("drawer"),N=w("drawer",s),[T,O,E]=nM(N),H=void 0===p&&j?()=>j(document.body):p,P=(0,eb.default)({"no-mask":!l,[`${N}-rtl`]:"rtl"===S},t,O,E),A=c.useMemo(()=>null!=n?n:"large"===a?736:378,[n,a]),L=c.useMemo(()=>null!=o?o:"large"===a?736:378,[o,a]),z={motionName:(0,nd.getTransitionName)(N,"mask-motion"),motionAppear:!0,motionEnter:!0,motionLeave:!0,motionDeadline:500},D=(0,np.usePanelRef)(),[_,F]=(0,nr.useZIndex)("Drawer",k.zIndex),{classNames:W={},styles:$={}}=k;return T(c.createElement(ni.default,{form:!0,space:!0},c.createElement(ns.default.Provider,{value:F},c.createElement(na,Object.assign({prefixCls:N,onClose:u,maskMotion:z,motion:e=>({motionName:(0,nd.getTransitionName)(N,`panel-motion-${e}`),motionAppear:!0,motionEnter:!0,motionLeave:!0,motionDeadline:500})},k,{classNames:{mask:(0,eb.default)(W.mask,R.mask),content:(0,eb.default)(W.content,R.content),wrapper:(0,eb.default)(W.wrapper,R.wrapper)},styles:{mask:Object.assign(Object.assign(Object.assign({},$.mask),g),B.mask),content:Object.assign(Object.assign(Object.assign({},$.content),y),B.content),wrapper:Object.assign(Object.assign(Object.assign({},$.wrapper),x),B.wrapper)},open:null!=r?r:h,mask:l,push:i,width:A,height:L,style:Object.assign(Object.assign({},I),f),className:(0,eb.default)(M,m),rootClassName:P,getContainer:H,afterOpenChange:null!=d?d:v,panelRef:D,zIndex:_,destroyOnClose:null!=C?C:b}),c.createElement(nh,Object.assign({prefixCls:N},k,{onClose:u}))))));};nB._InternalPanelDoNotUseOrYouWillBeFired=e=>{let{prefixCls:t,style:n,className:o,placement:a="right"}=e,l=nI(e,["prefixCls","style","className","placement"]),{getPrefixCls:i}=c.useContext(nc.ConfigContext),r=i("drawer",t),[d,u,s]=nM(r),p=(0,eb.default)(r,`${r}-pure`,`${r}-${a}`,u,s,o);return d(c.createElement("div",{className:p,style:n},c.createElement(nh,Object.assign({prefixCls:r},l))));};var nN=new nv.Keyframes("antBadgeLoadingCircle",{"0%":{display:"none",opacity:0,overflow:"hidden"},"80%":{overflow:"hidden"},"100%":{display:"unset",opacity:1}}),nT=function(e){var t,n,o,a,l,i,r,d,u,s,c,p;return(0,f.default)({},"".concat(e.proComponentsCls,"-layout"),(0,f.default)((0,f.default)((0,f.default)({},"".concat(e.antCls,"-layout-sider").concat(e.componentCls),{background:(null===(t=e.layout)||void 0===t||null===(t=t.sider)||void 0===t?void 0:t.colorMenuBackground)||"transparent"}),e.componentCls,(0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)({position:"relative",boxSizing:"border-box","&-menu":{position:"relative",zIndex:10,minHeight:"100%"}},"& ".concat(e.antCls,"-layout-sider-children"),{position:"relative",display:"flex",flexDirection:"column",height:"100%",paddingInline:null===(n=e.layout)||void 0===n||null===(n=n.sider)||void 0===n?void 0:n.paddingInlineLayoutMenu,paddingBlock:null===(o=e.layout)||void 0===o||null===(o=o.sider)||void 0===o?void 0:o.paddingBlockLayoutMenu,borderInlineEnd:"1px solid ".concat(e.colorSplit),marginInlineEnd:-1}),"".concat(e.antCls,"-menu"),(0,f.default)((0,f.default)({},"".concat(e.antCls,"-menu-item-group-title"),{fontSize:e.fontSizeSM,paddingBottom:4}),"".concat(e.antCls,"-menu-item:not(").concat(e.antCls,"-menu-item-selected):hover"),{color:null===(a=e.layout)||void 0===a||null===(a=a.sider)||void 0===a?void 0:a.colorTextMenuItemHover})),"&-logo",{position:"relative",display:"flex",alignItems:"center",justifyContent:"space-between",paddingInline:12,paddingBlock:16,color:null===(l=e.layout)||void 0===l||null===(l=l.sider)||void 0===l?void 0:l.colorTextMenu,cursor:"pointer",borderBlockEnd:"1px solid ".concat(null===(i=e.layout)||void 0===i||null===(i=i.sider)||void 0===i?void 0:i.colorMenuItemDivider),"> a":{display:"flex",alignItems:"center",justifyContent:"center",minHeight:22,fontSize:22,"> img":{display:"inline-block",height:22,verticalAlign:"middle"},"> h1":{display:"inline-block",height:22,marginBlock:0,marginInlineEnd:0,marginInlineStart:6,color:null===(r=e.layout)||void 0===r||null===(r=r.sider)||void 0===r?void 0:r.colorTextMenuTitle,animationName:nN,animationDuration:".4s",animationTimingFunction:"ease",fontWeight:600,fontSize:16,lineHeight:"22px",verticalAlign:"middle"}},"&-collapsed":(0,f.default)({flexDirection:"column-reverse",margin:0,padding:12},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginBlockEnd:8,fontSize:16,transition:"font-size 0.2s ease-in-out,color 0.2s ease-in-out"})}),"&-actions",{display:"flex",alignItems:"center",justifyContent:"space-between",marginBlock:4,marginInline:0,color:null===(d=e.layout)||void 0===d||null===(d=d.sider)||void 0===d?void 0:d.colorTextMenu,"&-collapsed":{flexDirection:"column-reverse",paddingBlock:0,paddingInline:8,fontSize:16,transition:"font-size 0.3s ease-in-out"},"&-list":{color:null===(u=e.layout)||void 0===u||null===(u=u.sider)||void 0===u?void 0:u.colorTextMenuSecondary,"&-collapsed":{marginBlockEnd:8,animationName:"none"},"&-item":{paddingInline:6,paddingBlock:6,lineHeight:"16px",fontSize:16,cursor:"pointer",borderRadius:e.borderRadius,"&:hover":{background:e.colorBgTextHover}}},"&-avatar":{fontSize:14,paddingInline:8,paddingBlock:8,display:"flex",alignItems:"center",gap:e.marginXS,borderRadius:e.borderRadius,"& *":{cursor:"pointer"},"&:hover":{background:e.colorBgTextHover}}}),"&-hide-menu-collapsed",{insetInlineStart:"-".concat(e.proLayoutCollapsedWidth-12,"px"),position:"absolute"}),"&-extra",{marginBlockEnd:16,marginBlock:0,marginInline:16,"&-no-logo":{marginBlockStart:16}}),"&-links",{width:"100%",ul:{height:"auto"}}),"&-link-menu",{border:"none",boxShadow:"none",background:"transparent"}),"&-footer",{color:null===(s=e.layout)||void 0===s||null===(s=s.sider)||void 0===s?void 0:s.colorTextMenuSecondary,paddingBlockEnd:16,fontSize:e.fontSize,animationName:nN,animationDuration:".4s",animationTimingFunction:"ease"})),"".concat(e.componentCls).concat(e.componentCls,"-fixed"),{position:"fixed",insetBlockStart:0,insetInlineStart:0,zIndex:"100",height:"100%","&-mix":{height:"calc(100% - ".concat((null===(c=e.layout)||void 0===c||null===(c=c.header)||void 0===c?void 0:c.heightLayoutHeader)||56,"px)"),insetBlockStart:"".concat((null===(p=e.layout)||void 0===p||null===(p=p.header)||void 0===p?void 0:p.heightLayoutHeader)||56,"px")}}));},nO=function(e){var t,n,o=e.isMobile,a=e.siderWidth,l=e.collapsed,i=e.onCollapse,r=e.style,u=e.className,s=e.hide,p=e.prefixCls,f=e.getContainer,m=(0,c.useContext)(w.ProProvider).token;(0,c.useEffect)(function(){!0===o&&(null==i||i(!0));},[o]);var h=(0,ew.default)(e,["className","style"]),v=c.default.useContext(ev.default.ConfigContext).direction,g=(t="".concat(p,"-sider"),(0,e8.useStyle)("ProLayoutSiderMenu",function(e){return[nT((0,j.default)((0,j.default)({},e),{},{componentCls:".".concat(t),proLayoutCollapsedWidth:64}))];})),y=g.wrapSSR,x=g.hashId,b=(0,eb.default)("".concat(p,"-sider"),u,x);if(s)return null;var C=(0,tY.openVisibleCompatible)(!l,function(){return null==i?void 0:i(!0);});return y(o?(0,d.jsx)(nB,(0,j.default)((0,j.default)({placement:"rtl"===v?"right":"left",className:(0,eb.default)("".concat(p,"-drawer-sider"),u)},C),{},{style:(0,j.default)({padding:0,height:"100vh"},r),onClose:function(){null==i||i(!0);},maskClosable:!0,closable:!1,getContainer:f||!1,width:a,styles:{body:{height:"100vh",padding:0,display:"flex",flexDirection:"row",backgroundColor:null===(n=m.layout)||void 0===n||null===(n=n.sider)||void 0===n?void 0:n.colorMenuBackground}},children:(0,d.jsx)(tE,(0,j.default)((0,j.default)({},h),{},{isMobile:!0,className:b,collapsed:!o&&l,splitMenus:!1,originCollapsed:l}))})):(0,d.jsx)(tE,(0,j.default)((0,j.default)({className:b,originCollapsed:l},h),{},{style:r})));},nE=n("37ff088c"),nH=n("be817acd"),nP=function(e,t,n){if(n){var o=(0,te.default)(n.keys()).find(function(t){try{if(t.startsWith("http"))return!1;return(0,nH.match)(t)(e);}catch(e){return console.log("key",t,e),!1;}});if(o)return n.get(o);}if(t){var a=Object.keys(t).find(function(t){try{if(null!=t&&t.startsWith("http"))return!1;return(0,nH.match)(t)(e);}catch(e){return console.log("key",t,e),!1;}});if(a)return t[a];}return{path:""};},nA=function(e,t){var n=e.pathname,o=e.breadcrumb,a=e.breadcrumbMap,l=e.formatMessage,i=e.title,r=e.menu,d=t?"":i||"",u=nP(void 0===n?"/":n,o,a);if(!u)return{title:d,id:"",pageName:d};var s=u.name;return(!1!==(void 0===r?{locale:!1}:r).locale&&u.locale&&l&&(s=l({id:u.locale||"",defaultMessage:u.name})),s)?t||!i?{title:s,id:u.locale||"",pageName:s}:{title:"".concat(s," - ").concat(i),id:u.locale||"",pageName:s}:{title:d,id:u.locale||"",pageName:d};},nL=(0,j.default)({},{"app.setting.pagestyle":"Page style setting","app.setting.pagestyle.dark":"Dark Menu style","app.setting.pagestyle.light":"Light Menu style","app.setting.pagestyle.realdark":"Dark style (Beta)","app.setting.content-width":"Content Width","app.setting.content-width.fixed":"Fixed","app.setting.content-width.fluid":"Fluid","app.setting.themecolor":"Theme Color","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.techBlue":"Tech Blue (default)","app.setting.themecolor.daybreak":"Daybreak Blue","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.sidermenutype":"SideMenu Type","app.setting.sidermenutype-sub":"Classic","app.setting.sidermenutype-group":"Grouping","app.setting.navigationmode":"Navigation Mode","app.setting.regionalsettings":"Regional Settings","app.setting.regionalsettings.header":"Header","app.setting.regionalsettings.menu":"Menu","app.setting.regionalsettings.footer":"Footer","app.setting.regionalsettings.menuHeader":"Menu Header","app.setting.sidemenu":"Side Menu Layout","app.setting.topmenu":"Top Menu Layout","app.setting.mixmenu":"Mix Menu Layout","app.setting.splitMenus":"Split Menus","app.setting.fixedheader":"Fixed Header","app.setting.fixedsidebar":"Fixed Sidebar","app.setting.fixedsidebar.hint":"Works on Side Menu Layout","app.setting.hideheader":"Hidden Header when scrolling","app.setting.hideheader.hint":"Works when Hidden Header is enabled","app.setting.othersettings":"Other Settings","app.setting.weakmode":"Weak Mode","app.setting.copy":"Copy Setting","app.setting.loading":"Loading theme","app.setting.copyinfo":"copy success\uFF0Cplease replace defaultSettings in src/models/setting.js","app.setting.production.hint":"Setting panel shows in development environment only, please manually modify"}),nz=(0,j.default)({},{"app.setting.pagestyle":"Impostazioni di stile","app.setting.pagestyle.dark":"Tema scuro","app.setting.pagestyle.light":"Tema chiaro","app.setting.content-width":"Largezza contenuto","app.setting.content-width.fixed":"Fissa","app.setting.content-width.fluid":"Fluida","app.setting.themecolor":"Colore del tema","app.setting.themecolor.dust":"Rosso polvere","app.setting.themecolor.volcano":"Vulcano","app.setting.themecolor.sunset":"Arancione tramonto","app.setting.themecolor.cyan":"Ciano","app.setting.themecolor.green":"Verde polare","app.setting.themecolor.techBlue":"Tech Blu (default)","app.setting.themecolor.daybreak":"Blu cielo mattutino","app.setting.themecolor.geekblue":"Blu geek","app.setting.themecolor.purple":"Viola dorato","app.setting.navigationmode":"Modalit\xe0 di navigazione","app.setting.sidemenu":"Menu laterale","app.setting.topmenu":"Menu in testata","app.setting.mixmenu":"Menu misto","app.setting.splitMenus":"Menu divisi","app.setting.fixedheader":"Testata fissa","app.setting.fixedsidebar":"Menu laterale fisso","app.setting.fixedsidebar.hint":"Solo se selezionato Menu laterale","app.setting.hideheader":"Nascondi testata durante lo scorrimento","app.setting.hideheader.hint":"Solo se abilitato Nascondi testata durante lo scorrimento","app.setting.othersettings":"Altre impostazioni","app.setting.weakmode":"Inverti colori","app.setting.copy":"Copia impostazioni","app.setting.loading":"Carico tema...","app.setting.copyinfo":"Impostazioni copiate con successo! Incolla il contenuto in config/defaultSettings.js","app.setting.production.hint":"Questo pannello \xe8 visibile solo durante lo sviluppo. Le impostazioni devono poi essere modificate manulamente"}),nD=(0,j.default)({},{"app.setting.pagestyle":"\uC2A4\uD0C0\uC77C \uC124\uC815","app.setting.pagestyle.dark":"\uB2E4\uD06C \uBAA8\uB4DC","app.setting.pagestyle.light":"\uB77C\uC774\uD2B8 \uBAA8\uB4DC","app.setting.content-width":"\uCEE8\uD150\uCE20 \uB108\uBE44","app.setting.content-width.fixed":"\uACE0\uC815","app.setting.content-width.fluid":"\uD750\uB984","app.setting.themecolor":"\uD14C\uB9C8 \uC0C9\uC0C1","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.techBlue":"Tech Blu (default)","app.setting.themecolor.daybreak":"Daybreak Blue","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.navigationmode":"\uB124\uBE44\uAC8C\uC774\uC158 \uBAA8\uB4DC","app.setting.regionalsettings":"\uC601\uC5ED\uBCC4 \uC124\uC815","app.setting.regionalsettings.header":"\uD5E4\uB354","app.setting.regionalsettings.menu":"\uBA54\uB274","app.setting.regionalsettings.footer":"\uBC14\uB2E5\uAE00","app.setting.regionalsettings.menuHeader":"\uBA54\uB274 \uD5E4\uB354","app.setting.sidemenu":"\uBA54\uB274 \uC0AC\uC774\uB4DC \uBC30\uCE58","app.setting.topmenu":"\uBA54\uB274 \uC0C1\uB2E8 \uBC30\uCE58","app.setting.mixmenu":"\uD63C\uD569\uD615 \uBC30\uCE58","app.setting.splitMenus":"\uBA54\uB274 \uBD84\uB9AC","app.setting.fixedheader":"\uD5E4\uB354 \uACE0\uC815","app.setting.fixedsidebar":"\uC0AC\uC774\uB4DC\uBC14 \uACE0\uC815","app.setting.fixedsidebar.hint":"'\uBA54\uB274 \uC0AC\uC774\uB4DC \uBC30\uCE58'\uB97C \uC120\uD0DD\uD588\uC744 \uB54C \uB3D9\uC791\uD568","app.setting.hideheader":"\uC2A4\uD06C\uB864 \uC911 \uD5E4\uB354 \uAC10\uCD94\uAE30","app.setting.hideheader.hint":"'\uD5E4\uB354 \uAC10\uCD94\uAE30 \uC635\uC158'\uC744 \uC120\uD0DD\uD588\uC744 \uB54C \uB3D9\uC791\uD568","app.setting.othersettings":"\uB2E4\uB978 \uC124\uC815","app.setting.weakmode":"\uACE0\uB300\uBE44 \uBAA8\uB4DC","app.setting.copy":"\uC124\uC815\uAC12 \uBCF5\uC0AC","app.setting.loading":"\uD14C\uB9C8 \uB85C\uB529 \uC911","app.setting.copyinfo":"\uBCF5\uC0AC \uC131\uACF5. src/models/settings.js\uC5D0 \uC788\uB294 defaultSettings\uB97C \uAD50\uCCB4\uD574 \uC8FC\uC138\uC694.","app.setting.production.hint":"\uC124\uC815 \uD310\uB12C\uC740 \uAC1C\uBC1C \uD658\uACBD\uC5D0\uC11C\uB9CC \uBCF4\uC5EC\uC9D1\uB2C8\uB2E4. \uC9C1\uC811 \uC218\uB3D9\uC73C\uB85C \uBCC0\uACBD\uBC14\uB78D\uB2C8\uB2E4."}),n_={"zh-CN":(0,j.default)({},{"app.setting.pagestyle":"\u6574\u4F53\u98CE\u683C\u8BBE\u7F6E","app.setting.pagestyle.dark":"\u6697\u8272\u83DC\u5355\u98CE\u683C","app.setting.pagestyle.light":"\u4EAE\u8272\u83DC\u5355\u98CE\u683C","app.setting.pagestyle.realdark":"\u6697\u8272\u98CE\u683C(\u5B9E\u9A8C\u529F\u80FD)","app.setting.content-width":"\u5185\u5BB9\u533A\u57DF\u5BBD\u5EA6","app.setting.content-width.fixed":"\u5B9A\u5BBD","app.setting.content-width.fluid":"\u6D41\u5F0F","app.setting.themecolor":"\u4E3B\u9898\u8272","app.setting.themecolor.dust":"\u8584\u66AE","app.setting.themecolor.volcano":"\u706B\u5C71","app.setting.themecolor.sunset":"\u65E5\u66AE","app.setting.themecolor.cyan":"\u660E\u9752","app.setting.themecolor.green":"\u6781\u5149\u7EFF","app.setting.themecolor.techBlue":"\u79D1\u6280\u84DD\uFF08\u9ED8\u8BA4\uFF09","app.setting.themecolor.daybreak":"\u62C2\u6653","app.setting.themecolor.geekblue":"\u6781\u5BA2\u84DD","app.setting.themecolor.purple":"\u9171\u7D2B","app.setting.navigationmode":"\u5BFC\u822A\u6A21\u5F0F","app.setting.sidermenutype":"\u4FA7\u8FB9\u83DC\u5355\u7C7B\u578B","app.setting.sidermenutype-sub":"\u7ECF\u5178\u6A21\u5F0F","app.setting.sidermenutype-group":"\u5206\u7EC4\u6A21\u5F0F","app.setting.regionalsettings":"\u5185\u5BB9\u533A\u57DF","app.setting.regionalsettings.header":"\u9876\u680F","app.setting.regionalsettings.menu":"\u83DC\u5355","app.setting.regionalsettings.footer":"\u9875\u811A","app.setting.regionalsettings.menuHeader":"\u83DC\u5355\u5934","app.setting.sidemenu":"\u4FA7\u8FB9\u83DC\u5355\u5E03\u5C40","app.setting.topmenu":"\u9876\u90E8\u83DC\u5355\u5E03\u5C40","app.setting.mixmenu":"\u6DF7\u5408\u83DC\u5355\u5E03\u5C40","app.setting.splitMenus":"\u81EA\u52A8\u5206\u5272\u83DC\u5355","app.setting.fixedheader":"\u56FA\u5B9A Header","app.setting.fixedsidebar":"\u56FA\u5B9A\u4FA7\u8FB9\u83DC\u5355","app.setting.fixedsidebar.hint":"\u4FA7\u8FB9\u83DC\u5355\u5E03\u5C40\u65F6\u53EF\u914D\u7F6E","app.setting.hideheader":"\u4E0B\u6ED1\u65F6\u9690\u85CF Header","app.setting.hideheader.hint":"\u56FA\u5B9A Header \u65F6\u53EF\u914D\u7F6E","app.setting.othersettings":"\u5176\u4ED6\u8BBE\u7F6E","app.setting.weakmode":"\u8272\u5F31\u6A21\u5F0F","app.setting.copy":"\u62F7\u8D1D\u8BBE\u7F6E","app.setting.loading":"\u6B63\u5728\u52A0\u8F7D\u4E3B\u9898","app.setting.copyinfo":"\u62F7\u8D1D\u6210\u529F\uFF0C\u8BF7\u5230 src/defaultSettings.js \u4E2D\u66FF\u6362\u9ED8\u8BA4\u914D\u7F6E","app.setting.production.hint":"\u914D\u7F6E\u680F\u53EA\u5728\u5F00\u53D1\u73AF\u5883\u7528\u4E8E\u9884\u89C8\uFF0C\u751F\u4EA7\u73AF\u5883\u4E0D\u4F1A\u5C55\u73B0\uFF0C\u8BF7\u62F7\u8D1D\u540E\u624B\u52A8\u4FEE\u6539\u914D\u7F6E\u6587\u4EF6"}),"zh-TW":(0,j.default)({},{"app.setting.pagestyle":"\u6574\u9AD4\u98A8\u683C\u8A2D\u7F6E","app.setting.pagestyle.dark":"\u6697\u8272\u83DC\u55AE\u98A8\u683C","app.setting.pagestyle.realdark":"\u6697\u8272\u98A8\u683C(\u5B9E\u9A8C\u529F\u80FD)","app.setting.pagestyle.light":"\u4EAE\u8272\u83DC\u55AE\u98A8\u683C","app.setting.content-width":"\u5167\u5BB9\u5340\u57DF\u5BEC\u5EA6","app.setting.content-width.fixed":"\u5B9A\u5BEC","app.setting.content-width.fluid":"\u6D41\u5F0F","app.setting.themecolor":"\u4E3B\u984C\u8272","app.setting.themecolor.dust":"\u8584\u66AE","app.setting.themecolor.volcano":"\u706B\u5C71","app.setting.themecolor.sunset":"\u65E5\u66AE","app.setting.themecolor.cyan":"\u660E\u9752","app.setting.themecolor.green":"\u6975\u5149\u7DA0","app.setting.themecolor.techBlue":"\u79D1\u6280\u84DD\uFF08\u9ED8\u8A8D\uFF09","app.setting.themecolor.daybreak":"\u62C2\u66C9\u85CD","app.setting.themecolor.geekblue":"\u6975\u5BA2\u85CD","app.setting.themecolor.purple":"\u91AC\u7D2B","app.setting.navigationmode":"\u5C0E\u822A\u6A21\u5F0F","app.setting.sidemenu":"\u5074\u908A\u83DC\u55AE\u5E03\u5C40","app.setting.topmenu":"\u9802\u90E8\u83DC\u55AE\u5E03\u5C40","app.setting.mixmenu":"\u6DF7\u5408\u83DC\u55AE\u5E03\u5C40","app.setting.splitMenus":"\u81EA\u52A8\u5206\u5272\u83DC\u5355","app.setting.fixedheader":"\u56FA\u5B9A Header","app.setting.fixedsidebar":"\u56FA\u5B9A\u5074\u908A\u83DC\u55AE","app.setting.fixedsidebar.hint":"\u5074\u908A\u83DC\u55AE\u5E03\u5C40\u6642\u53EF\u914D\u7F6E","app.setting.hideheader":"\u4E0B\u6ED1\u6642\u96B1\u85CF Header","app.setting.hideheader.hint":"\u56FA\u5B9A Header \u6642\u53EF\u914D\u7F6E","app.setting.othersettings":"\u5176\u4ED6\u8A2D\u7F6E","app.setting.weakmode":"\u8272\u5F31\u6A21\u5F0F","app.setting.copy":"\u62F7\u8C9D\u8A2D\u7F6E","app.setting.loading":"\u6B63\u5728\u52A0\u8F09\u4E3B\u984C","app.setting.copyinfo":"\u62F7\u8C9D\u6210\u529F\uFF0C\u8ACB\u5230 src/defaultSettings.js \u4E2D\u66FF\u63DB\u9ED8\u8A8D\u914D\u7F6E","app.setting.production.hint":"\u914D\u7F6E\u6B04\u53EA\u5728\u958B\u767C\u74B0\u5883\u7528\u65BC\u9810\u89BD\uFF0C\u751F\u7522\u74B0\u5883\u4E0D\u6703\u5C55\u73FE\uFF0C\u8ACB\u62F7\u8C9D\u5F8C\u624B\u52D5\u4FEE\u6539\u914D\u7F6E\u6587\u4EF6"}),"en-US":nL,"it-IT":nz,"ko-KR":nD},nF=function(){var e;return void 0===I?e5.default:(null===(e=I)||void 0===e||null===(e=e.env)||void 0===e?void 0:e.ANTD_VERSION)||e5.default;},nW=function(e){var t,n,o,a,l,i,r,d,u,s,c,p,m,h,v,g,y,x,b,C,k,j,w,S,M,I,R,B,N,T,O,E;return null!==(t=nF())&&void 0!==t&&t.startsWith("5")?{}:(0,f.default)((0,f.default)((0,f.default)({},e.componentCls,(0,f.default)((0,f.default)({width:"100%",height:"100%"},"".concat(e.proComponentsCls,"-base-menu"),(k={color:null===(n=e.layout)||void 0===n||null===(n=n.sider)||void 0===n?void 0:n.colorTextMenu},(0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)(k,"".concat(e.antCls,"-menu-sub"),{backgroundColor:"transparent!important",color:null===(o=e.layout)||void 0===o||null===(o=o.sider)||void 0===o?void 0:o.colorTextMenu}),"& ".concat(e.antCls,"-layout"),{backgroundColor:"transparent",width:"100%"}),"".concat(e.antCls,"-menu-submenu-expand-icon, ").concat(e.antCls,"-menu-submenu-arrow"),{color:"inherit"}),"&".concat(e.antCls,"-menu"),(0,f.default)((0,f.default)({color:null===(a=e.layout)||void 0===a||null===(a=a.sider)||void 0===a?void 0:a.colorTextMenu},"".concat(e.antCls,"-menu-item"),{"*":{transition:"none !important"}}),"".concat(e.antCls,"-menu-item a"),{color:"inherit"})),"&".concat(e.antCls,"-menu-inline"),(0,f.default)({},"".concat(e.antCls,"-menu-selected::after,").concat(e.antCls,"-menu-item-selected::after"),{display:"none"})),"".concat(e.antCls,"-menu-sub ").concat(e.antCls,"-menu-inline"),{backgroundColor:"transparent!important"}),"".concat(e.antCls,"-menu-item:active, \n        ").concat(e.antCls,"-menu-submenu-title:active"),{backgroundColor:"transparent!important"}),"&".concat(e.antCls,"-menu-light"),(0,f.default)({},"".concat(e.antCls,"-menu-item:hover, \n            ").concat(e.antCls,"-menu-item-active,\n            ").concat(e.antCls,"-menu-submenu-active, \n            ").concat(e.antCls,"-menu-submenu-title:hover"),(0,f.default)({color:null===(l=e.layout)||void 0===l||null===(l=l.sider)||void 0===l?void 0:l.colorTextMenuActive,borderRadius:e.borderRadius},"".concat(e.antCls,"-menu-submenu-arrow"),{color:null===(i=e.layout)||void 0===i||null===(i=i.sider)||void 0===i?void 0:i.colorTextMenuActive}))),"&".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-horizontal)"),(0,f.default)((0,f.default)({},"".concat(e.antCls,"-menu-item-selected"),{backgroundColor:null===(r=e.layout)||void 0===r||null===(r=r.sider)||void 0===r?void 0:r.colorBgMenuItemSelected,borderRadius:e.borderRadius}),"".concat(e.antCls,"-menu-item:hover, \n            ").concat(e.antCls,"-menu-item-active,\n            ").concat(e.antCls,"-menu-submenu-title:hover"),(0,f.default)({color:null===(d=e.layout)||void 0===d||null===(d=d.sider)||void 0===d?void 0:d.colorTextMenuActive,borderRadius:e.borderRadius,backgroundColor:"".concat(null===(u=e.layout)||void 0===u||null===(u=u.header)||void 0===u?void 0:u.colorBgMenuItemHover," !important")},"".concat(e.antCls,"-menu-submenu-arrow"),{color:null===(s=e.layout)||void 0===s||null===(s=s.sider)||void 0===s?void 0:s.colorTextMenuActive}))),"".concat(e.antCls,"-menu-item-selected"),{color:null===(c=e.layout)||void 0===c||null===(c=c.sider)||void 0===c?void 0:c.colorTextMenuSelected}),(0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)(k,"".concat(e.antCls,"-menu-submenu-selected"),{color:null===(p=e.layout)||void 0===p||null===(p=p.sider)||void 0===p?void 0:p.colorTextMenuSelected}),"&".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-inline) ").concat(e.antCls,"-menu-submenu-open"),{color:null===(m=e.layout)||void 0===m||null===(m=m.sider)||void 0===m?void 0:m.colorTextMenuSelected}),"&".concat(e.antCls,"-menu-vertical"),(0,f.default)({},"".concat(e.antCls,"-menu-submenu-selected"),{borderRadius:e.borderRadius,color:null===(h=e.layout)||void 0===h||null===(h=h.sider)||void 0===h?void 0:h.colorTextMenuSelected})),"".concat(e.antCls,"-menu-submenu:hover > ").concat(e.antCls,"-menu-submenu-title > ").concat(e.antCls,"-menu-submenu-arrow"),{color:null===(v=e.layout)||void 0===v||null===(v=v.sider)||void 0===v?void 0:v.colorTextMenuActive}),"&".concat(e.antCls,"-menu-horizontal"),(0,f.default)((0,f.default)((0,f.default)((0,f.default)({},"".concat(e.antCls,"-menu-item:hover,\n          ").concat(e.antCls,"-menu-submenu:hover,\n          ").concat(e.antCls,"-menu-item-active,\n          ").concat(e.antCls,"-menu-submenu-active"),{borderRadius:4,transition:"none",color:null===(g=e.layout)||void 0===g||null===(g=g.header)||void 0===g?void 0:g.colorTextMenuActive,backgroundColor:"".concat(null===(y=e.layout)||void 0===y||null===(y=y.header)||void 0===y?void 0:y.colorBgMenuItemHover," !important")}),"".concat(e.antCls,"-menu-item-open,\n          ").concat(e.antCls,"-menu-submenu-open,\n          ").concat(e.antCls,"-menu-item-selected,\n          ").concat(e.antCls,"-menu-submenu-selected"),(0,f.default)({backgroundColor:null===(x=e.layout)||void 0===x||null===(x=x.header)||void 0===x?void 0:x.colorBgMenuItemSelected,borderRadius:e.borderRadius,transition:"none",color:"".concat(null===(b=e.layout)||void 0===b||null===(b=b.header)||void 0===b?void 0:b.colorTextMenuSelected," !important")},"".concat(e.antCls,"-menu-submenu-arrow"),{color:"".concat(null===(C=e.layout)||void 0===C||null===(C=C.header)||void 0===C?void 0:C.colorTextMenuSelected," !important")})),"> ".concat(e.antCls,"-menu-item, > ").concat(e.antCls,"-menu-submenu"),{paddingInline:16,marginInline:4}),"> ".concat(e.antCls,"-menu-item::after, > ").concat(e.antCls,"-menu-submenu::after"),{display:"none"})))),"".concat(e.proComponentsCls,"-top-nav-header-base-menu"),(0,f.default)((0,f.default)({},"&".concat(e.antCls,"-menu"),(0,f.default)({color:null===(j=e.layout)||void 0===j||null===(j=j.header)||void 0===j?void 0:j.colorTextMenu},"".concat(e.antCls,"-menu-item a"),{color:"inherit"})),"&".concat(e.antCls,"-menu-light"),(0,f.default)((0,f.default)({},"".concat(e.antCls,"-menu-item:hover, \n            ").concat(e.antCls,"-menu-item-active,\n            ").concat(e.antCls,"-menu-submenu-active, \n            ").concat(e.antCls,"-menu-submenu-title:hover"),(0,f.default)({color:null===(w=e.layout)||void 0===w||null===(w=w.header)||void 0===w?void 0:w.colorTextMenuActive,borderRadius:e.borderRadius,transition:"none",backgroundColor:null===(S=e.layout)||void 0===S||null===(S=S.header)||void 0===S?void 0:S.colorBgMenuItemSelected},"".concat(e.antCls,"-menu-submenu-arrow"),{color:null===(M=e.layout)||void 0===M||null===(M=M.header)||void 0===M?void 0:M.colorTextMenuActive})),"".concat(e.antCls,"-menu-item-selected"),{color:null===(I=e.layout)||void 0===I||null===(I=I.header)||void 0===I?void 0:I.colorTextMenuSelected,borderRadius:e.borderRadius,backgroundColor:null===(R=e.layout)||void 0===R||null===(R=R.header)||void 0===R?void 0:R.colorBgMenuItemSelected})))),"".concat(e.antCls,"-menu-sub").concat(e.antCls,"-menu-inline"),{backgroundColor:"transparent!important"}),"".concat(e.antCls,"-menu-submenu-popup"),(0,f.default)((0,f.default)((0,f.default)((0,f.default)({backgroundColor:"rgba(255, 255, 255, 0.42)","-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)"},"".concat(e.antCls,"-menu"),(0,f.default)({background:"transparent !important",backgroundColor:"transparent !important"},"".concat(e.antCls,"-menu-item:active, \n        ").concat(e.antCls,"-menu-submenu-title:active"),{backgroundColor:"transparent!important"})),"".concat(e.antCls,"-menu-item-selected"),{color:null===(B=e.layout)||void 0===B||null===(B=B.sider)||void 0===B?void 0:B.colorTextMenuSelected}),"".concat(e.antCls,"-menu-submenu-selected"),{color:null===(N=e.layout)||void 0===N||null===(N=N.sider)||void 0===N?void 0:N.colorTextMenuSelected}),"".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-horizontal)"),(0,f.default)((0,f.default)({},"".concat(e.antCls,"-menu-item-selected"),{backgroundColor:"rgba(0, 0, 0, 0.04)",borderRadius:e.borderRadius,color:null===(T=e.layout)||void 0===T||null===(T=T.sider)||void 0===T?void 0:T.colorTextMenuSelected}),"".concat(e.antCls,"-menu-item:hover, \n          ").concat(e.antCls,"-menu-item-active,\n          ").concat(e.antCls,"-menu-submenu-title:hover"),(0,f.default)({color:null===(O=e.layout)||void 0===O||null===(O=O.sider)||void 0===O?void 0:O.colorTextMenuActive,borderRadius:e.borderRadius},"".concat(e.antCls,"-menu-submenu-arrow"),{color:null===(E=e.layout)||void 0===E||null===(E=E.sider)||void 0===E?void 0:E.colorTextMenuActive}))));},n$=function(e){var t,n,o,a;return(0,f.default)((0,f.default)({},"".concat(e.antCls,"-layout"),{backgroundColor:"transparent !important"}),e.componentCls,(0,f.default)((0,f.default)((0,f.default)((0,f.default)({},"& ".concat(e.antCls,"-layout"),{display:"flex",backgroundColor:"transparent",width:"100%"}),"".concat(e.componentCls,"-content"),{display:"flex",flexDirection:"column",width:"100%",backgroundColor:(null===(t=e.layout)||void 0===t||null===(t=t.pageContainer)||void 0===t?void 0:t.colorBgPageContainer)||"transparent",position:"relative",paddingBlock:null===(n=e.layout)||void 0===n||null===(n=n.pageContainer)||void 0===n?void 0:n.paddingBlockPageContainerContent,paddingInline:null===(o=e.layout)||void 0===o||null===(o=o.pageContainer)||void 0===o?void 0:o.paddingInlinePageContainerContent,"&-has-page-container":{padding:0}}),"".concat(e.componentCls,"-container"),{width:"100%",display:"flex",flexDirection:"column",minWidth:0,minHeight:0,backgroundColor:"transparent"}),"".concat(e.componentCls,"-bg-list"),{pointerEvents:"none",position:"fixed",overflow:"hidden",insetBlockStart:0,insetInlineStart:0,zIndex:0,height:"100%",width:"100%",background:null===(a=e.layout)||void 0===a?void 0:a.bgLayout}));},nG=n("ce2a0991"),nK=function(){var e;return void 0===I?e5.default:(null===(e=I)||void 0===e||null===(e=e.env)||void 0===e?void 0:e.ANTD_VERSION)||e5.default;},nU=function(e,t,n){var o=e.breadcrumbName,a=e.title,l=e.path;return n.findIndex(function(t){return t.linkPath===e.path;})===n.length-1?(0,d.jsx)("span",{children:a||o}):(0,d.jsx)("span",{onClick:l?function(){return location.href=l;}:void 0,children:a||o});},nV=function(e,t){var n=t.formatMessage,o=t.menu;return e.locale&&n&&(null==o?void 0:o.locale)!==!1?n({id:e.locale,defaultMessage:e.name}):e.name;},nX=function(e,t){var n=e.get(t);if(!n){var o=(Array.from(e.keys())||[]).find(function(e){try{if(null!=e&&e.startsWith("http"))return!1;return(0,nH.match)(e.replace("?",""))(t);}catch(t){return console.log("path",e,t),!1;}});o&&(n=e.get(o));}return n||{path:""};},nY=function(e){var t={location:e.location,breadcrumbMap:e.breadcrumbMap},n=t.location,o=t.breadcrumbMap;return n&&n.pathname&&o?(function(e){if(!e||"/"===e)return["/"];var t=e.split("/").filter(function(e){return e;});return t.map(function(e,n){return"/".concat(t.slice(0,n+1).join("/"));});})(null==n?void 0:n.pathname).map(function(t){var n=nX(o,t),a=nV(n,e),l=n.hideInBreadcrumb;return a&&!l?{linkPath:t,breadcrumbName:a,title:a,component:n.component}:{linkPath:"",breadcrumbName:"",title:""};}).filter(function(e){return e&&e.linkPath;}):[];},nZ=function(e,t){var n=e.breadcrumbRender,o=e.itemRender,a=(t.breadcrumbProps||{}).minLength,l=nY(e),i=function(e){for(var t=o||nU,n=arguments.length,a=Array(n>1?n-1:0),l=1;l<n;l++)a[l-1]=arguments[l];return null==t?void 0:t.apply(void 0,[(0,j.default)((0,j.default)({},e),{},{path:e.linkPath||e.path})].concat(a));},r=l;return n&&(r=n(r||[])||void 0),(r&&r.length<(void 0===a?2:a)||!1===n)&&(r=void 0),(0,nG.compareVersions)(nK(),"5.3.0")>-1?{items:r,itemRender:i}:{routes:r,itemRender:i};},nq=function e(t,n,o,a){var l=eu(t,(null==n?void 0:n.locale)||!1,o,!0),i=l.menuData,r=l.breadcrumb;return a?e(a(i),n,o,void 0):{breadcrumb:(0,te.default)(r).reduce(function(e,t){var n=(0,C.default)(t,2),o=n[0],a=n[1];return e[o]=a,e;},{}),breadcrumbMap:r,menuData:i};},nJ=n("2a11ab2e"),nQ=i._(nJ),n0=n("aaa78f0a"),n1=function(e){var t=(0,c.useState)({}),n=(0,C.default)(t,2),o=n[0],a=n[1];return(0,c.useEffect)(function(){a((0,n0.omitUndefined)({layout:"object"!==(0,nQ.default)(e.layout)?e.layout:void 0,navTheme:e.navTheme,menuRender:e.menuRender,footerRender:e.footerRender,menuHeaderRender:e.menuHeaderRender,headerRender:e.headerRender,fixSiderbar:e.fixSiderbar}));},[e.layout,e.navTheme,e.menuRender,e.footerRender,e.menuHeaderRender,e.headerRender,e.fixSiderbar]),o;},n2=["id","defaultMessage"],n5=["fixSiderbar","navTheme","layout"],n4=0,n8=function(e,t){var n;return!1===e.headerRender||e.pure?null:(0,d.jsx)(tV,(0,j.default)((0,j.default)({matchMenuKeys:t},e),{},{stylish:null===(n=e.stylish)||void 0===n?void 0:n.header}));},n6=function(e,t){var n,o=e.layout,a=e.isMobile,l=e.selectedKeys,i=e.openKeys,r=e.splitMenus,u=e.suppressSiderWhenMenuEmpty,s=e.menuRender;if(!1===e.menuRender||e.pure)return null;var c=e.menuData;if(r&&(!1!==i||"mix"===o)&&!a){var p,f,m=l||t,h=(0,C.default)(m,1)[0];c=h&&(null===(f=e.menuData)||void 0===f||null===(f=f.find(function(e){return e.key===h;}))||void 0===f?void 0:f.children)||[];}var v=eU(c||[]);if(v&&(null==v?void 0:v.length)<1&&(r||u))return null;if("top"===o&&!a)return(0,d.jsx)(nO,(0,j.default)((0,j.default)({matchMenuKeys:t},e),{},{hide:!0,stylish:null===(n=e.stylish)||void 0===n?void 0:n.sider}));var g=(0,d.jsx)(nO,(0,j.default)((0,j.default)({matchMenuKeys:t},e),{},{menuData:v,stylish:null===(p=e.stylish)||void 0===p?void 0:p.sider}));return s?s(e,g):g;},n3=function(e,t){var n=t.pageTitleRender,o=nA(e);if(!1===n)return{title:t.title||"",id:"",pageName:""};if(n){var a=n(e,o.title,o);if("string"==typeof a)return nA((0,j.default)((0,j.default)({},o),{},{title:a}));(0,eM.default)("string"==typeof a,"pro-layout: renderPageTitle return value should be a string");}return o;},n9=function(e){var t,n,o,a,l,i,r,u,s,p,m,v,y,b,k,I,R,B,N=e||{},E=N.children,H=N.onCollapse,P=N.location,A=void 0===P?{pathname:"/"}:P,L=N.contentStyle,z=N.route,D=N.defaultCollapsed,_=N.style,F=N.siderWidth,W=N.menu,$=N.siderMenuType,G=N.isChildrenLayout,K=N.menuDataRender,U=N.actionRef,V=N.bgLayoutImgList,X=N.formatMessage,Y=N.loading,Z=(0,c.useMemo)(function(){return F||("mix"===e.layout?215:256);},[e.layout,F]),q=(0,c.useContext)(ev.default.ConfigContext),J=null!==(l=e.prefixCls)&&void 0!==l?l:q.getPrefixCls("pro"),Q=(0,O.useMountMergeState)(!1,{value:null==W?void 0:W.loading,onChange:null==W?void 0:W.onLoadingChange}),ee=(0,C.default)(Q,2),et=ee[0],en=ee[1],eo=(0,c.useState)(function(){return n4+=1,"pro-layout-".concat(n4);}),ea=(0,C.default)(eo,1)[0],el=(0,c.useCallback)(function(e){var t=e.id,n=e.defaultMessage,o=(0,x.default)(e,n2);if(X)return X((0,j.default)({id:t,defaultMessage:n},o));var a=n_[(0,M.isBrowser)()?window.localStorage.getItem("umi_locale")||window.g_locale||navigator.language:"zh-CN"]||n_["zh-CN"];return a[t]?a[t]:n;},[X]),ei=(0,eR.default)([ea,null==W?void 0:W.params],(t=(0,g.default)((0,h.default)().mark(function e(t){var n,o,a;return(0,h.default)().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return o=(0,C.default)(t,2)[1],en(!0),e.next=4,null==W||null===(n=W.request)||void 0===n?void 0:n.call(W,o||{},(null==z?void 0:z.children)||(null==z?void 0:z.routes)||[]);case 4:return a=e.sent,en(!1),e.abrupt("return",a);case 7:case"end":return e.stop();}},e);})),function(e){return t.apply(this,arguments);}),{revalidateOnFocus:!1,shouldRetryOnError:!1,revalidateOnReconnect:!1}),er=ei.data,ed=ei.mutate,eu=ei.isLoading;(0,c.useEffect)(function(){en(eu);},[eu]);var es=(0,eR.useSWRConfig)().cache;(0,c.useEffect)(function(){return function(){es instanceof Map&&es.delete(ea);};},[]);var ec=(0,c.useMemo)(function(){return nq(er||(null==z?void 0:z.children)||(null==z?void 0:z.routes)||[],W,el,K);},[el,W,K,er,null==z?void 0:z.children,null==z?void 0:z.routes])||{},ep=ec.breadcrumb,ef=ec.breadcrumbMap,eh=ec.menuData,eg=void 0===eh?[]:eh;U&&null!=W&&W.request&&(U.current={reload:function(){ed();}});var ex=(0,c.useMemo)(function(){return em(A.pathname||"/",eg||[],!0);},[A.pathname,eg]),eC=(0,c.useMemo)(function(){return Array.from(new Set(ex.map(function(e){return e.key||e.path||"";})));},[ex]),ej=ex[ex.length-1]||{},eS=n1(ej),eM=(0,j.default)((0,j.default)({},e),eS),eI=eM.fixSiderbar,eB=(eM.navTheme,eM.layout),eN=(0,x.default)(eM,n5),eT=T(),eO=(0,c.useMemo)(function(){return("sm"===eT||"xs"===eT)&&!e.disableMobile;},[eT,e.disableMobile]),eE="top"!==eB&&!eO,eH=(0,ek.default)(function(){return void 0!==D?D:!!eO||"md"===eT;},{value:e.collapsed,onChange:H}),eP=(0,C.default)(eH,2),eA=eP[0],eL=eP[1],ez=(0,ew.default)((0,j.default)((0,j.default)((0,j.default)({prefixCls:J},e),{},{siderWidth:Z},eS),{},{formatMessage:el,breadcrumb:ep,menu:(0,j.default)((0,j.default)({},W),{},{type:$||(null==W?void 0:W.type),loading:et}),layout:eB}),["className","style","breadcrumbRender"]),eD=n3((0,j.default)((0,j.default)({pathname:A.pathname},ez),{},{breadcrumbMap:ef}),e),e_=nZ((0,j.default)((0,j.default)({},ez),{},{breadcrumbRender:e.breadcrumbRender,breadcrumbMap:ef}),e),eF=n6((0,j.default)((0,j.default)({},ez),{},{menuData:eg,onCollapse:eL,isMobile:eO,collapsed:eA}),eC),e$=n8((0,j.default)((0,j.default)({},ez),{},{children:null,hasSiderMenu:!!eF,menuData:eg,isMobile:eO,collapsed:eA,onCollapse:eL}),eC),eK=!1===(n=(0,j.default)({isMobile:eO,collapsed:eA},ez)).footerRender||n.pure?null:n.footerRender?n.footerRender((0,j.default)({},n),(0,d.jsx)(eG.DefaultFooter,{})):null,eU=(0,c.useContext)(nE.RouteContext).isChildrenLayout,eV=void 0!==G?G:eU,eX="".concat(J,"-layout"),eY=(0,e8.useStyle)("ProLayout",function(e){var t=(0,j.default)((0,j.default)({},e),{},{componentCls:".".concat(eX)});return[n$(t),nW(t)];}),eZ=eY.wrapSSR,eq=eY.hashId,eJ=(0,eb.default)(e.className,eq,"ant-design-pro",eX,(0,f.default)((0,f.default)((0,f.default)((0,f.default)((0,f.default)({},"screen-".concat(eT),eT),"".concat(eX,"-top-menu"),"top"===eB),"".concat(eX,"-is-children"),eV),"".concat(eX,"-fix-siderbar"),eI),"".concat(eX,"-").concat(eB),eB)),eQ=eE?eA?64:Z:0,e0={position:"relative"};(eV||L&&L.minHeight)&&(e0.minHeight=0),(0,c.useEffect)(function(){var t;null===(t=e.onPageChange)||void 0===t||t.call(e,e.location);},[A.pathname,null===(i=A.pathname)||void 0===i?void 0:i.search]);var e1=(0,c.useState)(!1),e2=(0,C.default)(e1,2),e5=e2[0],e4=e2[1],e6=(0,c.useState)(0),e3=(0,C.default)(e6,2),e9=e3[0],e7=e3[1];o=e.title||!1,a="string"==typeof eD.pageName?eD.title:o,(0,c.useEffect)(function(){(0,M.isBrowser)()&&a&&(document.title=a);},[eD.title,a]);var te=(0,c.useContext)(w.ProProvider).token,tt=(0,c.useMemo)(function(){return V&&V.length>0?null==V?void 0:V.map(function(e,t){return(0,d.jsx)("img",{src:e.src,style:(0,j.default)({position:"absolute"},e)},t);}):null;},[V]);return eZ((0,d.jsx)(nE.RouteContext.Provider,{value:(0,j.default)((0,j.default)({},ez),{},{breadcrumb:e_,menuData:eg,isMobile:eO,collapsed:eA,hasPageContainer:e9,setHasPageContainer:e7,isChildrenLayout:!0,title:eD.pageName,hasSiderMenu:!!eF,hasHeader:!!e$,siderWidth:eQ,hasFooter:!!eK,hasFooterToolbar:e5,setHasFooterToolbar:e4,pageTitleInfo:eD,matchMenus:ex,matchMenuKeys:eC,currentMenu:ej}),children:e.pure?(0,d.jsx)(d.Fragment,{children:E}):(0,d.jsxs)("div",{className:eJ,children:[tt||null!==(r=te.layout)&&void 0!==r&&r.bgLayout?(0,d.jsx)("div",{className:(0,eb.default)("".concat(eX,"-bg-list"),eq),children:tt}):null,(0,d.jsxs)(ey.default,{style:(0,j.default)({minHeight:"100%",flexDirection:eF?"row":void 0},_),children:[(0,d.jsx)(ev.default,{theme:{hashed:(0,w.isNeedOpenHash)(),token:{controlHeightLG:(null===(u=te.layout)||void 0===u||null===(u=u.sider)||void 0===u?void 0:u.menuHeight)||(null==te?void 0:te.controlHeightLG)},components:{Menu:(0,S.coverToNewToken)({colorItemBg:(null===(s=te.layout)||void 0===s||null===(s=s.sider)||void 0===s?void 0:s.colorMenuBackground)||"transparent",colorSubItemBg:(null===(p=te.layout)||void 0===p||null===(p=p.sider)||void 0===p?void 0:p.colorMenuBackground)||"transparent",radiusItem:te.borderRadius,colorItemBgSelected:(null===(m=te.layout)||void 0===m||null===(m=m.sider)||void 0===m?void 0:m.colorBgMenuItemSelected)||(null==te?void 0:te.colorBgTextHover),colorItemBgHover:(null===(v=te.layout)||void 0===v||null===(v=v.sider)||void 0===v?void 0:v.colorBgMenuItemHover)||(null==te?void 0:te.colorBgTextHover),colorItemBgActive:(null===(y=te.layout)||void 0===y||null===(y=y.sider)||void 0===y?void 0:y.colorBgMenuItemActive)||(null==te?void 0:te.colorBgTextActive),colorItemBgSelectedHorizontal:(null===(b=te.layout)||void 0===b||null===(b=b.sider)||void 0===b?void 0:b.colorBgMenuItemSelected)||(null==te?void 0:te.colorBgTextHover),colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemText:(null===(k=te.layout)||void 0===k||null===(k=k.sider)||void 0===k?void 0:k.colorTextMenu)||(null==te?void 0:te.colorTextSecondary),colorItemTextHover:(null===(I=te.layout)||void 0===I||null===(I=I.sider)||void 0===I?void 0:I.colorTextMenuItemHover)||"rgba(0, 0, 0, 0.85)",colorItemTextSelected:(null===(R=te.layout)||void 0===R||null===(R=R.sider)||void 0===R?void 0:R.colorTextMenuSelected)||"rgba(0, 0, 0, 1)",popupBg:null==te?void 0:te.colorBgElevated,subMenuItemBg:null==te?void 0:te.colorBgElevated,darkSubMenuItemBg:"transparent",darkPopupBg:null==te?void 0:te.colorBgElevated})}},children:eF}),(0,d.jsxs)("div",{style:e0,className:"".concat(eX,"-container ").concat(eq).trim(),children:[e$,(0,d.jsx)(eW,(0,j.default)((0,j.default)({hasPageContainer:e9,isChildrenLayout:eV},eN),{},{hasHeader:!!e$,prefixCls:eX,style:L,children:Y?(0,d.jsx)(tX.PageLoading,{}):E})),eK,e5&&(0,d.jsx)("div",{className:"".concat(eX,"-has-footer"),style:{height:64,marginBlockStart:null===(B=te.layout)||void 0===B||null===(B=B.pageContainer)||void 0===B?void 0:B.paddingBlockPageContainerContent}})]})]})]})}));},n7=function(e){var t=e.colorPrimary,n=void 0!==e.navTheme?{dark:"realDark"===e.navTheme}:{};return(0,d.jsx)(ev.default,{theme:t?{token:{colorPrimary:t}}:void 0,children:(0,d.jsx)(w.ProConfigProvider,(0,j.default)((0,j.default)({},n),{},{token:e.token,prefixCls:e.prefixCls,children:(0,d.jsx)(n9,(0,j.default)((0,j.default)({logo:(0,d.jsx)(e$,{})},ty),{},{location:(0,M.isBrowser)()?window.location:void 0},e))}))});};let oe=()=>(0,d.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 200 200",children:[(0,d.jsxs)("defs",{children:[(0,d.jsxs)("linearGradient",{id:"linearGradient-1",x1:"62.102%",x2:"108.197%",y1:"0%",y2:"37.864%",children:[(0,d.jsx)("stop",{offset:"0%",stopColor:"#4285EB"}),(0,d.jsx)("stop",{offset:"100%",stopColor:"#2EC7FF"})]}),(0,d.jsxs)("linearGradient",{id:"linearGradient-2",x1:"69.644%",x2:"54.043%",y1:"0%",y2:"108.457%",children:[(0,d.jsx)("stop",{offset:"0%",stopColor:"#29CDFF"}),(0,d.jsx)("stop",{offset:"37.86%",stopColor:"#148EFF"}),(0,d.jsx)("stop",{offset:"100%",stopColor:"#0A60FF"})]}),(0,d.jsxs)("linearGradient",{id:"linearGradient-3",x1:"69.691%",x2:"16.723%",y1:"-12.974%",y2:"117.391%",children:[(0,d.jsx)("stop",{offset:"0%",stopColor:"#FA816E"}),(0,d.jsx)("stop",{offset:"41.473%",stopColor:"#F74A5C"}),(0,d.jsx)("stop",{offset:"100%",stopColor:"#F51D2C"})]}),(0,d.jsxs)("linearGradient",{id:"linearGradient-4",x1:"68.128%",x2:"30.44%",y1:"-35.691%",y2:"114.943%",children:[(0,d.jsx)("stop",{offset:"0%",stopColor:"#FA8E7D"}),(0,d.jsx)("stop",{offset:"51.264%",stopColor:"#F74A5C"}),(0,d.jsx)("stop",{offset:"100%",stopColor:"#F51D2C"})]})]}),(0,d.jsx)("g",{fill:"none",fillRule:"evenodd",stroke:"none",strokeWidth:"1",children:(0,d.jsx)("g",{transform:"translate(-20 -20)",children:(0,d.jsx)("g",{transform:"translate(20 20)",children:(0,d.jsxs)("g",{children:[(0,d.jsxs)("g",{fillRule:"nonzero",children:[(0,d.jsxs)("g",{children:[(0,d.jsx)("path",{fill:"url(#linearGradient-1)",d:"M91.588 4.177L4.18 91.513a11.981 11.981 0 000 16.974l87.408 87.336a12.005 12.005 0 0016.989 0l36.648-36.618c4.209-4.205 4.209-11.023 0-15.228-4.208-4.205-11.031-4.205-15.24 0l-27.783 27.76c-1.17 1.169-2.945 1.169-4.114 0l-69.802-69.744c-1.17-1.169-1.17-2.942 0-4.11l69.802-69.745c1.17-1.169 2.944-1.169 4.114 0l27.783 27.76c4.209 4.205 11.032 4.205 15.24 0 4.209-4.205 4.209-11.022 0-15.227L108.581 4.056c-4.719-4.594-12.312-4.557-16.993.12z"}),(0,d.jsx)("path",{fill:"url(#linearGradient-2)",d:"M91.588 4.177L4.18 91.513a11.981 11.981 0 000 16.974l87.408 87.336a12.005 12.005 0 0016.989 0l36.648-36.618c4.209-4.205 4.209-11.023 0-15.228-4.208-4.205-11.031-4.205-15.24 0l-27.783 27.76c-1.17 1.169-2.945 1.169-4.114 0l-69.802-69.744c-1.17-1.169-1.17-2.942 0-4.11l69.802-69.745c2.912-2.51 7.664-7.596 14.642-8.786 5.186-.883 10.855 1.062 17.009 5.837L108.58 4.056c-4.719-4.594-12.312-4.557-16.993.12z"})]}),(0,d.jsx)("path",{fill:"url(#linearGradient-3)",d:"M153.686 135.855c4.208 4.205 11.031 4.205 15.24 0l27.034-27.012c4.7-4.696 4.7-12.28 0-16.974l-27.27-27.15c-4.218-4.2-11.043-4.195-15.254.013-4.209 4.205-4.209 11.022 0 15.227l18.418 18.403c1.17 1.169 1.17 2.943 0 4.111l-18.168 18.154c-4.209 4.205-4.209 11.023 0 15.228z"})]}),(0,d.jsx)("ellipse",{cx:"100.519",cy:"100.437",fill:"url(#linearGradient-4)",rx:"23.6",ry:"23.581"})]})})})})]});var ot=n("4e1013a7"),on=i._(ot);let oo=e=>{var t,n;return!e.route&&(e.noFound||e.notFound)||(null===(t=e.route)||void 0===t?void 0:t.unaccessible)&&(e.unAccessible||e.noAccessible)||(!e.route||(null===(n=e.route)||void 0===n?void 0:n.unaccessible))&&(0,d.jsx)(e_.default,{status:e.route?"403":"404",title:e.route?"403":"404",subTitle:e.route?"\u62B1\u6B49\uFF0C\u4F60\u65E0\u6743\u8BBF\u95EE\u8BE5\u9875\u9762":"\u62B1\u6B49\uFF0C\u4F60\u8BBF\u95EE\u7684\u9875\u9762\u4E0D\u5B58\u5728",extra:(0,d.jsx)(on.default,{type:"primary",onClick:()=>u.history.push("/"),children:"\u8FD4\u56DE\u9996\u9875"})})||e.children;};var oa=n("ab3d3880"),ol=i._(oa),oi=n("c9c5d7af"),or=i._(oi),od=n("5bb94c90"),ou=i._(od),os=n("bb2a7fe6"),oc=i._(os),op=c.forwardRef(function(e,t){return c.createElement(oc.default,(0,j.default)((0,j.default)({},e),{},{ref:t,icon:ou.default}));}),of=n("016aeb36"),om=n("75bdba54");let oh=(e,t)=>{if(0===e.length)return[];let n=[];for(let o of e){let e={...o};t(o)?Array.isArray(e.routes)&&n.push(...oh(e.routes,t)):(Array.isArray(e.children)&&(e.children=oh(e.children,t),e.routes=e.children),n.push(e));}return n;},ov=e=>0===e.length?[]:e.map(e=>{let t={...e};return e.originPath&&(t.path=e.originPath),Array.isArray(e.routes)&&(t.routes=ov(e.routes)),Array.isArray(e.children)&&(t.children=ov(e.children)),t;});var og=e=>{let t=(0,u.useLocation)(),n=(0,u.useNavigate)(),{clientRoutes:o,pluginManager:a}=(0,u.useAppData)(),l=of.useModel&&(0,of.useModel)("@@initialState")||{initialState:void 0,loading:!1,setInitialState:null},{initialState:i,loading:r,setInitialState:s}=l,p={locale:!1,navTheme:"light",colorPrimary:"#1890ff",layout:"side",contentWidth:"Fluid",fixedHeader:!1,fixSiderbar:!0,colorWeak:!1,title:"\u56E2\u961F\u534F\u4F5C\u7BA1\u7406\u7CFB\u7EDF",pwa:!1,logo:"/logo.svg",iconfontUrl:"",token:{}},f=a.applyPlugins({key:"layout",type:"modify",initialValue:{...l}}),m=oh(o.filter(e=>"ant-design-pro-layout"===e.id),e=>!!e.isLayout&&"ant-design-pro-layout"!==e.id||!!e.isWrapper),[h]=(0,om.useAccessMarkedRoutes)(ov(m)),v=(0,c.useMemo)(()=>{var e,n,o;return null===(o=(0,u.matchRoutes)(h.children,t.pathname))||void 0===o?void 0:null===(n=o.pop)||void 0===n?void 0:null===(e=n.call(o))||void 0===e?void 0:e.route;},[t.pathname]);return(0,d.jsx)(n7,{route:h,location:t,title:p.title||"teamauth-frontend",navTheme:"dark",siderWidth:256,onMenuHeaderClick:e=>{e.stopPropagation(),e.preventDefault(),n("/");},formatMessage:p.formatMessage||void 0,menu:{locale:p.locale},logo:oe,menuItemRender:(e,n)=>e.isUrl||e.children?n:e.path&&t.pathname!==e.path?(0,d.jsx)(u.Link,{to:e.path.replace("/*",""),target:e.target,children:n}):n,itemRender:(e,t,n)=>{let{breadcrumbName:o,title:a,path:l}=e,i=a||o,r=n[n.length-1];return r&&(r.path===l||r.linkPath===l)?(0,d.jsx)("span",{children:i}):(0,d.jsx)(u.Link,{to:l,children:i});},disableContentMargin:!0,fixSiderbar:!0,fixedHeader:!0,...f,rightContentRender:!1!==f.rightContentRender&&(e=>{let t=function(e){var t,n,o,a,l;let i;if(e.runtimeConfig.rightRender)return e.runtimeConfig.rightRender(e.initialState,e.setInitialState,e.runtimeConfig);let r=(null===(t=e.initialState)||void 0===t?void 0:t.avatar)||(null===(n=e.initialState)||void 0===n?void 0:n.name)||e.runtimeConfig.logout,u=(null===(o=e.initialState)||void 0===o?void 0:o.avatar)===!1,s=r?(0,d.jsxs)("span",{className:"umi-plugin-layout-action",children:[u?null:(0,d.jsx)(eq.default,{size:"small",className:"umi-plugin-layout-avatar",src:(null===(a=e.initialState)||void 0===a?void 0:a.avatar)||"https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png",alt:"avatar"}),(0,d.jsx)("span",{className:u?"umi-plugin-layout-name umi-plugin-layout-hide-avatar-img":"umi-plugin-layout-name",children:null===(l=e.initialState)||void 0===l?void 0:l.name})]}):null;if(e.loading)return(0,d.jsx)("div",{className:"umi-plugin-layout-right",children:(0,d.jsx)(or.default,{size:"small",style:{marginLeft:8,marginRight:8}})});if(!s)return null;let c={className:"umi-plugin-layout-menu",selectedKeys:[],items:[{key:"logout",label:(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(op,{}),"\u9000\u51FA\u767B\u5F55"]}),onClick:()=>{var t,n;null==e||null===(n=e.runtimeConfig)||void 0===n||null===(t=n.logout)||void 0===t||t.call(n,e.initialState);}}]};return i=e5.default.startsWith("5.")||e5.default.startsWith("4.24.")?{menu:c}:e5.default.startsWith("3.")?{overlay:(0,d.jsx)(eQ.default,{children:c.items.map(e=>(0,d.jsx)(eQ.default.Item,{onClick:e.onClick,children:e.label},e.key))})}:{overlay:(0,d.jsx)(eQ.default,{...c})},(0,d.jsx)("div",{className:"umi-plugin-layout-right anticon",children:e.runtimeConfig.logout?(0,d.jsx)(ol.default,{...i,overlayClassName:"umi-plugin-layout-container",children:s}):s});}({runtimeConfig:f,loading:r,initialState:i,setInitialState:s});return f.rightContentRender?f.rightContentRender(e,t,{userConfig:p,runtimeConfig:f,loading:r,initialState:i,setInitialState:s}):t;}),children:(0,d.jsx)(oo,{route:v,noFound:null==f?void 0:f.noFound,notFound:null==f?void 0:f.notFound,unAccessible:null==f?void 0:f.unAccessible,noAccessible:null==f?void 0:f.noAccessible,children:f.childrenRender?f.childrenRender((0,d.jsx)(u.Outlet,{}),e):(0,d.jsx)(u.Outlet,{})})});};}}]);
//# sourceMappingURL=d273031b-async.81835b75.js.map