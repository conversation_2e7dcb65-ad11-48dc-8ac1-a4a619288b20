{"version": 3, "sources": ["src/pages/invite/[token].tsx"], "sourcesContent": ["/**\n * 邀请链接处理页面\n * 路由: /invite/:token\n */\n\nimport { TeamOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';\nimport { Helmet, history, useParams } from '@umijs/max';\nimport {\n  Button,\n  Card,\n  Space,\n  Typography,\n  Result,\n  Spin,\n  Alert,\n  Divider,\n} from 'antd';\nimport { createStyles } from 'antd-style';\nimport React, { useState, useEffect } from 'react';\nimport { Footer } from '@/components';\nimport { InvitationService } from '@/services';\nimport type { InvitationInfoResponse } from '@/types/api';\nimport { TokenManager } from '@/utils/request';\nimport Settings from '../../../config/defaultSettings';\n\nconst { Title, Text, Paragraph } = Typography;\n\nconst useStyles = createStyles(({ token }) => {\n  return {\n    container: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh',\n      overflow: 'auto',\n      backgroundImage:\n        \"url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')\",\n      backgroundSize: '100% 100%',\n    },\n    content: {\n      flex: 1,\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: '32px 16px',\n    },\n    header: {\n      marginBottom: 40,\n      textAlign: 'center',\n    },\n    logo: {\n      marginBottom: 16,\n    },\n    title: {\n      marginBottom: 0,\n    },\n    inviteCard: {\n      width: '100%',\n      maxWidth: 500,\n      boxShadow: token.boxShadowTertiary,\n    },\n    footer: {\n      marginTop: 40,\n      textAlign: 'center',\n    },\n  };\n});\n\nconst InvitePage: React.FC = () => {\n  const { token } = useParams<{ token: string }>();\n  const [loading, setLoading] = useState(true);\n  const [processing, setProcessing] = useState(false);\n  const [invitationInfo, setInvitationInfo] = useState<InvitationInfoResponse | null>(null);\n  const [result, setResult] = useState<any>(null);\n  const { styles } = useStyles();\n\n  // 获取邀请信息\n  const fetchInvitationInfo = async () => {\n    if (!token) {\n      setResult({\n        type: 'error',\n        title: '邀请链接无效',\n        message: '邀请链接格式错误或已过期',\n      });\n      setLoading(false);\n      return;\n    }\n\n    try {\n      const response = await InvitationService.getInvitationInfo(token);\n\n      if (response.success) {\n        setInvitationInfo(response);\n      } else {\n        setResult({\n          type: 'error',\n          title: '邀请链接无效',\n          message: response.errorMessage || '无法获取邀请信息',\n        });\n      }\n    } catch (error) {\n      console.error('获取邀请信息失败:', error);\n      setResult({\n        type: 'error',\n        title: '获取邀请信息失败',\n        message: '网络错误，请稍后重试',\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 处理邀请接受\n  const handleAcceptInvitation = async () => {\n    if (!token) return;\n\n    setProcessing(true);\n    try {\n      const response = await InvitationService.acceptInvitationByLink(token, {});\n\n      if (response.success) {\n        // 如果是新用户且返回了访问令牌，自动登录\n        if (response.isNewUser && response.accessToken) {\n          try {\n            TokenManager.setToken(response.accessToken);\n            console.log('新用户自动创建并登录成功（无需密码）');\n\n            setResult({\n              type: 'success',\n              title: '欢迎加入团队！',\n              message: `您的账号已自动创建并登录（无需密码）。${response.nextAction || '正在跳转到个人中心...'}`,\n              teamName: response.teamName,\n              isNewUser: response.isNewUser,\n              autoLogin: true,\n            });\n\n            // 延迟跳转到个人中心\n            setTimeout(() => {\n              history.push('/personal-center');\n            }, 3000);\n          } catch (error) {\n            console.error('自动登录失败:', error);\n            setResult({\n              type: 'success',\n              title: '账号创建成功！',\n              message: '您的账号已成功创建（无需密码），请使用邮箱验证码登录后查看团队信息。',\n              teamName: response.teamName,\n              isNewUser: response.isNewUser,\n              autoLogin: false,\n            });\n          }\n        } else if (response.isNewUser) {\n          // 新用户但没有自动登录令牌\n          setResult({\n            type: 'success',\n            title: '账号创建成功！',\n            message: '您的账号已成功创建（无需密码），请使用邮箱验证码登录后查看团队信息。',\n            teamName: response.teamName,\n            isNewUser: response.isNewUser,\n            autoLogin: false,\n          });\n        } else {\n          // 现有用户\n          setResult({\n            type: 'success',\n            title: '加入成功！',\n            message: response.nextAction || '您已成功加入团队，请登录后查看团队信息。',\n            teamName: response.teamName,\n            isNewUser: response.isNewUser,\n            autoLogin: false,\n          });\n        }\n      } else {\n        setResult({\n          type: 'error',\n          title: '加入失败',\n          message: response.errorMessage || '处理邀请时发生错误',\n        });\n      }\n    } catch (error) {\n      console.error('处理邀请失败:', error);\n      setResult({\n        type: 'error',\n        title: '加入失败',\n        message: '网络错误，请稍后重试',\n      });\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  // 处理取消操作\n  const handleCancel = () => {\n    history.push('/');\n  };\n\n  // 邀请确认界面\n  const InvitationConfirm = () => {\n    if (!invitationInfo) return null;\n\n    return (\n      <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n        <TeamOutlined style={{ fontSize: 64, color: '#1890ff', marginBottom: 24 }} />\n        <Title level={3}>团队邀请</Title>\n\n        <div style={{ marginBottom: 32 }}>\n          <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n            <div>\n              <Text type=\"secondary\">您被邀请加入团队：</Text>\n              <br />\n              <Title level={4} style={{ margin: '8px 0', color: '#1890ff' }}>\n                {invitationInfo.teamName}\n              </Title>\n            </div>\n\n            {invitationInfo.inviterName && (\n              <div>\n                <Text type=\"secondary\">邀请人：</Text>\n                <Text strong>{invitationInfo.inviterName}</Text>\n              </div>\n            )}\n\n            {invitationInfo.message && (\n              <div>\n                <Text type=\"secondary\">邀请消息：</Text>\n                <br />\n                <Text italic>\"{invitationInfo.message}\"</Text>\n              </div>\n            )}\n          </Space>\n        </div>\n\n        <Divider />\n\n        <div style={{ marginTop: 24 }}>\n          <Title level={4} style={{ marginBottom: 24 }}>\n            您确定要加入此团队吗？\n          </Title>\n\n          <Space size=\"large\">\n            <Button\n              type=\"primary\"\n              size=\"large\"\n              loading={processing}\n              onClick={handleAcceptInvitation}\n              icon={<CheckCircleOutlined />}\n              disabled={invitationInfo.isExpired}\n            >\n              {processing ? '正在处理...' : '确认加入'}\n            </Button>\n            <Button\n              size=\"large\"\n              onClick={handleCancel}\n              icon={<CloseCircleOutlined />}\n              disabled={processing}\n            >\n              取消\n            </Button>\n          </Space>\n        </div>\n\n        {invitationInfo.isExpired && (\n          <Alert\n            message=\"邀请已过期\"\n            description=\"此邀请链接已过期，请联系团队管理员重新发送邀请。\"\n            type=\"warning\"\n            showIcon\n            style={{ marginTop: 24 }}\n          />\n        )}\n      </div>\n    );\n  };\n\n  // 结果展示\n  const ResultDisplay = () => {\n    if (!result) return null;\n\n    // 根据结果类型和自动登录状态显示不同的按钮\n    const getExtraButtons = () => {\n      if (result.type === 'success') {\n        if (result.autoLogin) {\n          // 自动登录成功，显示跳转到个人中心的按钮\n          return [\n            <Button type=\"primary\" key=\"personal-center\" onClick={() => history.push('/personal-center')}>\n              前往个人中心\n            </Button>,\n          ];\n        } else if (result.isNewUser) {\n          // 新用户但未自动登录，引导去登录\n          return [\n            <Button type=\"primary\" key=\"login\" onClick={() => history.push('/user/login')}>\n              前往登录\n            </Button>,\n            <Button key=\"home\" onClick={() => history.push('/')}>\n              返回首页\n            </Button>,\n          ];\n        } else {\n          // 现有用户，引导去登录\n          return [\n            <Button type=\"primary\" key=\"login\" onClick={() => history.push('/user/login')}>\n              前往登录\n            </Button>,\n            <Button key=\"home\" onClick={() => history.push('/')}>\n              返回首页\n            </Button>,\n          ];\n        }\n      } else {\n        // 错误情况，显示重试和返回首页\n        return [\n          <Button type=\"primary\" key=\"retry\" onClick={() => window.location.reload()}>\n            重试\n          </Button>,\n          <Button key=\"home\" onClick={() => history.push('/')}>\n            返回首页\n          </Button>,\n        ];\n      }\n    };\n\n    return (\n      <Result\n        status={result.type}\n        title={result.title}\n        subTitle={result.message}\n        extra={getExtraButtons()}\n      />\n    );\n  };\n\n  // 页面加载时获取邀请信息\n  useEffect(() => {\n    fetchInvitationInfo();\n  }, [token]);\n\n  // 加载中状态\n  if (loading) {\n    return (\n      <div className={styles.container}>\n        <Helmet>\n          <title>\n            团队邀请\n            {Settings.title && ` - ${Settings.title}`}\n          </title>\n        </Helmet>\n        <div className={styles.content}>\n          <Card className={styles.inviteCard}>\n            <div style={{ textAlign: 'center', padding: '60px 20px' }}>\n              <Spin size=\"large\" />\n              <div style={{ marginTop: 16 }}>\n                <Text type=\"secondary\">正在加载邀请信息...</Text>\n              </div>\n            </div>\n          </Card>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  // 显示结果页面\n  if (result) {\n    return (\n      <div className={styles.container}>\n        <Helmet>\n          <title>\n            团队邀请\n            {Settings.title && ` - ${Settings.title}`}\n          </title>\n        </Helmet>\n        <div className={styles.content}>\n          <Card className={styles.inviteCard}>\n            <ResultDisplay />\n          </Card>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  // 显示邀请确认页面\n  return (\n    <div className={styles.container}>\n      <Helmet>\n        <title>\n          团队邀请\n          {Settings.title && ` - ${Settings.title}`}\n        </title>\n      </Helmet>\n      <div className={styles.content}>\n        <div className={styles.header}>\n          <Space direction=\"vertical\" align=\"center\" size=\"large\">\n            <div className={styles.logo}>\n              <img src=\"/logo.svg\" alt=\"TeamAuth\" height={48} />\n            </div>\n            <div className={styles.title}>\n              <Title level={2}>团队邀请</Title>\n              <Text type=\"secondary\">加入团队，开始协作</Text>\n            </div>\n          </Space>\n        </div>\n\n        <Card className={styles.inviteCard}>\n          <InvitationConfirm />\n        </Card>\n\n        <div className={styles.footer}>\n          <Text type=\"secondary\">© 2025 TeamAuth. All rights reserved.</Text>\n        </div>\n      </div>\n      <Footer />\n    </div>\n  );\n};\n\nexport default InvitePage;\n"], "names": [], "mappings": "uQAiaA,+CAAA,6GA3Z2C,wMAYA,gBACpB,gBACW,gBAEL,oBACR,aAErB,GAAM,CAAE,MAAA,CAAK,CAAE,KAAA,CAAI,CAAE,UAAA,CAAS,CAAE,CAAG,SAAU,CAEvC,EAAY,GAAA,cAAY,EAAC,CAAC,CAAE,MAAA,CAAK,CAAE,GAChC,CAAA,CACL,UAAW,CACT,QAAS,OACT,cAAe,SACf,OAAQ,QACR,SAAU,OACV,gBACE,8FACF,eAAgB,WAClB,EACA,QAAS,CACP,KAAM,EACN,QAAS,OACT,cAAe,SACf,eAAgB,SAChB,WAAY,SACZ,QAAS,WACX,EACA,OAAQ,CACN,aAAc,GACd,UAAW,QACb,EACA,KAAM,CACJ,aAAc,EAChB,EACA,MAAO,CACL,aAAc,CAChB,EACA,WAAY,CACV,MAAO,OACP,SAAU,IACV,UAAW,EAAM,iBAAiB,AACpC,EACA,OAAQ,CACN,UAAW,GACX,UAAW,QACb,CACF,CAAA,OAgWF,EA7V6B,KAC3B,GAAM,CAAE,MAAA,CAAK,CAAE,CAAG,GAAA,WAAS,IACrB,CAAC,EAAS,EAAW,CAAG,GAAA,UAAQ,EAAC,CAAA,GACjC,CAAC,EAAY,EAAc,CAAG,GAAA,UAAQ,EAAC,CAAA,GACvC,CAAC,EAAgB,EAAkB,CAAG,GAAA,UAAQ,EAAgC,MAC9E,CAAC,EAAQ,EAAU,CAAG,GAAA,UAAQ,EAAM,MACpC,CAAE,OAAA,CAAM,CAAE,CAAG,IAGb,EAAsB,UAC1B,GAAI,CAAC,EAAO,CACV,EAAU,CACR,KAAM,QACN,MAAO,uCACP,QAAS,0EACX,GACA,EAAW,CAAA,GACX,OACF,CAEA,GAAI,CACF,IAAM,EAAW,MAAM,mBAAiB,CAAC,iBAAiB,CAAC,GAEvD,EAAS,OAAO,CAClB,EAAkB,GAElB,EAAU,CACR,KAAM,QACN,MAAO,uCACP,QAAS,EAAS,YAAY,EAAI,kDACpC,GAEJ,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,oDAAa,GAC3B,EAAU,CACR,KAAM,QACN,MAAO,mDACP,QAAS,8DACX,GACF,QAAU,CACR,EAAW,CAAA,GACb,CACF,EAGM,EAAyB,UAC7B,GAAK,GAEL,EAAc,CAAA,GACd,GAAI,CACF,IAAM,EAAW,MAAM,mBAAiB,CAAC,sBAAsB,CAAC,EAAO,CAAC,GAExE,GAAI,EAAS,OAAO,EAElB,GAAI,EAAS,SAAS,EAAI,EAAS,WAAW,CAC5C,GAAI,CACF,cAAY,CAAC,QAAQ,CAAC,EAAS,WAAW,EAC1C,QAAQ,GAAG,CAAC,gHAEZ,EAAU,CACR,KAAM,UACN,MAAO,6CACP,QAAS,CAAC,wJAAmB,EAAE,EAAS,UAAU,EAAI,4DAAe,CAAC,CACtE,SAAU,EAAS,QAAQ,CAC3B,UAAW,EAAS,SAAS,CAC7B,UAAW,CAAA,CACb,GAGA,WAAW,KACT,SAAO,CAAC,IAAI,CAAC,oBACf,EAAG,KACL,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wCAAW,GACzB,EAAU,CACR,KAAM,UACN,MAAO,6CACP,QAAS,+MACT,SAAU,EAAS,QAAQ,CAC3B,UAAW,EAAS,SAAS,CAC7B,UAAW,CAAA,CACb,GACF,MACS,EAAS,SAAS,CAE3B,EAAU,CACR,KAAM,UACN,MAAO,6CACP,QAAS,+MACT,SAAU,EAAS,QAAQ,CAC3B,UAAW,EAAS,SAAS,CAC7B,UAAW,CAAA,CACb,GAGA,EAAU,CACR,KAAM,UACN,MAAO,iCACP,QAAS,EAAS,UAAU,EAAI,2HAChC,SAAU,EAAS,QAAQ,CAC3B,UAAW,EAAS,SAAS,CAC7B,UAAW,CAAA,CACb,SAGF,EAAU,CACR,KAAM,QACN,MAAO,2BACP,QAAS,EAAS,YAAY,EAAI,wDACpC,GAEJ,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wCAAW,GACzB,EAAU,CACR,KAAM,QACN,MAAO,2BACP,QAAS,8DACX,GACF,QAAU,CACR,EAAc,CAAA,GAChB,EACF,EAGM,EAAe,KACnB,SAAO,CAAC,IAAI,CAAC,KACf,QAgJA,CALA,GAAA,WAAS,EAAC,KACR,IACF,EAAG,CAAC,EAAM,EAGN,GAEA,WAAC,OAAI,UAAW,EAAO,SAAS,WAC9B,UAAC,QAAM,WACL,WAAC,mBAAM,2BAEJ,SAAQ,CAAC,KAAK,EAAI,CAAC,GAAG,EAAE,SAAQ,CAAC,KAAK,CAAC,CAAC,MAG7C,UAAC,OAAI,UAAW,EAAO,OAAO,UAC5B,UAAC,SAAI,EAAC,UAAW,EAAO,UAAU,UAChC,WAAC,OAAI,MAAO,CAAE,UAAW,SAAU,QAAS,WAAY,YACtD,UAAC,SAAI,EAAC,KAAK,UACX,UAAC,OAAI,MAAO,CAAE,UAAW,EAAG,WAC1B,UAAC,GAAK,KAAK,qBAAY,iEAK/B,UAAC,QAAM,QAMT,EAEA,WAAC,OAAI,UAAW,EAAO,SAAS,WAC9B,UAAC,QAAM,WACL,WAAC,mBAAM,2BAEJ,SAAQ,CAAC,KAAK,EAAI,CAAC,GAAG,EAAE,SAAQ,CAAC,KAAK,CAAC,CAAC,MAG7C,UAAC,OAAI,UAAW,EAAO,OAAO,UAC5B,UAAC,SAAI,EAAC,UAAW,EAAO,UAAU,UAChC,UAnGY,IACpB,AAAK,EA+CH,UAAC,SAAM,EACL,OAAQ,EAAO,IAAI,CACnB,MAAO,EAAO,KAAK,CACnB,SAAU,EAAO,OAAO,CACxB,MA/CF,AAAI,AAAgB,YAAhB,EAAO,IAAI,CA+BN,CACL,UAAC,SAAM,EAAC,KAAK,UAAsB,QAAS,IAAM,OAAO,QAAQ,CAAC,MAAM,YAAI,gBAAjD,SAG3B,UAAC,SAAM,EAAY,QAAS,IAAM,SAAO,CAAC,IAAI,CAAC,cAAM,4BAAzC,QAGb,CArCD,AAAI,EAAO,SAAS,CAEX,CACL,UAAC,SAAM,EAAC,KAAK,UAAgC,QAAS,IAAM,SAAO,CAAC,IAAI,CAAC,6BAAqB,wCAAnE,mBAG5B,EACQ,EAAO,SAAS,CAElB,CACL,UAAC,SAAM,EAAC,KAAK,UAAsB,QAAS,IAAM,SAAO,CAAC,IAAI,CAAC,wBAAgB,4BAApD,SAG3B,UAAC,SAAM,EAAY,QAAS,IAAM,SAAO,CAAC,IAAI,CAAC,cAAM,4BAAzC,QAGb,IArBa,aAqGhB,UAAC,QAAM,QAOX,WAAC,OAAI,UAAW,EAAO,SAAS,WAC9B,UAAC,QAAM,WACL,WAAC,mBAAM,2BAEJ,SAAQ,CAAC,KAAK,EAAI,CAAC,GAAG,EAAE,SAAQ,CAAC,KAAK,CAAC,CAAC,MAG7C,WAAC,OAAI,UAAW,EAAO,OAAO,WAC5B,UAAC,OAAI,UAAW,EAAO,MAAM,UAC3B,WAAC,SAAK,EAAC,UAAU,WAAW,MAAM,SAAS,KAAK,kBAC9C,UAAC,OAAI,UAAW,EAAO,IAAI,UACzB,UAAC,OAAI,IAAI,YAAY,IAAI,WAAW,OAAQ,OAE9C,WAAC,OAAI,UAAW,EAAO,KAAK,WAC1B,UAAC,GAAM,MAAO,WAAG,6BACjB,UAAC,GAAK,KAAK,qBAAY,mEAK7B,UAAC,SAAI,EAAC,UAAW,EAAO,UAAU,UAChC,UAhNkB,IACxB,AAAK,EAGH,WAAC,OAAI,MAAO,CAAE,UAAW,SAAU,QAAS,WAAY,YACtD,UAAC,SAAY,EAAC,MAAO,CAAE,SAAU,GAAI,MAAO,UAAW,aAAc,EAAG,IACxE,UAAC,GAAM,MAAO,WAAG,6BAEjB,UAAC,OAAI,MAAO,CAAE,aAAc,EAAG,WAC7B,WAAC,SAAK,EAAC,UAAU,WAAW,KAAK,SAAS,MAAO,CAAE,MAAO,MAAO,YAC/D,WAAC,iBACC,UAAC,GAAK,KAAK,qBAAY,2DACvB,UAAC,SACD,UAAC,GAAM,MAAO,EAAG,MAAO,CAAE,OAAQ,QAAS,MAAO,SAAU,WACzD,EAAe,QAAQ,MAI3B,EAAe,WAAW,EACzB,WAAC,iBACC,UAAC,GAAK,KAAK,qBAAY,6BACvB,UAAC,GAAK,MAAM,aAAE,EAAe,WAAW,MAI3C,EAAe,OAAO,EACrB,WAAC,iBACC,UAAC,GAAK,KAAK,qBAAY,mCACvB,UAAC,SACD,WAAC,GAAK,MAAM,cAAC,IAAE,EAAe,OAAO,CAAC,eAM9C,UAAC,SAAO,KAER,WAAC,OAAI,MAAO,CAAE,UAAW,EAAG,YAC1B,UAAC,GAAM,MAAO,EAAG,MAAO,CAAE,aAAc,EAAG,WAAG,uEAI9C,WAAC,SAAK,EAAC,KAAK,kBACV,UAAC,SAAM,EACL,KAAK,UACL,KAAK,QACL,QAAS,EACT,QAAS,EACT,KAAM,UAAC,SAAmB,KAC1B,SAAU,EAAe,SAAS,UAEjC,EAAa,8BAAY,6BAE5B,UAAC,SAAM,EACL,KAAK,QACL,QAAS,EACT,KAAM,UAAC,SAAmB,KAC1B,SAAU,WACX,uBAMJ,EAAe,SAAS,EACvB,UAAC,SAAK,EACJ,QAAQ,iCACR,YAAY,mJACZ,KAAK,UACL,QAAQ,IACR,MAAO,CAAE,UAAW,EAAG,OArEH,WAkNxB,UAAC,OAAI,UAAW,EAAO,MAAM,UAC3B,UAAC,GAAK,KAAK,qBAAY,kDAG3B,UAAC,QAAM,QAGb"}