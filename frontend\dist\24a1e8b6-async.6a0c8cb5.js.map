{"version": 3, "sources": ["node_modules/@ant-design/icons-svg/es/asn/CarOutlined.js", "node_modules/@ant-design/icons/es/icons/CarOutlined.js", "node_modules/@ant-design/icons-svg/es/asn/MinusCircleOutlined.js", "node_modules/@ant-design/icons/es/icons/MinusCircleOutlined.js", "node_modules/antd/es/flex/utils.js", "node_modules/antd/es/flex/style/index.js", "node_modules/antd/es/flex/index.js", "src/pages/personal-center/TeamListCard.tsx", "src/services/todo.ts", "src/pages/personal-center/TodoManagement.tsx", "node_modules/@ant-design/icons-svg/es/asn/BarChartOutlined.js", "node_modules/@ant-design/icons/es/icons/BarChartOutlined.js", "src/pages/personal-center/UserProfileCard.tsx", "src/pages/personal-center/index.tsx"], "sourcesContent": ["// This icon file is generated automatically.\nvar CarOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M380 704h264c4.4 0 8-3.6 8-8v-84c0-4.4-3.6-8-8-8h-40c-4.4 0-8 3.6-8 8v36H428v-36c0-4.4-3.6-8-8-8h-40c-4.4 0-8 3.6-8 8v84c0 4.4 3.6 8 8 8zm340-123a40 40 0 1080 0 40 40 0 10-80 0zm239-167.6L935.3 372a8 8 0 00-10.9-2.9l-50.7 29.6-78.3-216.2a63.9 63.9 0 00-60.9-44.4H301.2c-34.7 0-65.5 22.4-76.2 55.5l-74.6 205.2-50.8-29.6a8 8 0 00-10.9 2.9L65 413.4c-2.2 3.8-.9 8.6 2.9 10.8l60.4 35.2-14.5 40c-1.2 3.2-1.8 6.6-1.8 10v348.2c0 15.7 11.8 28.4 26.3 28.4h67.6c12.3 0 23-9.3 25.6-22.3l7.7-37.7h545.6l7.7 37.7c2.7 13 13.3 22.3 25.6 22.3h67.6c14.5 0 26.3-12.7 26.3-28.4V509.4c0-3.4-.6-6.8-1.8-10l-14.5-40 60.3-35.2a8 8 0 003-10.8zM840 517v237H184V517l15.6-43h624.8l15.6 43zM292.7 218.1l.5-1.3.4-1.3c1.1-3.3 4.1-5.5 7.6-5.5h427.6l75.4 208H220l72.7-199.9zM224 581a40 40 0 1080 0 40 40 0 10-80 0z\" } }] }, \"name\": \"car\", \"theme\": \"outlined\" };\nexport default CarOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CarOutlinedSvg from \"@ant-design/icons-svg/es/asn/CarOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CarOutlined = function CarOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CarOutlinedSvg\n  }));\n};\n\n/**![car](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM4MCA3MDRoMjY0YzQuNCAwIDgtMy42IDgtOHYtODRjMC00LjQtMy42LTgtOC04aC00MGMtNC40IDAtOCAzLjYtOCA4djM2SDQyOHYtMzZjMC00LjQtMy42LTgtOC04aC00MGMtNC40IDAtOCAzLjYtOCA4djg0YzAgNC40IDMuNiA4IDggOHptMzQwLTEyM2E0MCA0MCAwIDEwODAgMCA0MCA0MCAwIDEwLTgwIDB6bTIzOS0xNjcuNkw5MzUuMyAzNzJhOCA4IDAgMDAtMTAuOS0yLjlsLTUwLjcgMjkuNi03OC4zLTIxNi4yYTYzLjkgNjMuOSAwIDAwLTYwLjktNDQuNEgzMDEuMmMtMzQuNyAwLTY1LjUgMjIuNC03Ni4yIDU1LjVsLTc0LjYgMjA1LjItNTAuOC0yOS42YTggOCAwIDAwLTEwLjkgMi45TDY1IDQxMy40Yy0yLjIgMy44LS45IDguNiAyLjkgMTAuOGw2MC40IDM1LjItMTQuNSA0MGMtMS4yIDMuMi0xLjggNi42LTEuOCAxMHYzNDguMmMwIDE1LjcgMTEuOCAyOC40IDI2LjMgMjguNGg2Ny42YzEyLjMgMCAyMy05LjMgMjUuNi0yMi4zbDcuNy0zNy43aDU0NS42bDcuNyAzNy43YzIuNyAxMyAxMy4zIDIyLjMgMjUuNiAyMi4zaDY3LjZjMTQuNSAwIDI2LjMtMTIuNyAyNi4zLTI4LjRWNTA5LjRjMC0zLjQtLjYtNi44LTEuOC0xMGwtMTQuNS00MCA2MC4zLTM1LjJhOCA4IDAgMDAzLTEwLjh6TTg0MCA1MTd2MjM3SDE4NFY1MTdsMTUuNi00M2g2MjQuOGwxNS42IDQzek0yOTIuNyAyMTguMWwuNS0xLjMuNC0xLjNjMS4xLTMuMyA0LjEtNS41IDcuNi01LjVoNDI3LjZsNzUuNCAyMDhIMjIwbDcyLjctMTk5Ljl6TTIyNCA1ODFhNDAgNDAgMCAxMDgwIDAgNDAgNDAgMCAxMC04MCAweiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CarOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CarOutlined';\n}\nexport default RefIcon;", "// This icon file is generated automatically.\nvar MinusCircleOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M696 480H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z\" } }] }, \"name\": \"minus-circle\", \"theme\": \"outlined\" };\nexport default MinusCircleOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport MinusCircleOutlinedSvg from \"@ant-design/icons-svg/es/asn/MinusCircleOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar MinusCircleOutlined = function MinusCircleOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: MinusCircleOutlinedSvg\n  }));\n};\n\n/**![minus-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY5NiA0ODBIMzI4Yy00LjQgMC04IDMuNi04IDh2NDhjMCA0LjQgMy42IDggOCA4aDM2OGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOHoiIC8+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(MinusCircleOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'MinusCircleOutlined';\n}\nexport default RefIcon;", "import classNames from 'classnames';\nexport const flexWrapValues = ['wrap', 'nowrap', 'wrap-reverse'];\nexport const justifyContentValues = ['flex-start', 'flex-end', 'start', 'end', 'center', 'space-between', 'space-around', 'space-evenly', 'stretch', 'normal', 'left', 'right'];\nexport const alignItemsValues = ['center', 'start', 'end', 'flex-start', 'flex-end', 'self-start', 'self-end', 'baseline', 'normal', 'stretch'];\nconst genClsWrap = (prefixCls, props) => {\n  const wrap = props.wrap === true ? 'wrap' : props.wrap;\n  return {\n    [`${prefixCls}-wrap-${wrap}`]: wrap && flexWrapValues.includes(wrap)\n  };\n};\nconst genClsAlign = (prefixCls, props) => {\n  const alignCls = {};\n  alignItemsValues.forEach(cssKey => {\n    alignCls[`${prefixCls}-align-${cssKey}`] = props.align === cssKey;\n  });\n  alignCls[`${prefixCls}-align-stretch`] = !props.align && !!props.vertical;\n  return alignCls;\n};\nconst genClsJustify = (prefixCls, props) => {\n  const justifyCls = {};\n  justifyContentValues.forEach(cssKey => {\n    justifyCls[`${prefixCls}-justify-${cssKey}`] = props.justify === cssKey;\n  });\n  return justifyCls;\n};\nfunction createFlexClassNames(prefixCls, props) {\n  return classNames(Object.assign(Object.assign(Object.assign({}, genClsWrap(prefixCls, props)), genClsAlign(prefixCls, props)), genClsJustify(prefixCls, props)));\n}\nexport default createFlexClassNames;", "import { genStyleHooks, mergeToken } from '../../theme/internal';\nimport { alignItemsValues, flexWrapValues, justifyContentValues } from '../utils';\nconst genFlexStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      display: 'flex',\n      margin: 0,\n      padding: 0,\n      '&-vertical': {\n        flexDirection: 'column'\n      },\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      '&:empty': {\n        display: 'none'\n      }\n    }\n  };\n};\nconst genFlexGapStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      '&-gap-small': {\n        gap: token.flexGapSM\n      },\n      '&-gap-middle': {\n        gap: token.flexGap\n      },\n      '&-gap-large': {\n        gap: token.flexGapLG\n      }\n    }\n  };\n};\nconst genFlexWrapStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const wrapStyle = {};\n  flexWrapValues.forEach(value => {\n    wrapStyle[`${componentCls}-wrap-${value}`] = {\n      flexWrap: value\n    };\n  });\n  return wrapStyle;\n};\nconst genAlignItemsStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const alignStyle = {};\n  alignItemsValues.forEach(value => {\n    alignStyle[`${componentCls}-align-${value}`] = {\n      alignItems: value\n    };\n  });\n  return alignStyle;\n};\nconst genJustifyContentStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const justifyStyle = {};\n  justifyContentValues.forEach(value => {\n    justifyStyle[`${componentCls}-justify-${value}`] = {\n      justifyContent: value\n    };\n  });\n  return justifyStyle;\n};\nexport const prepareComponentToken = () => ({});\nexport default genStyleHooks('Flex', token => {\n  const {\n    paddingXS,\n    padding,\n    paddingLG\n  } = token;\n  const flexToken = mergeToken(token, {\n    flexGapSM: paddingXS,\n    flexGap: padding,\n    flexGapLG: paddingLG\n  });\n  return [genFlexStyle(flexToken), genFlexGapStyle(flexToken), genFlexWrapStyle(flexToken), genAlignItemsStyle(flexToken), genJustifyContentStyle(flexToken)];\n}, prepareComponentToken, {\n  // Flex component don't apply extra font style\n  // https://github.com/ant-design/ant-design/issues/46403\n  resetStyle: false\n});", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { isPresetSize } from '../_util/gapSize';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nimport createFlexClassNames from './utils';\nconst Flex = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      rootClassName,\n      className,\n      style,\n      flex,\n      gap,\n      vertical = false,\n      component: Component = 'div'\n    } = props,\n    othersProps = __rest(props, [\"prefixCls\", \"rootClassName\", \"className\", \"style\", \"flex\", \"gap\", \"vertical\", \"component\"]);\n  const {\n    flex: ctxFlex,\n    direction: ctxDirection,\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('flex', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const mergedVertical = vertical !== null && vertical !== void 0 ? vertical : ctxFlex === null || ctxFlex === void 0 ? void 0 : ctxFlex.vertical;\n  const mergedCls = classNames(className, rootClassName, ctxFlex === null || ctxFlex === void 0 ? void 0 : ctxFlex.className, prefixCls, hashId, cssVarCls, createFlexClassNames(prefixCls, props), {\n    [`${prefixCls}-rtl`]: ctxDirection === 'rtl',\n    [`${prefixCls}-gap-${gap}`]: isPresetSize(gap),\n    [`${prefixCls}-vertical`]: mergedVertical\n  });\n  const mergedStyle = Object.assign(Object.assign({}, ctxFlex === null || ctxFlex === void 0 ? void 0 : ctxFlex.style), style);\n  if (flex) {\n    mergedStyle.flex = flex;\n  }\n  if (gap && !isPresetSize(gap)) {\n    mergedStyle.gap = gap;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(Component, Object.assign({\n    ref: ref,\n    className: mergedCls,\n    style: mergedStyle\n  }, omit(othersProps, ['justify', 'wrap', 'align']))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Flex.displayName = 'Flex';\n}\nexport default Flex;", "import {\n  CarOutlined,\n  CheckCircleOutlined,\n  ClockCircleOutlined,\n  CrownOutlined,\n  ExclamationCircleOutlined,\n  MinusCircleOutlined,\n  PlusOutlined,\n  RightOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { history, useModel } from '@umijs/max';\nimport {\n  Al<PERSON>,\n  Button,\n  Card,\n  Col,\n  Flex,\n  Form,\n  Input,\n  List,\n  message,\n  Modal,\n  Row,\n  Spin,\n  Tooltip,\n  Typography,\n} from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { AuthService } from '@/services';\nimport { TeamService } from '@/services/team';\nimport type { TeamDetailResponse, CreateTeamRequest } from '@/types/api';\nimport {\n  getTeamIdFromCurrentToken,\n  hasTeamInCurrentToken,\n  getUserIdFromCurrentToken,\n} from '@/utils/tokenUtils';\nimport { recordTeamSelection, hasUserSelectedTeam } from '@/utils/teamSelectionUtils';\n\nconst { Text, Title } = Typography;\nconst { TextArea } = Input;\n\n\n\n// 响应式布局样式\nconst styles = `\n  .team-item .ant-card-body {\n    padding: 0 !important;\n  }\n\n  .team-item:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;\n  }\n\n  @media (max-width: 768px) {\n    .team-item {\n      margin-bottom: 8px;\n    }\n\n    .team-stats-row {\n      margin-top: 8px;\n    }\n\n    .team-info-wrap {\n      gap: 8px !important;\n    }\n  }\n\n  @media (max-width: 576px) {\n    .team-stats-row {\n      margin-top: 12px;\n    }\n\n    .team-stats-col {\n      margin-bottom: 4px;\n    }\n\n    .team-info-wrap {\n      gap: 6px !important;\n    }\n\n    .team-meta-info {\n      flex-wrap: wrap;\n      gap: 6px !important;\n    }\n\n    .team-status-badges {\n      flex-wrap: wrap;\n      gap: 4px !important;\n      margin-top: 4px;\n    }\n  }\n\n  @media (max-width: 480px) {\n    .team-name-text {\n      font-size: 14px !important;\n    }\n\n    .team-meta-text {\n      font-size: 11px !important;\n    }\n\n    .team-meta-info {\n      gap: 4px !important;\n    }\n\n    .team-status-badges {\n      gap: 3px !important;\n    }\n  }\n`;\n\nconst TeamListCard: React.FC = () => {\n  // 团队列表状态管理\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [switchingTeamId, setSwitchingTeamId] = useState<number | null>(null);\n\n  // 创建团队模态框状态\n  const [createModalVisible, setCreateModalVisible] = useState(false);\n  const [createLoading, setCreateLoading] = useState(false);\n  const [form] = Form.useForm();\n\n  const { initialState, setInitialState } = useModel('@@initialState');\n  const currentTeam = initialState?.currentTeam;\n\n  // 获取当前Token中的团队信息和用户信息\n  const currentTokenTeamId = getTeamIdFromCurrentToken();\n  const currentUserId = getUserIdFromCurrentToken();\n  const hasTeamInToken = hasTeamInCurrentToken();\n\n  // 判断是否有真正的当前团队：\n  // 1. Token中有团队信息（说明用户已经选择过团队）\n  // 2. initialState中有团队信息（说明已经获取过团队详情）\n  // 3. 两者的团队ID一致（确保状态同步）\n  // 4. 用户曾经主动选择过这个团队（区分初始登录和主动选择）\n  const hasRealCurrentTeam = !!(\n    hasTeamInToken &&\n    currentTokenTeamId &&\n    currentTeam &&\n    currentTeam.id === currentTokenTeamId &&\n    currentUserId &&\n    hasUserSelectedTeam(currentUserId, currentTokenTeamId)\n  );\n\n  // 获取实际的当前团队ID：只有在用户真正选择过团队且状态同步时才返回团队ID\n  const actualCurrentTeamId = hasRealCurrentTeam ? currentTokenTeamId : null;\n\n  // 调试日志\n  console.log('TeamListCard 状态调试:', {\n    currentTeam: currentTeam?.id,\n    currentTokenTeamId,\n    currentUserId,\n    hasTeamInToken,\n    hasRealCurrentTeam,\n    actualCurrentTeamId,\n    hasUserSelectedCurrentTeam: currentUserId && currentTokenTeamId ? hasUserSelectedTeam(currentUserId, currentTokenTeamId) : false,\n    initialStateCurrentUser: !!initialState?.currentUser,\n  });\n\n  // 获取团队列表数据\n  useEffect(() => {\n    const fetchTeams = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n        const teamsData = await TeamService.getUserTeamsWithStats();\n        setTeams(teamsData);\n      } catch (error) {\n        console.error('获取团队列表失败:', error);\n        setError('获取团队列表失败');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // 只有在用户已登录时才获取团队列表\n    if (initialState?.currentUser) {\n      fetchTeams();\n    }\n  }, [initialState?.currentUser]);\n\n  // 监听全局状态变化，处理注销等情况\n  useEffect(() => {\n    // 如果用户已注销（currentUser为undefined），清除本地团队列表状态\n    if (!initialState?.currentUser) {\n      setTeams([]);\n      setError(null);\n      setLoading(false);\n      setSwitchingTeamId(null);\n    }\n  }, [initialState?.currentUser]);\n\n  // 监听当前团队状态变化\n  useEffect(() => {\n    console.log('当前团队状态变化:', {\n      currentTeam: currentTeam?.id,\n      actualCurrentTeamId,\n      hasRealCurrentTeam,\n    });\n  }, [currentTeam?.id, actualCurrentTeamId, hasRealCurrentTeam]);\n\n  // 创建团队处理函数\n  const handleCreateTeam = async (values: CreateTeamRequest) => {\n    setCreateLoading(true);\n    try {\n      const team = await TeamService.createTeam(values);\n      message.success('团队创建成功！');\n\n      // 重新获取团队列表\n      const teamsData = await TeamService.getUserTeamsWithStats();\n      setTeams(teamsData);\n\n      // 关闭模态框并重置表单\n      setCreateModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('创建团队失败:', error);\n      message.error('创建团队失败，请重试');\n    } finally {\n      setCreateLoading(false);\n    }\n  };\n\n  // 团队切换处理函数\n  const handleTeamSwitch = async (teamId: number, teamName: string) => {\n    // 检查用户是否已登录\n    if (!initialState?.currentUser) {\n      message.error('请先登录');\n      return;\n    }\n\n    try {\n      setSwitchingTeamId(teamId);\n\n      // 如果是当前团队，直接跳转到仪表盘，不需要调用切换API\n      if (teamId === actualCurrentTeamId) {\n        message.success(`进入团队：${teamName}`);\n        history.push('/dashboard');\n        return;\n      }\n\n      // 非当前团队，执行切换逻辑\n      const response = await AuthService.selectTeam({ teamId });\n\n      // 检查后端返回的团队选择成功标识\n      if (\n        response.teamSelectionSuccess &&\n        response.team &&\n        response.team.id === teamId\n      ) {\n        message.success(`已切换到团队：${teamName}`);\n\n        // 记录用户选择了这个团队\n        if (currentUserId) {\n          recordTeamSelection(currentUserId, teamId);\n        }\n\n        // 由于Token已经更新，路由守卫现在能够正确识别团队信息，可以直接跳转\n        // 同时异步更新 initialState 以保持状态同步\n        if (\n          initialState?.fetchTeamInfo &&\n          initialState?.fetchUserInfo &&\n          setInitialState\n        ) {\n          // 异步更新状态，不阻塞跳转\n          Promise.all([\n            initialState.fetchUserInfo(),\n            initialState.fetchTeamInfo(),\n          ])\n            .then(([currentUser, currentTeam]) => {\n              if (currentTeam && currentTeam.id === teamId) {\n                setInitialState({\n                  ...initialState,\n                  currentUser,\n                  currentTeam,\n                });\n              }\n            })\n            .catch((error) => {\n              console.error('更新 initialState 失败:', error);\n            });\n        }\n\n        // 直接跳转，路由守卫会处理团队验证\n        history.push('/dashboard');\n      } else {\n        console.error('团队切换响应异常，未返回正确的团队信息');\n        message.error('团队切换失败，请重试');\n      }\n    } catch (error: any) {\n      console.error('团队切换失败:', error);\n\n      // 响应拦截器已经处理了错误消息显示，这里只需要记录日志\n      // 如果是网络错误或其他非业务错误，才显示通用错误消息\n      if (!error.message || error.message === 'Failed to fetch') {\n        message.error('团队切换失败，请检查网络连接');\n      }\n    } finally {\n      setSwitchingTeamId(null);\n    }\n  };\n\n  return (\n    <>\n      {/* 注入样式 */}\n      <style dangerouslySetInnerHTML={{ __html: styles }} />\n\n      <Card\n        className=\"dashboard-card\"\n        style={{\n          borderRadius: 16,\n          boxShadow: '0 6px 20px rgba(0,0,0,0.08)',\n          border: 'none',\n          background: 'linear-gradient(145deg, #ffffff, #f8faff)',\n        }}\n        title={\n          <Flex justify=\"space-between\" align=\"center\">\n            <Title\n              level={4}\n              style={{\n                margin: 0,\n                background: 'linear-gradient(135deg, #1890ff, #722ed1)',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                fontWeight: 600,\n              }}\n            >\n              团队列表\n            </Title>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={() => setCreateModalVisible(true)}\n              style={{\n                borderRadius: 8,\n                background: 'linear-gradient(135deg, #1890ff, #722ed1)',\n                border: 'none',\n                boxShadow: '0 2px 8px rgba(24, 144, 255, 0.3)',\n              }}\n            >\n              创建团队\n            </Button>\n          </Flex>\n        }\n      >\n        {error ? (\n          <Alert\n            message=\"团队列表加载失败\"\n            description={error}\n            type=\"error\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n        ) : (\n          <Spin spinning={loading}>\n            {!initialState?.currentUser ? (\n              <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n                <Text type=\"secondary\">请先登录以查看团队列表</Text>\n              </div>\n            ) : teams.length === 0 && !loading ? (\n              <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n                <Text type=\"secondary\">暂无团队，请先加入或创建团队</Text>\n              </div>\n            ) : (\n              <List\n                dataSource={teams}\n                renderItem={(item) => (\n                  <List.Item>\n                    <Card\n                      className=\"team-item\"\n                      style={{\n                        background:\n                          actualCurrentTeamId === item.id\n                            ? 'linear-gradient(135deg, #f0f9ff, #e6f4ff)'\n                            : '#fff',\n                        borderRadius: 8,\n                        boxShadow:\n                          actualCurrentTeamId === item.id\n                            ? '0 2px 8px rgba(24, 144, 255, 0.12)'\n                            : '0 1px 4px rgba(0,0,0,0.06)',\n                        width: '100%',\n                        borderLeft: `3px solid ${item.isCreator ? '#722ed1' : '#52c41a'}`,\n                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                        border:\n                          actualCurrentTeamId === item.id\n                            ? '1px solid #91caff'\n                            : '1px solid #f0f0f0',\n                        padding: '12px 16px',\n                        position: 'relative',\n                        overflow: 'hidden',\n                      }}\n                      hoverable\n                      onMouseEnter={(e) => {\n                        if (actualCurrentTeamId !== item.id) {\n                          e.currentTarget.style.transform = 'translateY(-2px)';\n                          e.currentTarget.style.boxShadow =\n                            '0 8px 24px rgba(0,0,0,0.12)';\n                        }\n                      }}\n                      onMouseLeave={(e) => {\n                        if (actualCurrentTeamId !== item.id) {\n                          e.currentTarget.style.transform = 'translateY(0)';\n                          e.currentTarget.style.boxShadow =\n                            '0 2px 8px rgba(0,0,0,0.06)';\n                        }\n                      }}\n                    >\n                      {/* 响应式布局 */}\n                      <Row\n                        gutter={[8, 8]}\n                        align=\"middle\"\n                        style={{ width: '100%' }}\n                      >\n                        {/* 左侧：团队信息 */}\n                        <Col xs={24} sm={24} md={14} lg={12} xl={14}>\n                          <Flex vertical gap={6} className=\"team-info-wrap\">\n                            {/* 团队名称行 */}\n                            <Flex align=\"center\" gap={8} wrap=\"wrap\">\n                              <div\n                                style={{\n                                  cursor: 'pointer',\n                                  padding: '2px 4px',\n                                  borderRadius: 4,\n                                  transition: 'all 0.2s ease',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 6,\n                                }}\n                                onClick={() =>\n                                  handleTeamSwitch(item.id, item.name)\n                                }\n                                onMouseEnter={(e) => {\n                                  e.currentTarget.style.background =\n                                    'rgba(24, 144, 255, 0.05)';\n                                }}\n                                onMouseLeave={(e) => {\n                                  e.currentTarget.style.background =\n                                    'transparent';\n                                }}\n                              >\n                                <Text\n                                  strong\n                                  style={{\n                                    fontSize: 16,\n                                    color:\n                                      actualCurrentTeamId === item.id\n                                        ? '#1890ff'\n                                        : '#262626',\n                                    lineHeight: 1.2,\n                                  }}\n                                >\n                                  {item.name}\n                                </Text>\n                                <RightOutlined\n                                  style={{\n                                    fontSize: 10,\n                                    color:\n                                      actualCurrentTeamId === item.id\n                                        ? '#1890ff'\n                                        : '#8c8c8c',\n                                    verticalAlign: 'middle',\n                                    display: 'inline-flex',\n                                    alignItems: 'center',\n                                  }}\n                                />\n                              </div>\n\n                              {/* 状态标识 */}\n                              {actualCurrentTeamId === item.id && (\n                                <span\n                                  style={{\n                                    background: '#1890ff',\n                                    color: 'white',\n                                    padding: '1px 6px',\n                                    borderRadius: 8,\n                                    fontSize: 10,\n                                    fontWeight: 500,\n                                  }}\n                                >\n                                  当前\n                                </span>\n                              )}\n\n\n\n                              {switchingTeamId === item.id && (\n                                <Flex align=\"center\" gap={4}>\n                                  <Spin size=\"small\" />\n                                  <Text style={{ fontSize: 10, color: '#666' }}>\n                                    切换中\n                                  </Text>\n                                </Flex>\n                              )}\n                            </Flex>\n\n                            {/* 团队基本信息 */}\n                            <Flex align=\"center\" gap={12} wrap=\"wrap\" className=\"team-meta-info\">\n                              <Tooltip\n                                title={`团队创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`}\n                              >\n                                <Flex align=\"center\" gap={4}>\n                                  <ClockCircleOutlined\n                                    style={{ color: '#8c8c8c', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    style={{ fontSize: 12, color: '#8c8c8c' }}\n                                  >\n                                    创建: {new Date(\n                                      item.createdAt,\n                                    ).toLocaleDateString('zh-CN')}\n                                  </Text>\n                                </Flex>\n                              </Tooltip>\n\n                              {/* 加入日期 */}\n                              {item.assignedAt && (\n                                <Tooltip\n                                  title={`加入团队时间: ${new Date(item.assignedAt).toLocaleString('zh-CN')}`}\n                                >\n                                  <Flex align=\"center\" gap={4}>\n                                    <UserOutlined\n                                      style={{ color: '#8c8c8c', fontSize: 12 }}\n                                    />\n                                    <Text\n                                      style={{ fontSize: 12, color: '#8c8c8c' }}\n                                    >\n                                      加入: {new Date(\n                                        item.assignedAt,\n                                      ).toLocaleDateString('zh-CN')}\n                                    </Text>\n                                  </Flex>\n                                </Tooltip>\n                              )}\n\n                              <Tooltip\n                                title={`团队成员: ${item.memberCount}人`}\n                              >\n                                <Flex align=\"center\" gap={4}>\n                                  <TeamOutlined\n                                    style={{ color: '#8c8c8c', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    style={{ fontSize: 12, color: '#8c8c8c' }}\n                                  >\n                                    {item.memberCount} 人\n                                  </Text>\n                                </Flex>\n                              </Tooltip>\n                            </Flex>\n\n                            {/* 状态标识行 */}\n                            <Flex align=\"center\" gap={8} wrap=\"wrap\" className=\"team-status-badges\">\n                              {/* 角色标识 */}\n                              <span\n                                style={{\n                                  background: item.isCreator\n                                    ? '#722ed1'\n                                    : '#52c41a',\n                                  color: 'white',\n                                  padding: '2px 6px',\n                                  borderRadius: 8,\n                                  fontSize: 10,\n                                  fontWeight: 500,\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 2,\n                                }}\n                              >\n                                {item.isCreator ? (\n                                  <>\n                                    <CrownOutlined style={{ fontSize: 9 }} />\n                                    管理员\n                                  </>\n                                ) : (\n                                  <>\n                                    <UserOutlined style={{ fontSize: 9 }} />\n                                    成员\n                                  </>\n                                )}\n                              </span>\n\n                              {/* 用户状态标识 */}\n                              <span\n                                style={{\n                                  background: item.isActive ? '#52c41a' : '#ff4d4f',\n                                  color: 'white',\n                                  padding: '2px 6px',\n                                  borderRadius: 8,\n                                  fontSize: 10,\n                                  fontWeight: 500,\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 2,\n                                }}\n                              >\n                                {item.isActive ? (\n                                  <>\n                                    <CheckCircleOutlined style={{ fontSize: 9 }} />\n                                    启用\n                                  </>\n                                ) : (\n                                  <>\n                                    <MinusCircleOutlined style={{ fontSize: 9 }} />\n                                    停用\n                                  </>\n                                )}\n                              </span>\n                            </Flex>\n                          </Flex>\n                        </Col>\n\n                        {/* 右侧：响应式指标卡片 */}\n                        <Col xs={24} sm={24} md={10} lg={12} xl={10}>\n                          <Row\n                            gutter={[4, 4]}\n                            justify={{ xs: 'start', md: 'end' }}\n                          >\n                            {/* 车辆资源 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#f0f7ff',\n                                  border: '1px solid #d9e8ff',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <CarOutlined\n                                    style={{ color: '#1890ff', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#1890ff',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.vehicles || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    车辆\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 人员资源 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#f6ffed',\n                                  border: '1px solid #d1f0be',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <UserOutlined\n                                    style={{ color: '#52c41a', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#52c41a',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.personnel || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    人员\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 临期事项 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#fff7e6',\n                                  border: '1px solid #ffd666',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <ExclamationCircleOutlined\n                                    style={{ color: '#faad14', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#faad14',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.expiring || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    临期\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 逾期事项 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#fff1f0',\n                                  border: '1px solid #ffccc7',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <ExclamationCircleOutlined\n                                    style={{ color: '#ff4d4f', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#ff4d4f',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.overdue || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    逾期\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n                          </Row>\n                        </Col>\n                      </Row>\n                    </Card>\n                  </List.Item>\n                )}\n              />\n            )}\n          </Spin>\n        )}\n      </Card>\n\n      {/* 创建团队模态框 */}\n      <Modal\n        title={\n          <Flex align=\"center\" gap={8}>\n            <TeamOutlined style={{ color: '#1890ff' }} />\n            <span>创建新团队</span>\n          </Flex>\n        }\n        open={createModalVisible}\n        onCancel={() => {\n          setCreateModalVisible(false);\n          form.resetFields();\n        }}\n        footer={null}\n        width={500}\n        style={{ top: 100 }}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleCreateTeam}\n          autoComplete=\"off\"\n          style={{ marginTop: 24 }}\n        >\n          <Form.Item\n            label=\"团队名称\"\n            name=\"name\"\n            rules={[\n              { required: true, message: '请输入团队名称！' },\n              { max: 100, message: '团队名称长度不能超过100字符！' },\n              { min: 2, message: '团队名称至少需要2个字符！' },\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" size=\"large\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"团队描述\"\n            name=\"description\"\n            rules={[{ max: 500, message: '团队描述长度不能超过500字符！' }]}\n          >\n            <TextArea\n              placeholder=\"请输入团队描述（可选）\"\n              rows={4}\n              showCount\n              maxLength={500}\n            />\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0, marginTop: 32 }}>\n            <Flex justify=\"end\" gap={12}>\n              <Button\n                onClick={() => {\n                  setCreateModalVisible(false);\n                  form.resetFields();\n                }}\n              >\n                取消\n              </Button>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={createLoading}\n                style={{\n                  background: 'linear-gradient(135deg, #1890ff, #722ed1)',\n                  border: 'none',\n                }}\n              >\n                创建团队\n              </Button>\n            </Flex>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </>\n  );\n};\n\nexport default TeamListCard;\n", "/**\n * TODO服务\n */\n\nimport type {\n  CreateTodoRequest,\n  TodoResponse,\n  TodoStatsResponse,\n  UpdateTodoRequest,\n} from '@/types/api';\nimport { apiRequest } from '@/utils/request';\n\nexport class TodoService {\n  /**\n   * 获取用户的TODO列表\n   */\n  static async getUserTodos(): Promise<TodoResponse[]> {\n    const response = await apiRequest.get<TodoResponse[]>('/todos');\n    return response.data;\n  }\n\n  /**\n   * 创建TODO\n   */\n  static async createTodo(request: CreateTodoRequest): Promise<TodoResponse> {\n    const response = await apiRequest.post<TodoResponse>('/todos', request);\n    return response.data;\n  }\n\n  /**\n   * 更新TODO\n   */\n  static async updateTodo(\n    id: number,\n    request: UpdateTodoRequest,\n  ): Promise<TodoResponse> {\n    const response = await apiRequest.put<TodoResponse>(\n      `/todos/${id}`,\n      request,\n    );\n    return response.data;\n  }\n\n  /**\n   * 删除TODO\n   */\n  static async deleteTodo(id: number): Promise<void> {\n    await apiRequest.delete(`/todos/${id}`);\n  }\n\n  /**\n   * 获取TODO统计信息\n   */\n  static async getTodoStats(): Promise<TodoStatsResponse> {\n    const response = await apiRequest.get<TodoStatsResponse>('/todos/stats');\n    return response.data;\n  }\n}\n", "import {\n  CalendarOutlined,\n  CheckOutlined,\n  DeleteOutlined,\n  EditOutlined,\n  MoreOutlined,\n  PlusOutlined,\n  SearchOutlined,\n} from '@ant-design/icons';\nimport {\n  <PERSON><PERSON>,\n  Button,\n  Card,\n  Col,\n  Dropdown,\n  Flex,\n  Form,\n  Input,\n  List,\n  Modal,\n  message,\n  Progress,\n  Row,\n  Select,\n  Space,\n  Spin,\n  Tabs,\n  Tooltip,\n  Typography,\n} from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { TodoService } from '@/services/todo';\nimport type { TodoResponse, TodoStatsResponse } from '@/types/api';\n\nconst { Text } = Typography;\nconst { TabPane } = Tabs;\n\n// 使用API类型定义，不需要重复定义接口\ninterface TodoManagementProps {\n  onAddTodo?: (todo: TodoResponse) => void;\n  onUpdateTodo?: (id: number, updatedTodo: Partial<TodoResponse>) => void;\n  onDeleteTodo?: (id: number) => void;\n}\n\nconst TodoManagement: React.FC<TodoManagementProps> = (props) => {\n  // TODO数据状态管理\n  const [personalTasks, setPersonalTasks] = useState<TodoResponse[]>([]);\n  const [todoStats, setTodoStats] = useState<TodoStatsResponse>({\n    highPriorityCount: 0,\n    mediumPriorityCount: 0,\n    lowPriorityCount: 0,\n    totalCount: 0,\n    completedCount: 0,\n    completionPercentage: 0,\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // 待办事项状态管理\n  const [todoModalVisible, setTodoModalVisible] = useState(false);\n  const [todoForm] = Form.useForm();\n  const [editingTodoId, setEditingTodoId] = useState<number | null>(null);\n\n  // 过滤器状态\n  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'completed'>(\n    'pending',\n  );\n  const [searchText, setSearchText] = useState('');\n\n  // 获取TODO数据\n  useEffect(() => {\n    const fetchTodoData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        console.log('TodoManagement: 开始获取TODO数据');\n\n        // 分别获取TODO列表和统计数据，避免一个失败影响另一个\n        const todosPromise = TodoService.getUserTodos().catch((error) => {\n          console.error('获取TODO列表失败:', error);\n          return [];\n        });\n\n        const statsPromise = TodoService.getTodoStats().catch((error) => {\n          console.error('获取TODO统计失败:', error);\n          return {\n            highPriorityCount: 0,\n            mediumPriorityCount: 0,\n            lowPriorityCount: 0,\n            totalCount: 0,\n            completedCount: 0,\n            completionPercentage: 0,\n          };\n        });\n\n        const [todos, stats] = await Promise.all([todosPromise, statsPromise]);\n\n        console.log('TodoManagement: 获取到TODO列表:', todos);\n        console.log('TodoManagement: 获取到统计数据:', stats);\n\n        setPersonalTasks(todos);\n        setTodoStats(stats);\n      } catch (error) {\n        console.error('获取TODO数据时发生未知错误:', error);\n        setError('获取TODO数据失败，请刷新页面重试');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTodoData();\n  }, []);\n\n  // 根据激活的标签和搜索文本过滤任务\n  const filteredPersonalTasks = (personalTasks || []).filter((task) => {\n    // 根据标签过滤\n    if (activeTab === 'pending' && task.status === 1) return false;\n    if (activeTab === 'completed' && task.status === 0) return false;\n\n    // 根据搜索文本过滤\n    if (\n      searchText &&\n      !task.title.toLowerCase().includes(searchText.toLowerCase())\n    ) {\n      return false;\n    }\n\n    return true;\n  });\n\n  // 处理待办事项操作\n  const handleToggleTodoStatus = async (id: number) => {\n    try {\n      const task = personalTasks.find((t) => t.id === id);\n      if (!task) {\n        message.error('任务不存在');\n        return;\n      }\n\n      const newStatus = task.status === 0 ? 1 : 0;\n      console.log(`TodoManagement: 更新任务状态 ${id} -> ${newStatus}`);\n\n      await TodoService.updateTodo(id, { status: newStatus });\n\n      // 更新本地状态\n      setPersonalTasks(\n        personalTasks.map((task) =>\n          task.id === id ? { ...task, status: newStatus } : task,\n        ),\n      );\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        console.error('刷新统计数据失败:', statsError);\n        // 统计数据刷新失败不影响主要操作\n      }\n\n      message.success(newStatus === 1 ? '任务已完成' : '任务已标记为未完成');\n    } catch (error) {\n      console.error('更新任务状态失败:', error);\n      message.error('更新任务状态失败，请稍后重试');\n    }\n  };\n\n  const handleAddOrUpdateTodo = async (values: any) => {\n    try {\n      console.log('TodoManagement: 保存任务', { editingTodoId, values });\n\n      if (editingTodoId) {\n        // 更新现有待办事项\n        const updatedTodo = await TodoService.updateTodo(editingTodoId, {\n          title: values.name,\n          priority: values.priority,\n        });\n\n        setPersonalTasks(\n          personalTasks.map((task) =>\n            task.id === editingTodoId ? updatedTodo : task,\n          ),\n        );\n        message.success('任务更新成功');\n      } else {\n        // 添加新待办事项\n        const newTodo = await TodoService.createTodo({\n          title: values.name,\n          priority: values.priority,\n        });\n\n        setPersonalTasks([newTodo, ...personalTasks]);\n        message.success('任务创建成功');\n      }\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        console.error('刷新统计数据失败:', statsError);\n        // 统计数据刷新失败不影响主要操作\n      }\n\n      // 重置表单并关闭模态框\n      setTodoModalVisible(false);\n      setEditingTodoId(null);\n      todoForm.resetFields();\n    } catch (error) {\n      console.error('保存任务失败:', error);\n      const action = editingTodoId ? '更新' : '创建';\n      message.error(`${action}任务失败，请检查网络连接后重试`);\n    }\n  };\n\n  const handleDeleteTodo = async (id: number) => {\n    try {\n      console.log('TodoManagement: 删除任务', id);\n\n      await TodoService.deleteTodo(id);\n      setPersonalTasks(personalTasks.filter((task) => task.id !== id));\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        console.error('刷新统计数据失败:', statsError);\n        // 统计数据刷新失败不影响主要操作\n      }\n\n      message.success('任务删除成功');\n    } catch (error) {\n      console.error('删除任务失败:', error);\n      message.error('删除任务失败，请稍后重试');\n    }\n  };\n\n  return (\n    <Card\n      className=\"dashboard-card\"\n      style={{\n        borderRadius: 12,\n        boxShadow: '0 4px 12px rgba(0,0,0,0.05)',\n        border: 'none',\n        background: 'linear-gradient(145deg, #ffffff, #f5f8ff)',\n      }}\n      title={\n        <Flex justify=\"space-between\" align=\"center\">\n          <Text strong>待办事项</Text>\n        </Flex>\n      }\n    >\n      {/* 响应式标题行：搜索框、新增按钮、优先级计数和完成率 */}\n      <div\n        style={{\n          marginBottom: 16,\n          padding: '12px 16px',\n          background: '#fafbfc',\n          borderRadius: 8,\n          border: '1px solid #f0f0f0',\n        }}\n      >\n        {/* 使用 Row/Col 实现三列响应式布局 */}\n        <Row gutter={[16, 12]} align=\"middle\">\n          {/* 第一列：搜索框和新增按钮 */}\n          <Col xs={24} sm={24} md={8} lg={8} xl={8}>\n            <Flex align=\"center\" gap={12} style={{ width: '100%' }}>\n              <Input.Search\n                placeholder=\"搜索任务...\"\n                allowClear\n                prefix={<SearchOutlined />}\n                value={searchText}\n                onChange={(e) => setSearchText(e.target.value)}\n                style={{ flex: 1 }}\n                size=\"middle\"\n              />\n\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={() => {\n                  setEditingTodoId(null);\n                  todoForm.resetFields();\n                  setTodoModalVisible(true);\n                }}\n                style={{\n                  background: '#1890ff',\n                  borderColor: '#1890ff',\n                  boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',\n                  fontWeight: 500,\n                  minWidth: 80,\n                }}\n                size=\"middle\"\n              >\n                新增\n              </Button>\n            </Flex>\n          </Col>\n\n          {/* 第二列：各优先级数量 */}\n          <Col xs={24} sm={24} md={8} lg={8} xl={8}>\n            <Flex align=\"center\" justify=\"center\" wrap=\"wrap\">\n              <Space size={12} wrap>\n                <Tooltip\n                  title={`高优先级任务: ${todoStats.highPriorityCount}个`}\n                >\n                  <Flex align=\"center\" gap={4}>\n                    <div\n                      style={{\n                        width: 8,\n                        height: 8,\n                        borderRadius: '50%',\n                        background: '#ff4d4f',\n                      }}\n                    />\n                    <Text\n                      style={{\n                        fontSize: 12,\n                        fontWeight: 500,\n                        color: '#262626',\n                      }}\n                    >\n                      高: {todoStats.highPriorityCount}\n                    </Text>\n                  </Flex>\n                </Tooltip>\n\n                <Tooltip\n                  title={`中优先级任务: ${todoStats.mediumPriorityCount}个`}\n                >\n                  <Flex align=\"center\" gap={4}>\n                    <div\n                      style={{\n                        width: 8,\n                        height: 8,\n                        borderRadius: '50%',\n                        background: '#faad14',\n                      }}\n                    />\n                    <Text\n                      style={{\n                        fontSize: 12,\n                        fontWeight: 500,\n                        color: '#262626',\n                      }}\n                    >\n                      中: {todoStats.mediumPriorityCount}\n                    </Text>\n                  </Flex>\n                </Tooltip>\n\n                <Tooltip\n                  title={`低优先级任务: ${todoStats.lowPriorityCount}个`}\n                >\n                  <Flex align=\"center\" gap={4}>\n                    <div\n                      style={{\n                        width: 8,\n                        height: 8,\n                        borderRadius: '50%',\n                        background: '#52c41a',\n                      }}\n                    />\n                    <Text\n                      style={{\n                        fontSize: 12,\n                        fontWeight: 500,\n                        color: '#262626',\n                      }}\n                    >\n                      低: {todoStats.lowPriorityCount}\n                    </Text>\n                  </Flex>\n                </Tooltip>\n              </Space>\n            </Flex>\n          </Col>\n\n          {/* 第三列：完成率 */}\n          <Col xs={24} sm={24} md={8} lg={8} xl={8}>\n            <Flex align=\"center\" justify=\"center\">\n              <Tooltip\n                title={`完成率: ${todoStats.completionPercentage}% (${todoStats.completedCount}/${todoStats.totalCount})`}\n              >\n                <Flex align=\"center\" gap={6}>\n                  <Text\n                    style={{ fontSize: 12, fontWeight: 500, color: '#595959' }}\n                  >\n                    完成率:\n                  </Text>\n                  <Progress\n                    percent={todoStats.completionPercentage}\n                    size=\"small\"\n                    style={{ width: 80 }}\n                    strokeColor=\"#52c41a\"\n                    showInfo={false}\n                  />\n                  <Text\n                    style={{ fontSize: 12, fontWeight: 600, color: '#262626' }}\n                  >\n                    {todoStats.completionPercentage}%\n                  </Text>\n                </Flex>\n              </Tooltip>\n            </Flex>\n          </Col>\n        </Row>\n      </div>\n\n      {/* 第二行：标签页 */}\n      <Tabs\n        activeKey={activeTab}\n        onChange={(key) => setActiveTab(key as 'all' | 'pending' | 'completed')}\n        size=\"middle\"\n        style={{ marginBottom: 8 }}\n      >\n        <TabPane tab=\"全部\" key=\"all\" />\n        <TabPane tab=\"待处理\" key=\"pending\" />\n        <TabPane tab=\"已完成\" key=\"completed\" />\n      </Tabs>\n\n      {/* 待办事项列表 */}\n      {error ? (\n        <Alert\n          message=\"TODO数据加载失败\"\n          description={error}\n          type=\"error\"\n          showIcon\n          style={{ marginBottom: 16 }}\n        />\n      ) : (\n        <Spin spinning={loading}>\n          <List\n            dataSource={filteredPersonalTasks}\n            renderItem={(item) => {\n              return (\n                <List.Item\n                  className=\"todo-item\"\n                  style={{\n                    padding: '10px 16px',\n                    marginBottom: 12,\n                    borderRadius: 8,\n                    background: '#fff',\n                    opacity: item.status === 1 ? 0.7 : 1,\n                    borderLeft: `3px solid ${\n                      item.status === 1\n                        ? '#52c41a'\n                        : item.priority === 3\n                          ? '#ff4d4f'\n                          : item.priority === 2\n                            ? '#faad14'\n                            : '#8c8c8c'\n                    }`,\n                    boxShadow: '0 1px 4px rgba(0,0,0,0.05)',\n                  }}\n                >\n                  <Flex align=\"center\" gap={12} style={{ width: '100%' }}>\n                    {/* 左侧状态和优先级指示器 */}\n                    <Flex vertical align=\"center\">\n                      {item.status === 1 ? (\n                        <Flex\n                          align=\"center\"\n                          justify=\"center\"\n                          style={{\n                            width: 22,\n                            height: 22,\n                            borderRadius: '50%',\n                            background: '#52c41a',\n                          }}\n                        >\n                          <CheckOutlined\n                            style={{ color: '#fff', fontSize: 12 }}\n                          />\n                        </Flex>\n                      ) : (\n                        <div\n                          style={{\n                            width: 18,\n                            height: 18,\n                            borderRadius: '50%',\n                            border: `2px solid ${\n                              item.priority === 3\n                                ? '#ff4d4f'\n                                : item.priority === 2\n                                  ? '#faad14'\n                                  : '#8c8c8c'\n                            }`,\n                          }}\n                        />\n                      )}\n\n                      <div\n                        style={{\n                          width: 2,\n                          height: 24,\n                          background: '#f0f0f0',\n                          marginTop: 4,\n                        }}\n                      />\n                    </Flex>\n\n                    {/* 任务信息区 */}\n                    <Flex vertical style={{ flex: 1 }}>\n                      <Text\n                        style={{\n                          fontSize: 14,\n                          fontWeight: item.priority === 3 ? 500 : 'normal',\n                          textDecoration:\n                            item.status === 1 ? 'line-through' : 'none',\n                          color: item.status === 1 ? '#8c8c8c' : '#262626',\n                        }}\n                      >\n                        {item.title}\n                      </Text>\n\n                      {/* 显示创建日期 */}\n                      <Space align=\"center\" size={6} style={{ marginTop: 4 }}>\n                        <CalendarOutlined\n                          style={{\n                            fontSize: 12,\n                            color: '#8c8c8c',\n                          }}\n                        />\n                        <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                          创建于:{' '}\n                          {new Date(item.createdAt).toLocaleDateString('zh-CN')}\n                        </Text>\n                      </Space>\n                    </Flex>\n\n                    {/* 操作按钮区 */}\n                    <Dropdown\n                      trigger={['click']}\n                      menu={{\n                        items: [\n                          {\n                            key: 'complete',\n                            label:\n                              item.status === 1 ? '标记未完成' : '标记完成',\n                            icon: (\n                              <CheckOutlined\n                                style={{\n                                  color:\n                                    item.status === 1 ? '#8c8c8c' : '#52c41a',\n                                  fontSize: 14,\n                                }}\n                              />\n                            ),\n                          },\n                          {\n                            key: 'edit',\n                            label: '编辑任务',\n                            icon: <EditOutlined style={{ color: '#8c8c8c' }} />,\n                          },\n                          {\n                            key: 'delete',\n                            label: '删除任务',\n                            icon: (\n                              <DeleteOutlined style={{ color: '#ff4d4f' }} />\n                            ),\n                            danger: true,\n                          },\n                        ],\n                        onClick: ({ key }) => {\n                          if (key === 'complete') {\n                            handleToggleTodoStatus(item.id);\n                          } else if (key === 'edit') {\n                            setEditingTodoId(item.id);\n                            todoForm.setFieldsValue({\n                              name: item.title,\n                              priority: item.priority,\n                            });\n                            setTodoModalVisible(true);\n                          } else if (key === 'delete') {\n                            handleDeleteTodo(item.id);\n                          }\n                        },\n                      }}\n                    >\n                      <Button\n                        type=\"text\"\n                        size=\"small\"\n                        icon={<MoreOutlined />}\n                        style={{ width: 32, height: 32 }}\n                      />\n                    </Dropdown>\n                  </Flex>\n                </List.Item>\n              );\n            }}\n          />\n\n          {/* 待办事项表单模态框 */}\n          <Modal\n            title={editingTodoId ? '编辑待办事项' : '新增待办事项'}\n            open={todoModalVisible}\n            onCancel={() => {\n              setTodoModalVisible(false);\n              todoForm.resetFields();\n            }}\n            onOk={() => {\n              todoForm.submit();\n            }}\n            centered\n            destroyOnClose\n            footer={[\n              <Button key=\"cancel\" onClick={() => setTodoModalVisible(false)}>\n                取消\n              </Button>,\n              <Button\n                key=\"submit\"\n                type=\"primary\"\n                onClick={() => {\n                  todoForm.submit();\n                }}\n                style={{\n                  background: '#1890ff',\n                  borderColor: '#1890ff',\n                  boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',\n                }}\n              >\n                {editingTodoId ? '更新任务' : '创建任务'}\n              </Button>,\n            ]}\n          >\n            <Form\n              form={todoForm}\n              layout=\"vertical\"\n              onFinish={handleAddOrUpdateTodo}\n              autoComplete=\"off\"\n            >\n              <Form.Item\n                name=\"name\"\n                label=\"任务名称\"\n                rules={[{ required: true, message: '请输入任务名称' }]}\n              >\n                <Input\n                  placeholder=\"请输入任务名称\"\n                  size=\"large\"\n                  style={{ borderRadius: 6 }}\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"priority\"\n                label=\"优先级\"\n                initialValue={2}\n                rules={[{ required: true, message: '请选择优先级' }]}\n              >\n                <Select\n                  size=\"large\"\n                  options={[\n                    { value: 3, label: '高优先级' },\n                    { value: 2, label: '中优先级' },\n                    { value: 1, label: '低优先级' },\n                  ]}\n                  style={{ borderRadius: 6 }}\n                />\n              </Form.Item>\n            </Form>\n          </Modal>\n        </Spin>\n      )}\n    </Card>\n  );\n};\n\nexport default TodoManagement;\n", "// This icon file is generated automatically.\nvar BarChartOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm-600-80h56c4.4 0 8-3.6 8-8V560c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v144c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V384c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v320c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V462c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v242c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V304c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v400c0 4.4 3.6 8 8 8z\" } }] }, \"name\": \"bar-chart\", \"theme\": \"outlined\" };\nexport default BarChartOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport Bar<PERSON>hartOutlinedSvg from \"@ant-design/icons-svg/es/asn/BarChartOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar BarChartOutlined = function BarChartOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: BarChartOutlinedSvg\n  }));\n};\n\n/**![bar-chart](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4OCA3OTJIMjAwVjE2OGMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2Njg4YzAgNC40IDMuNiA4IDggOGg3NTJjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6bS02MDAtODBoNTZjNC40IDAgOC0zLjYgOC04VjU2MGMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2MTQ0YzAgNC40IDMuNiA4IDggOHptMTUyIDBoNTZjNC40IDAgOC0zLjYgOC04VjM4NGMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2MzIwYzAgNC40IDMuNiA4IDggOHptMTUyIDBoNTZjNC40IDAgOC0zLjYgOC04VjQ2MmMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2MjQyYzAgNC40IDMuNiA4IDggOHptMTUyIDBoNTZjNC40IDAgOC0zLjYgOC04VjMwNGMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2NDAwYzAgNC40IDMuNiA4IDggOHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(BarChartOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'BarChartOutlined';\n}\nexport default RefIcon;", "import {\n  Bar<PERSON>hartOutlined,\n  MailOutlined,\n  PhoneOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Avatar,\n  Card,\n  Col,\n  Flex,\n  Row,\n  Space,\n  Spin,\n  Typography,\n} from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type {\n  UserPersonalStatsResponse,\n  UserProfileDetailResponse,\n} from '@/types/api';\n\nconst { Title, Text } = Typography;\n\nconst UserProfileCard: React.FC = () => {\n  // 用户详细信息状态\n  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({\n    name: '',\n    position: '',\n    email: '',\n    telephone: '',\n    registerDate: '',\n    lastLoginTime: '',\n    lastLoginTeam: '',\n    teamCount: 0,\n    avatar: '',\n  });\n  const [userInfoLoading, setUserInfoLoading] = useState(true);\n  const [userInfoError, setUserInfoError] = useState<string | null>(null);\n\n  // 个人统计数据状态\n  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>(\n    {\n      vehicles: 0,\n      personnel: 0,\n      warnings: 0,\n      alerts: 0,\n    },\n  );\n  const [statsLoading, setStatsLoading] = useState(true);\n  const [statsError, setStatsError] = useState<string | null>(null);\n\n  // 状态管理\n\n  // 获取用户数据\n  useEffect(() => {\n    console.log('UserProfileCard: useEffect 开始执行');\n\n    const fetchUserData = async () => {\n      try {\n        console.log('UserProfileCard: 开始获取用户数据');\n\n        // 分别获取用户详细信息和统计数据，避免一个失败影响另一个\n        const userDetailPromise = UserService.getUserProfileDetail().catch(\n          (error) => {\n            console.error('获取用户详细信息失败:', error);\n            setUserInfoError('获取用户详细信息失败，请稍后重试');\n            return null;\n          },\n        );\n\n        const statsPromise = UserService.getUserPersonalStats().catch(\n          (error) => {\n            console.error('获取统计数据失败:', error);\n            setStatsError('获取统计数据失败，请稍后重试');\n            return null;\n          },\n        );\n\n        const [userDetail, stats] = await Promise.all([\n          userDetailPromise,\n          statsPromise,\n        ]);\n\n        if (userDetail) {\n          console.log('UserProfileCard: 获取到用户详细信息:', userDetail);\n          setUserInfo(userDetail);\n          setUserInfoError(null);\n        }\n\n        if (stats) {\n          console.log('UserProfileCard: 获取到统计数据:', stats);\n          setPersonalStats(stats);\n          setStatsError(null);\n        }\n      } catch (error) {\n        console.error('获取用户数据时发生未知错误:', error);\n        setUserInfoError('获取用户数据失败，请刷新页面重试');\n        setStatsError('获取统计数据失败，请刷新页面重试');\n      } finally {\n        setUserInfoLoading(false);\n        setStatsLoading(false);\n      }\n    };\n\n    fetchUserData();\n  }, []);\n\n  return (\n    <>\n      {/* 用户信息主卡片 */}\n      {userInfoError ? (\n        <Alert\n          message=\"用户信息加载失败\"\n          description={userInfoError}\n          type=\"error\"\n          showIcon\n          style={{ marginBottom: 24 }}\n        />\n      ) : (\n        <Spin spinning={userInfoLoading}>\n          {/* 使用 Card 组件替代自定义 div */}\n          <Card\n            style={{\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              borderRadius: 16,\n              color: 'white',\n              position: 'relative',\n              overflow: 'hidden',\n              minHeight: 140, // 改为最小高度，允许内容撑开\n              border: 'none',\n            }}\n            styles={{\n              body: {\n                padding: '16px 16px 16px 16px', // 统一使用16px内边距\n                height: '100%',\n              },\n            }}\n          >\n            {/* 装饰性背景元素 */}\n            <div\n              style={{\n                position: 'absolute',\n                top: -25,\n                right: -25,\n                width: 100,\n                height: 100,\n                background: 'rgba(255,255,255,0.1)',\n                borderRadius: '50%',\n              }}\n            />\n            <div\n              style={{\n                position: 'absolute',\n                bottom: -30,\n                left: -30,\n                width: 80,\n                height: 80,\n                background: 'rgba(255,255,255,0.05)',\n                borderRadius: '50%',\n              }}\n            />\n            <div\n              style={{\n                position: 'absolute',\n                top: '50%',\n                right: '20%',\n                width: 60,\n                height: 60,\n                background: 'rgba(255,255,255,0.03)',\n                borderRadius: '50%',\n                transform: 'translateY(-50%)',\n              }}\n            />\n\n            {/* 主要内容区域 - 使用响应式网格布局 */}\n            <Row\n              gutter={[16, 12]}\n              align=\"middle\"\n              style={{\n                position: 'relative',\n                zIndex: 1,\n                width: '100%',\n                minHeight: '100%',\n              }}\n            >\n              {/* 第一列：用户基本信息区域 */}\n              <Col xs={24} sm={24} md={8} lg={7} xl={6}>\n                <Flex align=\"center\" style={{ minHeight: '80px' }}>\n                  {/* 用户头像 - 使用 Ant Design Avatar 组件 */}\n                  <Avatar\n                    size={64}\n                    shape=\"square\"\n                    style={{\n                      backgroundColor: 'rgba(255,255,255,0.2)',\n                      marginRight: 20,\n                      fontSize: 24,\n                      fontWeight: 600,\n                      border: '2px solid rgba(255,255,255,0.3)',\n                    }}\n                  >\n                    {userInfo.name ? (\n                      userInfo.name.charAt(0).toUpperCase()\n                    ) : (\n                      <UserOutlined />\n                    )}\n                  </Avatar>\n\n                  {/* 用户信息 - 使用 Space 组件垂直布局 */}\n                  <Space direction=\"vertical\" size={4}>\n                    <Title\n                      level={3}\n                      style={{\n                        margin: 0,\n                        color: 'white',\n                        fontSize: 22,\n                        fontWeight: 600,\n                      }}\n                    >\n                      {userInfo.name || '加载中...'}\n                    </Title>\n\n                    {/* 联系信息 - 使用 Space 组件垂直排列 */}\n                    <Space direction=\"vertical\" size={4}>\n                      {userInfo.email && (\n                        <Space size={6} align=\"center\">\n                          <MailOutlined\n                            style={{\n                              fontSize: 13,\n                              color: 'rgba(255,255,255,0.9)',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              color: 'rgba(255,255,255,0.9)',\n                              fontSize: 12,\n                            }}\n                          >\n                            {userInfo.email}\n                          </Text>\n                        </Space>\n                      )}\n                      {userInfo.telephone && (\n                        <Space size={6} align=\"center\">\n                          <PhoneOutlined\n                            style={{\n                              fontSize: 13,\n                              color: 'rgba(255,255,255,0.9)',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              color: 'rgba(255,255,255,0.9)',\n                              fontSize: 12,\n                            }}\n                          >\n                            {userInfo.telephone}\n                          </Text>\n                        </Space>\n                      )}\n                    </Space>\n\n                    {/* 注册日期 */}\n                    {userInfo.registerDate && (\n                      <Text\n                        style={{\n                          fontSize: 13,\n                          color: 'rgba(255,255,255,0.8)',\n                          fontWeight: 500,\n                        }}\n                      >\n                        注册于 {userInfo.registerDate}\n                      </Text>\n                    )}\n                  </Space>\n                </Flex>\n              </Col>\n\n              {/* 第二列：数据概览区域 - 两行结构 */}\n              <Col xs={24} sm={24} md={8} lg={10} xl={12}>\n                <Flex\n                  vertical\n                  justify=\"center\"\n                  style={{\n                    minHeight: '80px',\n                    textAlign: 'center',\n                    padding: '8px 0',\n                  }}\n                >\n                  {/* 第一行：数据概览标题和图标 */}\n                  <Space\n                    align=\"center\"\n                    style={{\n                      justifyContent: 'center',\n                      marginBottom: 16,\n                    }}\n                  >\n                    <BarChartOutlined\n                      style={{\n                        fontSize: 16,\n                        color: 'rgba(255,255,255,0.9)',\n                      }}\n                    />\n                    <Text\n                      style={{\n                        color: 'rgba(255,255,255,0.9)',\n                        fontSize: 14,\n                        fontWeight: 600,\n                      }}\n                    >\n                      数据概览\n                    </Text>\n                  </Space>\n\n                  {/* 第二行：指标卡片 */}\n                  {statsError ? (\n                    <Text\n                      style={{ fontSize: 12, color: 'rgba(255,255,255,0.8)' }}\n                    >\n                      数据加载失败\n                    </Text>\n                  ) : (\n                    <Spin spinning={statsLoading}>\n                      <Row gutter={[4, 8]} justify=\"center\">\n                        <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                          <div style={{ textAlign: 'center' }}>\n                            <div\n                              style={{\n                                fontSize: 16,\n                                fontWeight: 700,\n                                color: 'white',\n                                lineHeight: 1,\n                              }}\n                            >\n                              {personalStats.vehicles}\n                            </div>\n                            <div\n                              style={{\n                                fontSize: 11,\n                                color: 'rgba(255,255,255,0.8)',\n                                marginTop: 2,\n                              }}\n                            >\n                              车辆\n                            </div>\n                          </div>\n                        </Col>\n                        <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                          <div style={{ textAlign: 'center' }}>\n                            <div\n                              style={{\n                                fontSize: 16,\n                                fontWeight: 700,\n                                color: 'white',\n                                lineHeight: 1,\n                              }}\n                            >\n                              {personalStats.personnel}\n                            </div>\n                            <div\n                              style={{\n                                fontSize: 11,\n                                color: 'rgba(255,255,255,0.8)',\n                                marginTop: 2,\n                              }}\n                            >\n                              人员\n                            </div>\n                          </div>\n                        </Col>\n                        <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                          <div style={{ textAlign: 'center' }}>\n                            <div\n                              style={{\n                                fontSize: 16,\n                                fontWeight: 700,\n                                color: 'white',\n                                lineHeight: 1,\n                              }}\n                            >\n                              {personalStats.warnings}\n                            </div>\n                            <div\n                              style={{\n                                fontSize: 11,\n                                color: 'rgba(255,255,255,0.8)',\n                                marginTop: 2,\n                              }}\n                            >\n                              预警\n                            </div>\n                          </div>\n                        </Col>\n                        <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                          <div style={{ textAlign: 'center' }}>\n                            <div\n                              style={{\n                                fontSize: 16,\n                                fontWeight: 700,\n                                color: 'white',\n                                lineHeight: 1,\n                              }}\n                            >\n                              {personalStats.alerts}\n                            </div>\n                            <div\n                              style={{\n                                fontSize: 11,\n                                color: 'rgba(255,255,255,0.8)',\n                                marginTop: 2,\n                              }}\n                            >\n                              告警\n                            </div>\n                          </div>\n                        </Col>\n                      </Row>\n                    </Spin>\n                  )}\n                </Flex>\n              </Col>\n\n              {/* 第三列：最近活动信息 */}\n              <Col xs={24} sm={24} md={8} lg={7} xl={6}>\n                <Flex\n                  vertical\n                  justify=\"center\"\n                  style={{ minHeight: '80px', padding: '8px 0' }}\n                >\n                  <Space direction=\"vertical\" size={10}>\n                    <Space direction=\"vertical\" size={4}>\n                      <Text\n                        style={{\n                          fontSize: 12,\n                          color: 'rgba(255,255,255,0.8)',\n                          fontWeight: 500,\n                        }}\n                      >\n                        最后登录时间\n                      </Text>\n                      <Text\n                        style={{\n                          fontSize: 14,\n                          color: 'white',\n                          fontWeight: 600,\n                          lineHeight: 1.3,\n                        }}\n                      >\n                        {userInfo.lastLoginTime || '暂无记录'}\n                      </Text>\n                    </Space>\n                    <Space direction=\"vertical\" size={4}>\n                      <Text\n                        style={{\n                          fontSize: 12,\n                          color: 'rgba(255,255,255,0.8)',\n                          fontWeight: 500,\n                        }}\n                      >\n                        最后登录团队\n                      </Text>\n                      <Text\n                        style={{\n                          fontSize: 14,\n                          color: 'white',\n                          fontWeight: 600,\n                          lineHeight: 1.3,\n                        }}\n                      >\n                        {userInfo.lastLoginTeam || '暂无记录'}\n                      </Text>\n                    </Space>\n                  </Space>\n                </Flex>\n              </Col>\n            </Row>\n          </Card>\n        </Spin>\n      )}\n    </>\n  );\n};\n\nexport default UserProfileCard;\n", "import { useModel, history } from '@umijs/max';\nimport { Card, Col, Row, Spin } from 'antd';\nimport React, { useEffect } from 'react';\nimport UserFloatButton from '@/components/FloatButton';\nimport TeamListCard from './TeamListCard';\nimport TodoManagement from './TodoManagement';\nimport UserProfileCard from './UserProfileCard';\n\nconst PersonalCenterPage: React.FC = () => {\n  const { initialState, loading } = useModel('@@initialState');\n\n  // 如果正在加载，显示加载状态\n  if (loading) {\n    return (\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff',\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n        }}\n      >\n        <Spin size=\"large\" />\n        <div style={{ marginLeft: 16 }}>正在加载用户信息...</div>\n      </div>\n    );\n  }\n\n  // 如果用户未登录，跳转到登录页\n  useEffect(() => {\n    if (!loading && !initialState?.currentUser) {\n      history.push('/user/login');\n    }\n  }, [loading, initialState?.currentUser]);\n\n  return (\n    <>\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff',\n          padding: '12px 12px 24px 12px', // 移动端减少左右边距\n        }}\n      >\n        {/* 大的容器区域 */}\n        <Card\n          style={{\n            width: '100%',\n            minHeight: 'calc(100vh - 48px)',\n            borderRadius: '12px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',\n          }}\n          styles={{\n            body: {\n              padding: '24px',\n            },\n          }}\n        >\n        <Row gutter={[16, 16]} style={{ margin: 0 }}>\n          {/* 个人信息卡片 - 全宽显示 */}\n          <Col xs={24} style={{ marginBottom: 8 }}>\n            <UserProfileCard />\n          </Col>\n\n          {/* 待办事项 - 响应式布局 */}\n          <Col\n            xs={24}\n            sm={24}\n            md={24}\n            lg={12}\n            xl={12}\n            xxl={12}\n            style={{ marginBottom: 8 }}\n          >\n            <TodoManagement />\n          </Col>\n\n          {/* 团队列表 - 响应式布局 */}\n          <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>\n            <TeamListCard />\n          </Col>\n        </Row>\n      </Card>\n    </div>\n\n    {/* 浮动按钮 */}\n    <UserFloatButton />\n  </>\n  );\n};\n\nexport default PersonalCenterPage;\n"], "names": [], "mappings": "+gBACI,EAAc,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,OAAQ,EAAG,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,+wBAAgxB,CAAE,EAAE,AAAC,EAAG,KAAQ,MAAO,MAAS,UAAW,2BCch9B,EAAuB,EAAM,UAAU,CARzB,SAAqB,CAAK,CAAE,CAAG,EAC/C,OAAoB,EAAM,aAAa,CAAC,SAAQ,CAAE,GAAA,SAAQ,EAAC,CAAC,EAAG,EAAO,CACpE,IAAK,EACL,KAAM,CACR,IACF,uGCXI,EAAsB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,OAAQ,EAAG,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,2FAA4F,CAAE,EAAG,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,+KAAgL,CAAE,EAAE,AAAC,EAAG,KAAQ,eAAgB,MAAS,UAAW,ECcngB,EAAuB,EAAM,UAAU,CARjB,SAA6B,CAAK,CAAE,CAAG,EAC/D,OAAoB,EAAM,aAAa,CAAC,SAAQ,CAAE,GAAA,SAAQ,EAAC,CAAC,EAAG,EAAO,CACpE,IAAK,EACL,KAAM,CACR,IACF,4QCXO,IAAM,GAAiB,CAAC,OAAQ,SAAU,eAAe,CACnD,GAAuB,CAAC,aAAc,WAAY,QAAS,MAAO,SAAU,gBAAiB,eAAgB,eAAgB,UAAW,SAAU,OAAQ,QAAQ,CAClK,GAAmB,CAAC,SAAU,QAAS,MAAO,aAAc,WAAY,aAAc,WAAY,WAAY,SAAU,UAAU,CACzI,GAAa,CAAC,EAAW,KAC7B,IAAM,EAAO,AAAe,CAAA,IAAf,EAAM,IAAI,CAAY,OAAS,EAAM,IAAI,CACtD,MAAO,CACL,CAAC,CAAC,EAAE,EAAU,MAAM,EAAE,EAAK,CAAC,CAAC,CAAE,GAAQ,GAAe,QAAQ,CAAC,EACjE,EACF,EACM,GAAc,CAAC,EAAW,KAC9B,IAAM,EAAW,CAAC,EAKlB,OAJA,GAAiB,OAAO,CAAC,IACvB,CAAQ,CAAC,CAAC,EAAE,EAAU,OAAO,EAAE,EAAO,CAAC,CAAC,CAAG,EAAM,KAAK,GAAK,EAC7D,GACA,CAAQ,CAAC,CAAC,EAAE,EAAU,cAAc,CAAC,CAAC,CAAG,CAAC,EAAM,KAAK,EAAI,CAAC,CAAC,EAAM,QAAQ,CAClE,EACT,EACM,GAAgB,CAAC,EAAW,KAChC,IAAM,EAAa,CAAC,EAIpB,OAHA,GAAqB,OAAO,CAAC,IAC3B,CAAU,CAAC,CAAC,EAAE,EAAU,SAAS,EAAE,EAAO,CAAC,CAAC,CAAG,EAAM,OAAO,GAAK,EACnE,GACO,EACT,ECtBM,GAAe,IACnB,GAAM,CACJ,aAAA,CAAY,CACb,CAAG,EACJ,MAAO,CACL,CAAC,EAAa,CAAE,CACd,QAAS,OACT,OAAQ,EACR,QAAS,EACT,aAAc,CACZ,cAAe,QACjB,EACA,QAAS,CACP,UAAW,KACb,EACA,UAAW,CACT,QAAS,MACX,CACF,CACF,EACF,EACM,GAAkB,IACtB,GAAM,CACJ,aAAA,CAAY,CACb,CAAG,EACJ,MAAO,CACL,CAAC,EAAa,CAAE,CACd,cAAe,CACb,IAAK,EAAM,SAAS,AACtB,EACA,eAAgB,CACd,IAAK,EAAM,OAAO,AACpB,EACA,cAAe,CACb,IAAK,EAAM,SAAS,AACtB,CACF,CACF,EACF,EACM,GAAmB,IACvB,GAAM,CACJ,aAAA,CAAY,CACb,CAAG,EACE,EAAY,CAAC,EAMnB,OALA,GAAe,OAAO,CAAC,IACrB,CAAS,CAAC,CAAC,EAAE,EAAa,MAAM,EAAE,EAAM,CAAC,CAAC,CAAG,CAC3C,SAAU,CACZ,EACF,GACO,EACT,EACM,GAAqB,IACzB,GAAM,CACJ,aAAA,CAAY,CACb,CAAG,EACE,EAAa,CAAC,EAMpB,OALA,GAAiB,OAAO,CAAC,IACvB,CAAU,CAAC,CAAC,EAAE,EAAa,OAAO,EAAE,EAAM,CAAC,CAAC,CAAG,CAC7C,WAAY,CACd,EACF,GACO,EACT,EACM,GAAyB,IAC7B,GAAM,CACJ,aAAA,CAAY,CACb,CAAG,EACE,EAAe,CAAC,EAMtB,OALA,GAAqB,OAAO,CAAC,IAC3B,CAAY,CAAC,CAAC,EAAE,EAAa,SAAS,EAAE,EAAM,CAAC,CAAC,CAAG,CACjD,eAAgB,CAClB,EACF,GACO,EACT,MAEA,GAAe,GAAA,eAAa,EAAC,OAAQ,IACnC,GAAM,CACJ,UAAA,CAAS,CACT,QAAA,CAAO,CACP,UAAA,CAAS,CACV,CAAG,EACE,EAAY,GAAA,aAAU,EAAC,EAAO,CAClC,UAAW,EACX,QAAS,EACT,UAAW,CACb,GACA,MAAO,CAAC,GAAa,GAAY,GAAgB,GAAY,GAAiB,GAAY,GAAmB,GAAY,GAAuB,GAAW,CAC7J,EAbqC,IAAO,CAAA,CAAC,CAAA,EAanB,CAGxB,WAAY,CAAA,CACd,GC5FI,GAAS,IAAI,EAAI,IAAI,CAAC,MAAM,EAAI,SAAU,CAAC,CAAE,CAAC,EAChD,IAAI,EAAI,CAAC,EACT,IAAK,IAAI,KAAK,EAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAG,IAAM,AAAe,EAAf,EAAE,OAAO,CAAC,IAAQ,CAAA,CAAC,CAAC,EAAE,CAAG,CAAC,CAAC,EAAE,AAAD,EAC/F,GAAI,AAAK,MAAL,GAAa,AAAwC,YAAxC,OAAO,OAAO,qBAAqB,CAAiB,IAAK,IAAI,EAAI,EAAG,EAAI,OAAO,qBAAqB,CAAC,GAAI,EAAI,EAAE,MAAM,CAAE,IAChH,EAAlB,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAS,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAG,CAAC,CAAC,EAAE,GAAG,CAAA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,AAAD,EAElG,OAAO,EACT,EAQA,IAAM,GAAoB,SAAK,CAAC,UAAU,CAAC,CAAC,EAAO,KACjD,GAAM,CACF,UAAW,CAAkB,CAC7B,cAAA,CAAa,CACb,UAAA,CAAS,CACT,MAAA,CAAK,CACL,KAAA,CAAI,CACJ,IAAA,CAAG,CACH,SAAA,EAAW,CAAA,CAAK,CAChB,UAAW,EAAY,KAAK,CAC7B,CAAG,EACJ,EAAc,GAAO,EAAO,CAAC,YAAa,gBAAiB,YAAa,QAAS,OAAQ,MAAO,WAAY,YAAY,EACpH,CACJ,KAAM,CAAO,CACb,UAAW,CAAY,CACvB,aAAA,CAAY,CACb,CAAG,SAAK,CAAC,UAAU,CAAC,eAAa,EAC5B,EAAY,EAAa,OAAQ,GACjC,CAAC,EAAY,EAAQ,EAAU,CAAG,GAAS,GAC3C,EAAiB,MAAA,EAA2C,EAAW,MAAA,EAAyC,KAAK,EAAI,EAAQ,QAAQ,CACzI,EAAY,GAAA,SAAU,EAAC,EAAW,EAAe,MAAA,EAAyC,KAAK,EAAI,EAAQ,SAAS,CAAE,EAAW,EAAQ,KFXxI,SAAU,EAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,EAAG,GEW+G,EAAW,IFX3F,GEWgF,EAAW,IFX3D,GEWgD,EAAW,KAAQ,CAChM,CAAC,CAAC,EAAE,EAAU,IAAI,CAAC,CAAC,CAAE,AAAiB,QAAjB,EACtB,CAAC,CAAC,EAAE,EAAU,KAAK,EAAE,EAAI,CAAC,CAAC,CAAE,GAAA,cAAY,EAAC,GAC1C,CAAC,CAAC,EAAE,EAAU,SAAS,CAAC,CAAC,CAAE,CAC7B,GACM,EAAc,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,EAAG,MAAA,EAAyC,KAAK,EAAI,EAAQ,KAAK,EAAG,GAOtH,OANI,GACF,CAAA,EAAY,IAAI,CAAG,CAAG,EAEpB,GAAO,CAAC,GAAA,cAAY,EAAC,IACvB,CAAA,EAAY,GAAG,CAAG,CAAE,EAEf,EAAwB,SAAK,CAAC,aAAa,CAAC,EAAW,OAAO,MAAM,CAAC,CAC1E,IAAK,EACL,UAAW,EACX,MAAO,CACT,EAAG,GAAA,SAAI,EAAC,EAAa,CAAC,UAAW,OAAQ,QAAQ,KACnD,+QCdA,GAAM,CAAE,KAAA,EAAI,CAAE,MAAA,EAAK,CAAE,CAAG,UAAU,CAC5B,CAAE,SAAA,EAAQ,CAAE,CAAG,UAAK,CAKpB,GAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEhB,CAAC,CAEK,GAAyB,KAE7B,GAAM,CAAC,EAAO,EAAS,CAAG,GAAA,UAAQ,EAAuB,EAAE,EACrD,CAAC,EAAS,EAAW,CAAG,GAAA,UAAQ,EAAC,CAAA,GACjC,CAAC,EAAO,EAAS,CAAG,GAAA,UAAQ,EAAgB,MAC5C,CAAC,EAAiB,EAAmB,CAAG,GAAA,UAAQ,EAAgB,MAGhE,CAAC,EAAoB,EAAsB,CAAG,GAAA,UAAQ,EAAC,CAAA,GACvD,CAAC,EAAe,EAAiB,CAAG,GAAA,UAAQ,EAAC,CAAA,GAC7C,CAAC,EAAK,CAAG,UAAI,CAAC,OAAO,GAErB,CAAE,aAAA,CAAY,CAAE,gBAAA,CAAe,CAAE,CAAG,GAAA,UAAQ,EAAC,kBAC7C,QAAc,SAAA,EAAc,WAAW,CAGvC,EAAqB,GAAA,4BAAyB,IAC9C,EAAgB,GAAA,4BAAyB,IACzC,EAAiB,GAAA,wBAAqB,IAOtC,EAAqB,CAAC,CAC1B,CAAA,GACA,GACA,GACA,EAAY,EAAE,GAAK,GACnB,GACA,GAAA,sBAAmB,EAAC,EAAe,EAAkB,EAIjD,EAAsB,EAAqB,EAAqB,KAGtE,QAAQ,GAAG,CAAC,yCAAsB,CAChC,WAAW,OAAE,SAAA,EAAa,EAAE,CAC5B,mBAAA,EACA,cAAA,EACA,eAAA,EACA,mBAAA,EACA,oBAAA,EACA,2BAA4B,EAAA,KAAiB,GAAqB,GAAA,sBAAmB,EAAC,EAAe,GACrG,wBAAyB,CAAC,QAAC,SAAA,EAAc,WAAW,CACtD,GAGA,GAAA,WAAS,EAAC,KACR,IAAM,EAAa,UACjB,GAAI,CACF,EAAW,CAAA,GACX,EAAS,MACT,IAAM,EAAY,MAAM,cAAW,CAAC,qBAAqB,GACzD,EAAS,GACX,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,oDAAa,GAC3B,EAAS,oDACX,QAAU,CACR,EAAW,CAAA,GACb,CACF,SAGI,SAAA,EAAc,WAAW,GAC3B,IAEJ,EAAG,OAAC,SAAA,EAAc,WAAW,CAAC,EAG9B,GAAA,WAAS,EAAC,YAEH,SAAA,EAAc,WAAW,IAC5B,EAAS,EAAE,EACX,EAAS,MACT,EAAW,CAAA,GACX,EAAmB,OAEvB,EAAG,OAAC,SAAA,EAAc,WAAW,CAAC,EAG9B,GAAA,WAAS,EAAC,KACR,QAAQ,GAAG,CAAC,oDAAa,CACvB,WAAW,OAAE,SAAA,EAAa,EAAE,CAC5B,oBAAA,EACA,mBAAA,CACF,GACF,EAAG,OAAC,SAAA,EAAa,EAAE,CAAE,EAAqB,EAAmB,EAG7D,IAAM,EAAmB,MAAO,IAC9B,EAAiB,CAAA,GACjB,GAAI,CACW,MAAM,cAAW,CAAC,UAAU,CAAC,GAC1C,UAAO,CAAC,OAAO,CAAC,8CAGhB,IAAM,EAAY,MAAM,cAAW,CAAC,qBAAqB,GACzD,EAAS,GAGT,EAAsB,CAAA,GACtB,EAAK,WAAW,GAClB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wCAAW,GACzB,UAAO,CAAC,KAAK,CAAC,gEAChB,QAAU,CACR,EAAiB,CAAA,GACnB,CACF,EAGM,EAAmB,MAAO,EAAgB,KAE9C,GAAI,QAAC,SAAA,EAAc,WAAW,EAAE,CAC9B,UAAO,CAAC,KAAK,CAAC,4BACd,OACF,CAEA,GAAI,CAIF,GAHA,EAAmB,GAGf,IAAW,EAAqB,CAClC,UAAO,CAAC,OAAO,CAAC,CAAC,wCAAK,EAAE,EAAS,CAAC,EAClC,SAAO,CAAC,IAAI,CAAC,cACb,OACF,CAGA,IAAM,EAAW,MAAM,cAAW,CAAC,UAAU,CAAC,CAAE,OAAA,CAAO,GAIrD,EAAS,oBAAoB,EAC7B,EAAS,IAAI,EACb,EAAS,IAAI,CAAC,EAAE,GAAK,GAErB,UAAO,CAAC,OAAO,CAAC,CAAC,wDAAO,EAAE,EAAS,CAAC,EAGhC,GACF,GAAA,sBAAmB,EAAC,EAAe,UAMnC,SAAA,EAAc,aAAa,UAC3B,SAAA,EAAc,aAAa,GAC3B,GAGA,QAAQ,GAAG,CAAC,CACV,EAAa,aAAa,GAC1B,EAAa,aAAa,GAC3B,EACE,IAAI,CAAC,CAAC,CAAC,EAAa,EAAY,IAC3B,GAAe,EAAY,EAAE,GAAK,GACpC,EAAgB,CACd,GAAG,CAAY,CACf,YAAA,EACA,YAAA,CACF,GAEJ,GACC,KAAK,CAAC,AAAC,IACN,QAAQ,KAAK,CAAC,0CAAuB,GACvC,GAIJ,SAAO,CAAC,IAAI,CAAC,gBAEb,QAAQ,KAAK,CAAC,sHACd,UAAO,CAAC,KAAK,CAAC,iEAElB,CAAE,MAAO,EAAY,CACnB,QAAQ,KAAK,CAAC,wCAAW,GAIpB,EAAM,OAAO,EAAI,AAAkB,oBAAlB,EAAM,OAAO,EACjC,UAAO,CAAC,KAAK,CAAC,wFAElB,QAAU,CACR,EAAmB,MACrB,CACF,EAEA,MACE,iCAEE,UAAC,SAAM,wBAAyB,CAAE,OAAQ,EAAO,IAEjD,UAAC,SAAI,EACH,UAAU,iBACV,MAAO,CACL,aAAc,GACd,UAAW,8BACX,OAAQ,OACR,WAAY,2CACd,EACA,MACE,WAAC,IAAK,QAAQ,gBAAgB,MAAM,mBAClC,UAAC,IACC,MAAO,EACP,MAAO,CACL,OAAQ,EACR,WAAY,4CACZ,qBAAsB,OACtB,oBAAqB,cACrB,WAAY,GACd,WACD,6BAGD,UAAC,SAAM,EACL,KAAK,UACL,KAAM,UAAC,SAAY,KACnB,QAAS,IAAM,EAAsB,CAAA,GACrC,MAAO,CACL,aAAc,EACd,WAAY,4CACZ,OAAQ,OACR,UAAW,mCACb,WACD,yCAMJ,EACC,UAAC,SAAK,EACJ,QAAQ,mDACR,YAAa,EACb,KAAK,QACL,QAAQ,IACR,MAAO,CAAE,aAAc,EAAG,IAG5B,UAAC,SAAI,EAAC,SAAU,WACb,OAAC,SAAA,EAAc,WAAW,EAIvB,AAAiB,IAAjB,EAAM,MAAM,EAAW,EAKzB,UAAC,UAAI,EACH,WAAY,EACZ,WAAY,AAAC,QAmRQ,EAiCA,EAiCA,EAiCA,QArXnB,UAAC,UAAI,CAAC,IAAI,WACR,UAAC,SAAI,EACH,UAAU,YACV,MAAO,CACL,WACE,IAAwB,EAAK,EAAE,CAC3B,4CACA,OACN,aAAc,EACd,UACE,IAAwB,EAAK,EAAE,CAC3B,qCACA,6BACN,MAAO,OACP,WAAY,CAAC,UAAU,EAAE,EAAK,SAAS,CAAG,UAAY,UAAU,CAAC,CACjE,WAAY,wCACZ,OACE,IAAwB,EAAK,EAAE,CAC3B,oBACA,oBACN,QAAS,YACT,SAAU,WACV,SAAU,QACZ,EACA,SAAS,IACT,aAAc,AAAC,IACT,IAAwB,EAAK,EAAE,GACjC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,CAAG,mBAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,CAC7B,+BAEN,EACA,aAAc,AAAC,IACT,IAAwB,EAAK,EAAE,GACjC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,CAAG,gBAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,CAC7B,8BAEN,WAGA,WAAC,SAAG,EACF,OAAQ,CAAC,EAAG,EAAE,CACd,MAAM,SACN,MAAO,CAAE,MAAO,MAAO,YAGvB,UAAC,SAAG,EAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,YACvC,WAAC,IAAK,QAAQ,IAAC,IAAK,EAAG,UAAU,2BAE/B,WAAC,IAAK,MAAM,SAAS,IAAK,EAAG,KAAK,iBAChC,WAAC,OACC,MAAO,CACL,OAAQ,UACR,QAAS,UACT,aAAc,EACd,WAAY,gBACZ,QAAS,OACT,WAAY,SACZ,IAAK,CACP,EACA,QAAS,IACP,EAAiB,EAAK,EAAE,CAAE,EAAK,IAAI,EAErC,aAAc,AAAC,IACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,CAC9B,2BACJ,EACA,aAAc,AAAC,IACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,CAC9B,cACJ,YAEA,UAAC,IACC,MAAM,IACN,MAAO,CACL,SAAU,GACV,MACE,IAAwB,EAAK,EAAE,CAC3B,UACA,UACN,WAAY,GACd,WAEC,EAAK,IAAI,GAEZ,UAAC,SAAa,EACZ,MAAO,CACL,SAAU,GACV,MACE,IAAwB,EAAK,EAAE,CAC3B,UACA,UACN,cAAe,SACf,QAAS,cACT,WAAY,QACd,OAKH,IAAwB,EAAK,EAAE,EAC9B,UAAC,QACC,MAAO,CACL,WAAY,UACZ,MAAO,QACP,QAAS,UACT,aAAc,EACd,SAAU,GACV,WAAY,GACd,WACD,iBAOF,IAAoB,EAAK,EAAE,EAC1B,WAAC,IAAK,MAAM,SAAS,IAAK,YACxB,UAAC,SAAI,EAAC,KAAK,UACX,UAAC,IAAK,MAAO,CAAE,SAAU,GAAI,MAAO,MAAO,WAAG,6BAQpD,WAAC,IAAK,MAAM,SAAS,IAAK,GAAI,KAAK,OAAO,UAAU,2BAClD,UAAC,UAAO,EACN,MAAO,CAAC,kDAAQ,EAAE,IAAI,KAAK,EAAK,SAAS,EAAE,cAAc,CAAC,SAAS,CAAC,UAEpE,WAAC,IAAK,MAAM,SAAS,IAAK,YACxB,UAAC,SAAmB,EAClB,MAAO,CAAE,MAAO,UAAW,SAAU,EAAG,IAE1C,WAAC,IACC,MAAO,CAAE,SAAU,GAAI,MAAO,SAAU,YACzC,iBACM,IAAI,KACP,EAAK,SAAS,EACd,kBAAkB,CAAC,iBAM1B,EAAK,UAAU,EACd,UAAC,UAAO,EACN,MAAO,CAAC,kDAAQ,EAAE,IAAI,KAAK,EAAK,UAAU,EAAE,cAAc,CAAC,SAAS,CAAC,UAErE,WAAC,IAAK,MAAM,SAAS,IAAK,YACxB,UAAC,SAAY,EACX,MAAO,CAAE,MAAO,UAAW,SAAU,EAAG,IAE1C,WAAC,IACC,MAAO,CAAE,SAAU,GAAI,MAAO,SAAU,YACzC,iBACM,IAAI,KACP,EAAK,UAAU,EACf,kBAAkB,CAAC,iBAM7B,UAAC,UAAO,EACN,MAAO,CAAC,kCAAM,EAAE,EAAK,WAAW,CAAC,QAAC,CAAC,UAEnC,WAAC,IAAK,MAAM,SAAS,IAAK,YACxB,UAAC,SAAY,EACX,MAAO,CAAE,MAAO,UAAW,SAAU,EAAG,IAE1C,WAAC,IACC,MAAO,CAAE,SAAU,GAAI,MAAO,SAAU,YAEvC,EAAK,WAAW,CAAC,qBAO1B,WAAC,IAAK,MAAM,SAAS,IAAK,EAAG,KAAK,OAAO,UAAU,+BAEjD,UAAC,QACC,MAAO,CACL,WAAY,EAAK,SAAS,CACtB,UACA,UACJ,MAAO,QACP,QAAS,UACT,aAAc,EACd,SAAU,GACV,WAAY,IACZ,QAAS,OACT,WAAY,SACZ,IAAK,CACP,WAEC,EAAK,SAAS,CACb,iCACE,UAAC,SAAa,EAAC,MAAO,CAAE,SAAU,CAAE,IAAK,wBAI3C,iCACE,UAAC,SAAY,EAAC,MAAO,CAAE,SAAU,CAAE,IAAK,oBAO9C,UAAC,QACC,MAAO,CACL,WAAY,EAAK,QAAQ,CAAG,UAAY,UACxC,MAAO,QACP,QAAS,UACT,aAAc,EACd,SAAU,GACV,WAAY,IACZ,QAAS,OACT,WAAY,SACZ,IAAK,CACP,WAEC,EAAK,QAAQ,CACZ,iCACE,UAAC,SAAmB,EAAC,MAAO,CAAE,SAAU,CAAE,IAAK,kBAIjD,iCACE,UAAC,GAAoB,MAAO,CAAE,SAAU,CAAE,IAAK,4BAU3D,UAAC,SAAG,EAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,YACvC,WAAC,SAAG,EACF,OAAQ,CAAC,EAAG,EAAE,CACd,QAAS,CAAE,GAAI,QAAS,GAAI,KAAM,YAGlC,UAAC,SAAG,EAAC,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,WACnC,UAAC,OACC,MAAO,CACL,WAAY,UACZ,OAAQ,oBACR,aAAc,EACd,QAAS,UACT,UAAW,SACX,SAAU,MACZ,WAEA,WAAC,IAAK,QAAQ,IAAC,MAAM,SAAS,IAAK,YACjC,UAAC,GACC,MAAO,CAAE,MAAO,UAAW,SAAU,EAAG,IAE1C,UAAC,IACC,MAAM,IACN,MAAO,CACL,SAAU,GACV,MAAO,UACP,WAAY,CACd,WAEC,SAAA,EAAA,EAAK,KAAK,YAAV,SAAA,EAAY,QAAQ,GAAI,IAE3B,UAAC,IAAK,MAAO,CAAE,SAAU,EAAG,MAAO,MAAO,WAAG,wBAQnD,UAAC,SAAG,EAAC,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,WACnC,UAAC,OACC,MAAO,CACL,WAAY,UACZ,OAAQ,oBACR,aAAc,EACd,QAAS,UACT,UAAW,SACX,SAAU,MACZ,WAEA,WAAC,IAAK,QAAQ,IAAC,MAAM,SAAS,IAAK,YACjC,UAAC,SAAY,EACX,MAAO,CAAE,MAAO,UAAW,SAAU,EAAG,IAE1C,UAAC,IACC,MAAM,IACN,MAAO,CACL,SAAU,GACV,MAAO,UACP,WAAY,CACd,WAEC,SAAA,EAAA,EAAK,KAAK,YAAV,SAAA,EAAY,SAAS,GAAI,IAE5B,UAAC,IAAK,MAAO,CAAE,SAAU,EAAG,MAAO,MAAO,WAAG,wBAQnD,UAAC,SAAG,EAAC,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,WACnC,UAAC,OACC,MAAO,CACL,WAAY,UACZ,OAAQ,oBACR,aAAc,EACd,QAAS,UACT,UAAW,SACX,SAAU,MACZ,WAEA,WAAC,IAAK,QAAQ,IAAC,MAAM,SAAS,IAAK,YACjC,UAAC,SAAyB,EACxB,MAAO,CAAE,MAAO,UAAW,SAAU,EAAG,IAE1C,UAAC,IACC,MAAM,IACN,MAAO,CACL,SAAU,GACV,MAAO,UACP,WAAY,CACd,WAEC,SAAA,EAAA,EAAK,KAAK,YAAV,SAAA,EAAY,QAAQ,GAAI,IAE3B,UAAC,IAAK,MAAO,CAAE,SAAU,EAAG,MAAO,MAAO,WAAG,wBAQnD,UAAC,SAAG,EAAC,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,WACnC,UAAC,OACC,MAAO,CACL,WAAY,UACZ,OAAQ,oBACR,aAAc,EACd,QAAS,UACT,UAAW,SACX,SAAU,MACZ,WAEA,WAAC,IAAK,QAAQ,IAAC,MAAM,SAAS,IAAK,YACjC,UAAC,SAAyB,EACxB,MAAO,CAAE,MAAO,UAAW,SAAU,EAAG,IAE1C,UAAC,IACC,MAAM,IACN,MAAO,CACL,SAAU,GACV,MAAO,UACP,WAAY,CACd,WAEC,SAAA,EAAA,EAAK,KAAK,YAAV,SAAA,EAAY,OAAO,GAAI,IAE1B,UAAC,IAAK,MAAO,CAAE,SAAU,EAAG,MAAO,MAAO,WAAG,wCA9XjE,UAAC,OAAI,MAAO,CAAE,UAAW,SAAU,QAAS,WAAY,WACtD,UAAC,IAAK,KAAK,qBAAY,2FALzB,UAAC,OAAI,MAAO,CAAE,UAAW,SAAU,QAAS,WAAY,WACtD,UAAC,IAAK,KAAK,qBAAY,6EAoZjC,UAAC,UAAK,EACJ,MACE,WAAC,IAAK,MAAM,SAAS,IAAK,YACxB,UAAC,SAAY,EAAC,MAAO,CAAE,MAAO,SAAU,IACxC,UAAC,iBAAK,sCAGV,KAAM,EACN,SAAU,KACR,EAAsB,CAAA,GACtB,EAAK,WAAW,GAClB,EACA,OAAQ,KACR,MAAO,IACP,MAAO,CAAE,IAAK,GAAI,WAElB,WAAC,UAAI,EACH,KAAM,EACN,OAAO,WACP,SAAU,EACV,aAAa,MACb,MAAO,CAAE,UAAW,EAAG,YAEvB,UAAC,UAAI,CAAC,IAAI,EACR,MAAM,2BACN,KAAK,OACL,MAAO,CACL,CAAE,SAAU,CAAA,EAAM,QAAS,kDAAW,EACtC,CAAE,IAAK,IAAK,QAAS,mFAAmB,EACxC,CAAE,IAAK,EAAG,QAAS,2EAAgB,EACpC,UAED,UAAC,UAAK,EAAC,YAAY,6CAAU,KAAK,YAGpC,UAAC,UAAI,CAAC,IAAI,EACR,MAAM,2BACN,KAAK,cACL,MAAO,CAAC,CAAE,IAAK,IAAK,QAAS,mFAAmB,EAAE,UAElD,UAAC,IACC,YAAY,qEACZ,KAAM,EACN,SAAS,IACT,UAAW,QAIf,UAAC,UAAI,CAAC,IAAI,EAAC,MAAO,CAAE,aAAc,EAAG,UAAW,EAAG,WACjD,WAAC,IAAK,QAAQ,MAAM,IAAK,aACvB,UAAC,SAAM,EACL,QAAS,KACP,EAAsB,CAAA,GACtB,EAAK,WAAW,GAClB,WACD,iBAGD,UAAC,SAAM,EACL,KAAK,UACL,SAAS,SACT,QAAS,EACT,MAAO,CACL,WAAY,4CACZ,OAAQ,MACV,WACD,0CASf,2UC5zBO,MAAM,GAIX,aAAa,cAAwC,CAEnD,MAAO,AADU,CAAA,MAAM,aAAU,CAAC,GAAG,CAAiB,SAAQ,EAC9C,IAAI,CACtB,CAKA,aAAa,WAAW,CAA0B,CAAyB,CAEzE,MAAO,AADU,CAAA,MAAM,aAAU,CAAC,IAAI,CAAe,SAAU,EAAO,EACtD,IAAI,CACtB,CAKA,aAAa,WACX,CAAU,CACV,CAA0B,CACH,CAKvB,MAAO,AAJU,CAAA,MAAM,aAAU,CAAC,GAAG,CACnC,CAAC,OAAO,EAAE,EAAG,CAAC,CACd,EACF,EACgB,IAAI,CACtB,CAKA,aAAa,WAAW,CAAU,CAAiB,CACjD,MAAM,aAAU,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAG,CAAC,EACxC,CAKA,aAAa,cAA2C,CAEtD,MAAO,AADU,CAAA,MAAM,aAAU,CAAC,GAAG,CAAoB,eAAc,EACvD,IAAI,CACtB,CACF,CCvBA,GAAM,CAAE,KAAA,EAAI,CAAE,CAAG,UAAU,CACrB,CAAE,QAAA,EAAO,CAAE,CAAG,UAAI,CASlB,GAAgD,AAAC,IAErD,GAAM,CAAC,EAAe,EAAiB,CAAG,GAAA,UAAQ,EAAiB,EAAE,EAC/D,CAAC,EAAW,EAAa,CAAG,GAAA,UAAQ,EAAoB,CAC5D,kBAAmB,EACnB,oBAAqB,EACrB,iBAAkB,EAClB,WAAY,EACZ,eAAgB,EAChB,qBAAsB,CACxB,GACM,CAAC,EAAS,EAAW,CAAG,GAAA,UAAQ,EAAC,CAAA,GACjC,CAAC,EAAO,EAAS,CAAG,GAAA,UAAQ,EAAgB,MAG5C,CAAC,EAAkB,EAAoB,CAAG,GAAA,UAAQ,EAAC,CAAA,GACnD,CAAC,EAAS,CAAG,UAAI,CAAC,OAAO,GACzB,CAAC,EAAe,EAAiB,CAAG,GAAA,UAAQ,EAAgB,MAG5D,CAAC,EAAW,EAAa,CAAG,GAAA,UAAQ,EACxC,WAEI,CAAC,EAAY,EAAc,CAAG,GAAA,UAAQ,EAAC,IAG7C,GAAA,WAAS,EAAC,KAyCR,AAxCsB,CAAA,UACpB,GAAI,CACF,EAAW,CAAA,GACX,EAAS,MAET,QAAQ,GAAG,CAAC,4DAGZ,IAAM,EAAe,GAAY,YAAY,GAAG,KAAK,CAAC,AAAC,IACrD,QAAQ,KAAK,CAAC,4CAAe,GACtB,EAAE,GAGL,EAAe,GAAY,YAAY,GAAG,KAAK,CAAC,AAAC,IACrD,QAAQ,KAAK,CAAC,4CAAe,GACtB,CACL,kBAAmB,EACnB,oBAAqB,EACrB,iBAAkB,EAClB,WAAY,EACZ,eAAgB,EAChB,qBAAsB,CACxB,IAGI,CAAC,EAAO,EAAM,CAAG,MAAM,QAAQ,GAAG,CAAC,CAAC,EAAc,EAAa,EAErE,QAAQ,GAAG,CAAC,sDAA8B,GAC1C,QAAQ,GAAG,CAAC,8DAA4B,GAExC,EAAiB,GACjB,EAAa,GACf,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,0EAAoB,GAClC,EAAS,4FACX,QAAU,CACR,EAAW,CAAA,GACb,CACF,CAAA,IAGF,EAAG,EAAE,EAGL,IAAM,EAAwB,AAAC,CAAA,GAAiB,EAAE,AAAD,EAAG,MAAM,CAAC,AAAC,GAEtD,CAAA,AAAc,YAAd,GAA2B,AAAgB,IAAhB,EAAK,MAAM,AAAK,GAC3C,CAAA,AAAc,cAAd,GAA6B,AAAgB,IAAhB,EAAK,MAAM,AAAK,GAI/C,CAAA,CAAA,IACA,CAAC,EAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,EAAW,WAAW,GAAE,GASzD,EAAyB,MAAO,IACpC,GAAI,CACF,IAAM,EAAO,EAAc,IAAI,CAAC,AAAC,GAAM,EAAE,EAAE,GAAK,GAChD,GAAI,CAAC,EAAM,CACT,UAAO,CAAC,KAAK,CAAC,kCACd,OACF,CAEA,IAAM,EAAY,AAAgB,IAAhB,EAAK,MAAM,CAAS,EAAI,EAC1C,QAAQ,GAAG,CAAC,CAAC,iEAAuB,EAAE,EAAG,IAAI,EAAE,EAAU,CAAC,EAE1D,MAAM,GAAY,UAAU,CAAC,EAAI,CAAE,OAAQ,CAAU,GAGrD,EACE,EAAc,GAAG,CAAC,AAAC,GACjB,EAAK,EAAE,GAAK,EAAK,CAAE,GAAG,CAAI,CAAE,OAAQ,CAAU,EAAI,IAKtD,GAAI,CACF,IAAM,EAAQ,MAAM,GAAY,YAAY,GAC5C,EAAa,GACf,CAAE,MAAO,EAAY,CACnB,QAAQ,KAAK,CAAC,oDAAa,GAE7B,CAEA,UAAO,CAAC,OAAO,CAAC,AAAc,IAAd,EAAkB,iCAAU,0DAC9C,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,oDAAa,GAC3B,UAAO,CAAC,KAAK,CAAC,wFAChB,CACF,EAEM,EAAwB,MAAO,IACnC,GAAI,CAGF,GAFA,QAAQ,GAAG,CAAC,2CAAwB,CAAE,cAAA,EAAe,OAAA,CAAO,GAExD,EAAe,CAEjB,IAAM,EAAc,MAAM,GAAY,UAAU,CAAC,EAAe,CAC9D,MAAO,EAAO,IAAI,CAClB,SAAU,EAAO,QAAQ,AAC3B,GAEA,EACE,EAAc,GAAG,CAAC,AAAC,GACjB,EAAK,EAAE,GAAK,EAAgB,EAAc,IAG9C,UAAO,CAAC,OAAO,CAAC,wCAClB,KAAO,CAEL,IAAM,EAAU,MAAM,GAAY,UAAU,CAAC,CAC3C,MAAO,EAAO,IAAI,CAClB,SAAU,EAAO,QAAQ,AAC3B,GAEA,EAAiB,CAAC,KAAY,EAAc,EAC5C,UAAO,CAAC,OAAO,CAAC,wCAClB,CAGA,GAAI,CACF,IAAM,EAAQ,MAAM,GAAY,YAAY,GAC5C,EAAa,GACf,CAAE,MAAO,EAAY,CACnB,QAAQ,KAAK,CAAC,oDAAa,GAE7B,CAGA,EAAoB,CAAA,GACpB,EAAiB,MACjB,EAAS,WAAW,GACtB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wCAAW,GACzB,IAAM,EAAS,EAAgB,eAAO,eACtC,UAAO,CAAC,KAAK,CAAC,CAAC,EAAE,EAAO,wHAAe,CAAC,EAC1C,CACF,EAEM,EAAmB,MAAO,IAC9B,GAAI,CACF,QAAQ,GAAG,CAAC,2CAAwB,GAEpC,MAAM,GAAY,UAAU,CAAC,GAC7B,EAAiB,EAAc,MAAM,CAAC,AAAC,GAAS,EAAK,EAAE,GAAK,IAG5D,GAAI,CACF,IAAM,EAAQ,MAAM,GAAY,YAAY,GAC5C,EAAa,GACf,CAAE,MAAO,EAAY,CACnB,QAAQ,KAAK,CAAC,oDAAa,GAE7B,CAEA,UAAO,CAAC,OAAO,CAAC,wCAClB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wCAAW,GACzB,UAAO,CAAC,KAAK,CAAC,4EAChB,CACF,EAEA,MACE,WAAC,SAAI,EACH,UAAU,iBACV,MAAO,CACL,aAAc,GACd,UAAW,8BACX,OAAQ,OACR,WAAY,2CACd,EACA,MACE,UAAC,IAAK,QAAQ,gBAAgB,MAAM,kBAClC,UAAC,IAAK,MAAM,aAAC,yCAKjB,UAAC,OACC,MAAO,CACL,aAAc,GACd,QAAS,YACT,WAAY,UACZ,aAAc,EACd,OAAQ,mBACV,WAGA,WAAC,SAAG,EAAC,OAAQ,CAAC,GAAI,GAAG,CAAE,MAAM,mBAE3B,UAAC,SAAG,EAAC,GAAI,GAAI,GAAI,GAAI,GAAI,EAAG,GAAI,EAAG,GAAI,WACrC,WAAC,IAAK,MAAM,SAAS,IAAK,GAAI,MAAO,CAAE,MAAO,MAAO,YACnD,UAAC,UAAK,CAAC,MAAM,EACX,YAAY,8BACZ,UAAU,IACV,OAAQ,UAAC,UAAc,KACvB,MAAO,EACP,SAAU,AAAC,GAAM,EAAc,EAAE,MAAM,CAAC,KAAK,EAC7C,MAAO,CAAE,KAAM,CAAE,EACjB,KAAK,WAGP,UAAC,SAAM,EACL,KAAK,UACL,KAAM,UAAC,SAAY,KACnB,QAAS,KACP,EAAiB,MACjB,EAAS,WAAW,GACpB,EAAoB,CAAA,GACtB,EACA,MAAO,CACL,WAAY,UACZ,YAAa,UACb,UAAW,oCACX,WAAY,IACZ,SAAU,EACZ,EACA,KAAK,kBACN,sBAOL,UAAC,SAAG,EAAC,GAAI,GAAI,GAAI,GAAI,GAAI,EAAG,GAAI,EAAG,GAAI,WACrC,UAAC,IAAK,MAAM,SAAS,QAAQ,SAAS,KAAK,gBACzC,WAAC,UAAK,EAAC,KAAM,GAAI,IAAI,cACnB,UAAC,UAAO,EACN,MAAO,CAAC,kDAAQ,EAAE,EAAU,iBAAiB,CAAC,QAAC,CAAC,UAEhD,WAAC,IAAK,MAAM,SAAS,IAAK,YACxB,UAAC,OACC,MAAO,CACL,MAAO,EACP,OAAQ,EACR,aAAc,MACd,WAAY,SACd,IAEF,WAAC,IACC,MAAO,CACL,SAAU,GACV,WAAY,IACZ,MAAO,SACT,YACD,WACK,EAAU,iBAAiB,SAKrC,UAAC,UAAO,EACN,MAAO,CAAC,kDAAQ,EAAE,EAAU,mBAAmB,CAAC,QAAC,CAAC,UAElD,WAAC,IAAK,MAAM,SAAS,IAAK,YACxB,UAAC,OACC,MAAO,CACL,MAAO,EACP,OAAQ,EACR,aAAc,MACd,WAAY,SACd,IAEF,WAAC,IACC,MAAO,CACL,SAAU,GACV,WAAY,IACZ,MAAO,SACT,YACD,WACK,EAAU,mBAAmB,SAKvC,UAAC,UAAO,EACN,MAAO,CAAC,kDAAQ,EAAE,EAAU,gBAAgB,CAAC,QAAC,CAAC,UAE/C,WAAC,IAAK,MAAM,SAAS,IAAK,YACxB,UAAC,OACC,MAAO,CACL,MAAO,EACP,OAAQ,EACR,aAAc,MACd,WAAY,SACd,IAEF,WAAC,IACC,MAAO,CACL,SAAU,GACV,WAAY,IACZ,MAAO,SACT,YACD,WACK,EAAU,gBAAgB,gBAS1C,UAAC,SAAG,EAAC,GAAI,GAAI,GAAI,GAAI,GAAI,EAAG,GAAI,EAAG,GAAI,WACrC,UAAC,IAAK,MAAM,SAAS,QAAQ,kBAC3B,UAAC,UAAO,EACN,MAAO,CAAC,0BAAK,EAAE,EAAU,oBAAoB,CAAC,GAAG,EAAE,EAAU,cAAc,CAAC,CAAC,EAAE,EAAU,UAAU,CAAC,CAAC,CAAC,UAEtG,WAAC,IAAK,MAAM,SAAS,IAAK,YACxB,UAAC,IACC,MAAO,CAAE,SAAU,GAAI,WAAY,IAAK,MAAO,SAAU,WAC1D,wBAGD,UAAC,UAAQ,EACP,QAAS,EAAU,oBAAoB,CACvC,KAAK,QACL,MAAO,CAAE,MAAO,EAAG,EACnB,YAAY,UACZ,SAAU,CAAA,IAEZ,WAAC,IACC,MAAO,CAAE,SAAU,GAAI,WAAY,IAAK,MAAO,SAAU,YAExD,EAAU,oBAAoB,CAAC,qBAU9C,WAAC,UAAI,EACH,UAAW,EACX,SAAU,AAAC,GAAQ,EAAa,GAChC,KAAK,SACL,MAAO,CAAE,aAAc,CAAE,YAEzB,UAAC,IAAQ,IAAI,gBAAS,OACtB,UAAC,IAAQ,IAAI,sBAAU,WACvB,UAAC,IAAQ,IAAI,sBAAU,gBAIxB,EACC,UAAC,SAAK,EACJ,QAAQ,2CACR,YAAa,EACb,KAAK,QACL,QAAQ,IACR,MAAO,CAAE,aAAc,EAAG,IAG5B,WAAC,SAAI,EAAC,SAAU,YACd,UAAC,UAAI,EACH,WAAY,EACZ,WAAY,AAAC,GAET,UAAC,UAAI,CAAC,IAAI,EACR,UAAU,YACV,MAAO,CACL,QAAS,YACT,aAAc,GACd,aAAc,EACd,WAAY,OACZ,QAAS,AAAgB,IAAhB,EAAK,MAAM,CAAS,GAAM,EACnC,WAAY,CAAC,UAAU,EACrB,AAAgB,IAAhB,EAAK,MAAM,CACP,UACA,AAAkB,IAAlB,EAAK,QAAQ,CACX,UACA,AAAkB,IAAlB,EAAK,QAAQ,CACX,UACA,UACT,CAAC,CACF,UAAW,4BACb,WAEA,WAAC,IAAK,MAAM,SAAS,IAAK,GAAI,MAAO,CAAE,MAAO,MAAO,YAEnD,WAAC,IAAK,QAAQ,IAAC,MAAM,mBAClB,AAAgB,IAAhB,EAAK,MAAM,CACV,UAAC,IACC,MAAM,SACN,QAAQ,SACR,MAAO,CACL,MAAO,GACP,OAAQ,GACR,aAAc,MACd,WAAY,SACd,WAEA,UAAC,UAAa,EACZ,MAAO,CAAE,MAAO,OAAQ,SAAU,EAAG,MAIzC,UAAC,OACC,MAAO,CACL,MAAO,GACP,OAAQ,GACR,aAAc,MACd,OAAQ,CAAC,UAAU,EACjB,AAAkB,IAAlB,EAAK,QAAQ,CACT,UACA,AAAkB,IAAlB,EAAK,QAAQ,CACX,UACA,UACP,CAAC,AACJ,IAIJ,UAAC,OACC,MAAO,CACL,MAAO,EACP,OAAQ,GACR,WAAY,UACZ,UAAW,CACb,OAKJ,WAAC,IAAK,QAAQ,IAAC,MAAO,CAAE,KAAM,CAAE,YAC9B,UAAC,IACC,MAAO,CACL,SAAU,GACV,WAAY,AAAkB,IAAlB,EAAK,QAAQ,CAAS,IAAM,SACxC,eACE,AAAgB,IAAhB,EAAK,MAAM,CAAS,eAAiB,OACvC,MAAO,AAAgB,IAAhB,EAAK,MAAM,CAAS,UAAY,SACzC,WAEC,EAAK,KAAK,GAIb,WAAC,UAAK,EAAC,MAAM,SAAS,KAAM,EAAG,MAAO,CAAE,UAAW,CAAE,YACnD,UAAC,UAAgB,EACf,MAAO,CACL,SAAU,GACV,MAAO,SACT,IAEF,WAAC,IAAK,KAAK,YAAY,MAAO,CAAE,SAAU,EAAG,YAAG,sBACzC,IACJ,IAAI,KAAK,EAAK,SAAS,EAAE,kBAAkB,CAAC,kBAMnD,UAAC,UAAQ,EACP,QAAS,CAAC,QAAQ,CAClB,KAAM,CACJ,MAAO,CACL,CACE,IAAK,WACL,MACE,AAAgB,IAAhB,EAAK,MAAM,CAAS,iCAAU,2BAChC,KACE,UAAC,UAAa,EACZ,MAAO,CACL,MACE,AAAgB,IAAhB,EAAK,MAAM,CAAS,UAAY,UAClC,SAAU,EACZ,GAGN,EACA,CACE,IAAK,OACL,MAAO,2BACP,KAAM,UAAC,UAAY,EAAC,MAAO,CAAE,MAAO,SAAU,GAChD,EACA,CACE,IAAK,SACL,MAAO,2BACP,KACE,UAAC,UAAc,EAAC,MAAO,CAAE,MAAO,SAAU,IAE5C,OAAQ,CAAA,CACV,EACD,CACD,QAAS,CAAC,CAAE,IAAA,CAAG,CAAE,IACX,AAAQ,aAAR,EACF,EAAuB,EAAK,EAAE,EACrB,AAAQ,SAAR,GACT,EAAiB,EAAK,EAAE,EACxB,EAAS,cAAc,CAAC,CACtB,KAAM,EAAK,KAAK,CAChB,SAAU,EAAK,QAAQ,AACzB,GACA,EAAoB,CAAA,IACH,WAAR,GACT,EAAiB,EAAK,EAAE,EAE5B,CACF,WAEA,UAAC,SAAM,EACL,KAAK,OACL,KAAK,QACL,KAAM,UAAC,UAAY,KACnB,MAAO,CAAE,MAAO,GAAI,OAAQ,EAAG,aAU7C,UAAC,UAAK,EACJ,MAAO,EAAgB,uCAAW,uCAClC,KAAM,EACN,SAAU,KACR,EAAoB,CAAA,GACpB,EAAS,WAAW,GACtB,EACA,KAAM,KACJ,EAAS,MAAM,GACjB,EACA,QAAQ,IACR,cAAc,IACd,OAAQ,CACN,UAAC,SAAM,EAAc,QAAS,IAAM,EAAoB,CAAA,YAAQ,gBAApD,UAGZ,UAAC,SAAM,EAEL,KAAK,UACL,QAAS,KACP,EAAS,MAAM,GACjB,EACA,MAAO,CACL,WAAY,UACZ,YAAa,UACb,UAAW,mCACb,WAEC,EAAgB,2BAAS,4BAXtB,UAaP,UAED,WAAC,UAAI,EACH,KAAM,EACN,OAAO,WACP,SAAU,EACV,aAAa,gBAEb,UAAC,UAAI,CAAC,IAAI,EACR,KAAK,OACL,MAAM,2BACN,MAAO,CAAC,CAAE,SAAU,CAAA,EAAM,QAAS,4CAAU,EAAE,UAE/C,UAAC,UAAK,EACJ,YAAY,6CACZ,KAAK,QACL,MAAO,CAAE,aAAc,CAAE,MAI7B,UAAC,UAAI,CAAC,IAAI,EACR,KAAK,WACL,MAAM,qBACN,aAAc,EACd,MAAO,CAAC,CAAE,SAAU,CAAA,EAAM,QAAS,sCAAS,EAAE,UAE9C,UAAC,UAAM,EACL,KAAK,QACL,QAAS,CACP,CAAE,MAAO,EAAG,MAAO,0BAAO,EAC1B,CAAE,MAAO,EAAG,MAAO,0BAAO,EAC1B,CAAE,MAAO,EAAG,MAAO,0BAAO,EAC3B,CACD,MAAO,CAAE,aAAc,CAAE,iBAS3C,EC1pBA,IAAI,GAAmB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,OAAQ,EAAG,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,kdAAmd,CAAE,EAAE,AAAC,EAAG,KAAQ,YAAa,MAAS,UAAW,ECc9pB,GAAuB,EAAM,UAAU,CARpB,SAA0B,CAAK,CAAE,CAAG,EACzD,OAAoB,EAAM,aAAa,CAAC,SAAQ,CAAE,GAAA,SAAQ,EAAC,CAAC,EAAG,EAAO,CACpE,IAAK,EACL,KAAM,EACR,IACF,wGCYA,GAAM,CAAE,MAAA,EAAK,CAAE,KAAA,EAAI,CAAE,CAAG,UAAU,CAE5B,GAA4B,KAEhC,GAAM,CAAC,EAAU,EAAY,CAAG,GAAA,UAAQ,EAA4B,CAClE,KAAM,GACN,SAAU,GACV,MAAO,GACP,UAAW,GACX,aAAc,GACd,cAAe,GACf,cAAe,GACf,UAAW,EACX,OAAQ,EACV,GACM,CAAC,EAAiB,EAAmB,CAAG,GAAA,UAAQ,EAAC,CAAA,GACjD,CAAC,EAAe,EAAiB,CAAG,GAAA,UAAQ,EAAgB,MAG5D,CAAC,EAAe,EAAiB,CAAG,GAAA,UAAQ,EAChD,CACE,SAAU,EACV,UAAW,EACX,SAAU,EACV,OAAQ,CACV,GAEI,CAAC,EAAc,EAAgB,CAAG,GAAA,UAAQ,EAAC,CAAA,GAC3C,CAAC,EAAY,EAAc,CAAG,GAAA,UAAQ,EAAgB,MA0D5D,MArDA,GAAA,WAAS,EAAC,KACR,QAAQ,GAAG,CAAC,uDAiDZ,AA/CsB,CAAA,UACpB,GAAI,CACF,QAAQ,GAAG,CAAC,qEAGZ,IAAM,EAAoB,cAAW,CAAC,oBAAoB,GAAG,KAAK,CAChE,AAAC,IACC,QAAQ,KAAK,CAAC,gEAAe,GAC7B,EAAiB,oGACV,OAIL,EAAe,cAAW,CAAC,oBAAoB,GAAG,KAAK,CAC3D,AAAC,IACC,QAAQ,KAAK,CAAC,oDAAa,GAC3B,EAAc,wFACP,OAIL,CAAC,EAAY,EAAM,CAAG,MAAM,QAAQ,GAAG,CAAC,CAC5C,EACA,EACD,EAEG,IACF,QAAQ,GAAG,CAAC,2EAA+B,GAC3C,EAAY,GACZ,EAAiB,OAGf,IACF,QAAQ,GAAG,CAAC,+DAA6B,GACzC,EAAiB,GACjB,EAAc,OAElB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,kFAAkB,GAChC,EAAiB,oGACjB,EAAc,oGAChB,QAAU,CACR,EAAmB,CAAA,GACnB,EAAgB,CAAA,GAClB,CACF,CAAA,IAGF,EAAG,EAAE,EAGH,+BAEG,EACC,UAAC,SAAK,EACJ,QAAQ,mDACR,YAAa,EACb,KAAK,QACL,QAAQ,IACR,MAAO,CAAE,aAAc,EAAG,IAG5B,UAAC,SAAI,EAAC,SAAU,WAEd,WAAC,SAAI,EACH,MAAO,CACL,WAAY,oDACZ,aAAc,GACd,MAAO,QACP,SAAU,WACV,SAAU,SACV,UAAW,IACX,OAAQ,MACV,EACA,OAAQ,CACN,KAAM,CACJ,QAAS,sBACT,OAAQ,MACV,CACF,YAGA,UAAC,OACC,MAAO,CACL,SAAU,WACV,IAAK,IACL,MAAO,IACP,MAAO,IACP,OAAQ,IACR,WAAY,wBACZ,aAAc,KAChB,IAEF,UAAC,OACC,MAAO,CACL,SAAU,WACV,OAAQ,IACR,KAAM,IACN,MAAO,GACP,OAAQ,GACR,WAAY,yBACZ,aAAc,KAChB,IAEF,UAAC,OACC,MAAO,CACL,SAAU,WACV,IAAK,MACL,MAAO,MACP,MAAO,GACP,OAAQ,GACR,WAAY,yBACZ,aAAc,MACd,UAAW,kBACb,IAIF,WAAC,SAAG,EACF,OAAQ,CAAC,GAAI,GAAG,CAChB,MAAM,SACN,MAAO,CACL,SAAU,WACV,OAAQ,EACR,MAAO,OACP,UAAW,MACb,YAGA,UAAC,SAAG,EAAC,GAAI,GAAI,GAAI,GAAI,GAAI,EAAG,GAAI,EAAG,GAAI,WACrC,WAAC,IAAK,MAAM,SAAS,MAAO,CAAE,UAAW,MAAO,YAE9C,UAAC,UAAM,EACL,KAAM,GACN,MAAM,SACN,MAAO,CACL,gBAAiB,wBACjB,YAAa,GACb,SAAU,GACV,WAAY,IACZ,OAAQ,iCACV,WAEC,EAAS,IAAI,CACZ,EAAS,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,GAEnC,UAAC,SAAY,OAKjB,WAAC,UAAK,EAAC,UAAU,WAAW,KAAM,YAChC,UAAC,IACC,MAAO,EACP,MAAO,CACL,OAAQ,EACR,MAAO,QACP,SAAU,GACV,WAAY,GACd,WAEC,EAAS,IAAI,EAAI,0BAIpB,WAAC,UAAK,EAAC,UAAU,WAAW,KAAM,YAC/B,EAAS,KAAK,EACb,WAAC,UAAK,EAAC,KAAM,EAAG,MAAM,mBACpB,UAAC,UAAY,EACX,MAAO,CACL,SAAU,GACV,MAAO,uBACT,IAEF,UAAC,IACC,MAAO,CACL,MAAO,wBACP,SAAU,EACZ,WAEC,EAAS,KAAK,MAIpB,EAAS,SAAS,EACjB,WAAC,UAAK,EAAC,KAAM,EAAG,MAAM,mBACpB,UAAC,UAAa,EACZ,MAAO,CACL,SAAU,GACV,MAAO,uBACT,IAEF,UAAC,IACC,MAAO,CACL,MAAO,wBACP,SAAU,EACZ,WAEC,EAAS,SAAS,SAO1B,EAAS,YAAY,EACpB,WAAC,IACC,MAAO,CACL,SAAU,GACV,MAAO,wBACP,WAAY,GACd,YACD,sBACM,EAAS,YAAY,YAQpC,UAAC,SAAG,EAAC,GAAI,GAAI,GAAI,GAAI,GAAI,EAAG,GAAI,GAAI,GAAI,YACtC,WAAC,IACC,QAAQ,IACR,QAAQ,SACR,MAAO,CACL,UAAW,OACX,UAAW,SACX,QAAS,OACX,YAGA,WAAC,UAAK,EACJ,MAAM,SACN,MAAO,CACL,eAAgB,SAChB,aAAc,EAChB,YAEA,UAAC,IACC,MAAO,CACL,SAAU,GACV,MAAO,uBACT,IAEF,UAAC,IACC,MAAO,CACL,MAAO,wBACP,SAAU,GACV,WAAY,GACd,WACD,gCAMF,EACC,UAAC,IACC,MAAO,CAAE,SAAU,GAAI,MAAO,uBAAwB,WACvD,yCAID,UAAC,SAAI,EAAC,SAAU,WACd,WAAC,SAAG,EAAC,OAAQ,CAAC,EAAG,EAAE,CAAE,QAAQ,mBAC3B,UAAC,SAAG,EAAC,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,WACnC,WAAC,OAAI,MAAO,CAAE,UAAW,QAAS,YAChC,UAAC,OACC,MAAO,CACL,SAAU,GACV,WAAY,IACZ,MAAO,QACP,WAAY,CACd,WAEC,EAAc,QAAQ,GAEzB,UAAC,OACC,MAAO,CACL,SAAU,GACV,MAAO,wBACP,UAAW,CACb,WACD,sBAKL,UAAC,SAAG,EAAC,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,WACnC,WAAC,OAAI,MAAO,CAAE,UAAW,QAAS,YAChC,UAAC,OACC,MAAO,CACL,SAAU,GACV,WAAY,IACZ,MAAO,QACP,WAAY,CACd,WAEC,EAAc,SAAS,GAE1B,UAAC,OACC,MAAO,CACL,SAAU,GACV,MAAO,wBACP,UAAW,CACb,WACD,sBAKL,UAAC,SAAG,EAAC,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,WACnC,WAAC,OAAI,MAAO,CAAE,UAAW,QAAS,YAChC,UAAC,OACC,MAAO,CACL,SAAU,GACV,WAAY,IACZ,MAAO,QACP,WAAY,CACd,WAEC,EAAc,QAAQ,GAEzB,UAAC,OACC,MAAO,CACL,SAAU,GACV,MAAO,wBACP,UAAW,CACb,WACD,sBAKL,UAAC,SAAG,EAAC,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,WACnC,WAAC,OAAI,MAAO,CAAE,UAAW,QAAS,YAChC,UAAC,OACC,MAAO,CACL,SAAU,GACV,WAAY,IACZ,MAAO,QACP,WAAY,CACd,WAEC,EAAc,MAAM,GAEvB,UAAC,OACC,MAAO,CACL,SAAU,GACV,MAAO,wBACP,UAAW,CACb,WACD,gCAYf,UAAC,SAAG,EAAC,GAAI,GAAI,GAAI,GAAI,GAAI,EAAG,GAAI,EAAG,GAAI,WACrC,UAAC,IACC,QAAQ,IACR,QAAQ,SACR,MAAO,CAAE,UAAW,OAAQ,QAAS,OAAQ,WAE7C,WAAC,UAAK,EAAC,UAAU,WAAW,KAAM,aAChC,WAAC,UAAK,EAAC,UAAU,WAAW,KAAM,YAChC,UAAC,IACC,MAAO,CACL,SAAU,GACV,MAAO,wBACP,WAAY,GACd,WACD,yCAGD,UAAC,IACC,MAAO,CACL,SAAU,GACV,MAAO,QACP,WAAY,IACZ,WAAY,GACd,WAEC,EAAS,aAAa,EAAI,gCAG/B,WAAC,UAAK,EAAC,UAAU,WAAW,KAAM,YAChC,UAAC,IACC,MAAO,CACL,SAAU,GACV,MAAO,wBACP,WAAY,GACd,WACD,yCAGD,UAAC,IACC,MAAO,CACL,SAAU,GACV,MAAO,QACP,WAAY,IACZ,WAAY,GACd,WAEC,EAAS,aAAa,EAAI,iDAYnD,EC3dM,GAA+B,KACnC,GAAM,CAAE,aAAA,CAAY,CAAE,QAAA,CAAO,CAAE,CAAG,GAAA,UAAQ,EAAC,yBAG3C,AAAI,EAEA,WAAC,OACC,MAAO,CACL,UAAW,QACX,WAAY,UACZ,QAAS,OACT,eAAgB,SAChB,WAAY,QACd,YAEA,UAAC,SAAI,EAAC,KAAK,UACX,UAAC,OAAI,MAAO,CAAE,WAAY,EAAG,WAAG,4DAMtC,GAAA,WAAS,EAAC,KACH,UAAY,SAAA,EAAc,WAAW,GACxC,SAAO,CAAC,IAAI,CAAC,eAEjB,EAAG,CAAC,QAAS,SAAA,EAAc,WAAW,CAAC,EAGrC,iCACE,UAAC,OACC,MAAO,CACL,UAAW,QACX,WAAY,UACZ,QAAS,qBACX,WAGA,UAAC,SAAI,EACH,MAAO,CACL,MAAO,OACP,UAAW,qBACX,aAAc,OACd,UAAW,+BACb,EACA,OAAQ,CACN,KAAM,CACJ,QAAS,MACX,CACF,WAEF,WAAC,SAAG,EAAC,OAAQ,CAAC,GAAI,GAAG,CAAE,MAAO,CAAE,OAAQ,CAAE,YAExC,UAAC,SAAG,EAAC,GAAI,GAAI,MAAO,CAAE,aAAc,CAAE,WACpC,UAAC,SAIH,UAAC,SAAG,EACF,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,IAAK,GACL,MAAO,CAAE,aAAc,CAAE,WAEzB,UAAC,SAIH,UAAC,SAAG,EAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,YAChD,UAAC,gBAOT,UAAC,SAAe,SAGpB"}