{"version": 3, "sources": ["src/pages/user/invitations/index.tsx"], "sourcesContent": ["/**\n * 用户邀请页面\n * \n * 功能特性：\n * - 显示用户收到的所有邀请\n * - 支持接受或拒绝邀请\n * - 显示邀请详情和团队信息\n * - 自动刷新待处理邀请\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  message,\n  Modal,\n  Form,\n  Input,\n  Typography,\n  Tag,\n  Tooltip,\n  Avatar,\n} from 'antd';\nimport {\n  CheckOutlined,\n  CloseOutlined,\n  TeamOutlined,\n  UserOutlined,\n  ReloadOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport dayjs from 'dayjs';\n\n// 导入服务和类型\nimport { InvitationService } from '@/services';\nimport type { TeamInvitationResponse, RespondInvitationRequest } from '@/types/api';\nimport { InvitationStatus } from '@/types/api';\nimport InvitationStatusComponent from '@/components/InvitationStatus';\n\nconst { Text, Title } = Typography;\nconst { TextArea } = Input;\n\nconst UserInvitationsPage: React.FC = () => {\n  const [invitations, setInvitations] = useState<TeamInvitationResponse[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [respondModalVisible, setRespondModalVisible] = useState(false);\n  const [selectedInvitation, setSelectedInvitation] = useState<TeamInvitationResponse | null>(null);\n  const [respondForm] = Form.useForm();\n\n  // 获取邀请列表\n  const fetchInvitations = async () => {\n    try {\n      setLoading(true);\n      const invitationList = await InvitationService.getUserReceivedInvitations();\n      setInvitations(invitationList || []);\n    } catch (error) {\n      console.error('获取邀请列表失败:', error);\n      message.error('获取邀请列表失败');\n      setInvitations([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchInvitations();\n  }, []);\n\n  // 响应邀请\n  const handleRespondInvitation = async (invitation: TeamInvitationResponse, accept: boolean) => {\n    setSelectedInvitation(invitation);\n    respondForm.setFieldsValue({ accept });\n    setRespondModalVisible(true);\n  };\n\n  // 提交响应\n  const handleSubmitResponse = async (values: { accept: boolean; message?: string }) => {\n    if (!selectedInvitation) return;\n\n    try {\n      const request: RespondInvitationRequest = {\n        accept: values.accept,\n        message: values.message,\n      };\n\n      await InvitationService.respondToInvitation(selectedInvitation.id, request);\n      \n      const action = values.accept ? '接受' : '拒绝';\n      message.success(`邀请${action}成功`);\n      \n      setRespondModalVisible(false);\n      respondForm.resetFields();\n      setSelectedInvitation(null);\n      fetchInvitations();\n    } catch (error) {\n      console.error('响应邀请失败:', error);\n      message.error('响应邀请失败');\n    }\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<TeamInvitationResponse> = [\n    {\n      title: '团队信息',\n      key: 'team',\n      render: (_, record) => (\n        <Space>\n          <Avatar icon={<TeamOutlined />} />\n          <div>\n            <Text strong>{record.teamName}</Text>\n            <br />\n            <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n              团队ID: {record.teamId}\n            </Text>\n          </div>\n        </Space>\n      ),\n    },\n    {\n      title: '邀请人',\n      key: 'inviter',\n      render: (_, record) => (\n        <Space>\n          <Avatar icon={<UserOutlined />} />\n          <div>\n            <Text strong>{record.inviterName}</Text>\n            <br />\n            <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n              {record.inviterEmail}\n            </Text>\n          </div>\n        </Space>\n      ),\n    },\n    {\n      title: '邀请状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status, record) => (\n        <InvitationStatusComponent \n          status={status} \n          isExpired={record.isExpired} \n        />\n      ),\n    },\n    {\n      title: '邀请时间',\n      dataIndex: 'invitedAt',\n      key: 'invitedAt',\n      render: (time) => (\n        <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>\n          {dayjs(time).format('MM-DD HH:mm')}\n        </Tooltip>\n      ),\n      sorter: (a, b) => dayjs(a.invitedAt).unix() - dayjs(b.invitedAt).unix(),\n      defaultSortOrder: 'descend',\n    },\n    {\n      title: '过期时间',\n      dataIndex: 'expiresAt',\n      key: 'expiresAt',\n      render: (time, record) => {\n        const isExpired = record.isExpired;\n        return (\n          <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>\n            <Text type={isExpired ? 'danger' : 'secondary'}>\n              {dayjs(time).format('MM-DD HH:mm')}\n            </Text>\n          </Tooltip>\n        );\n      },\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space>\n          {record.canBeResponded && (\n            <>\n              <Button\n                type=\"primary\"\n                size=\"small\"\n                icon={<CheckOutlined />}\n                onClick={() => handleRespondInvitation(record, true)}\n              >\n                接受\n              </Button>\n              <Button\n                size=\"small\"\n                icon={<CloseOutlined />}\n                onClick={() => handleRespondInvitation(record, false)}\n              >\n                拒绝\n              </Button>\n            </>\n          )}\n        </Space>\n      ),\n    },\n  ];\n\n  // 统计信息\n  const pendingCount = invitations.filter(inv => inv.status === InvitationStatus.PENDING && !inv.isExpired).length;\n  const acceptedCount = invitations.filter(inv => inv.status === InvitationStatus.ACCEPTED).length;\n  const rejectedCount = invitations.filter(inv => inv.status === InvitationStatus.REJECTED).length;\n\n  return (\n    <PageContainer\n      title=\"我的邀请\"\n      subTitle=\"管理您收到的团队邀请\"\n      extra={[\n        <Button \n          key=\"refresh\"\n          icon={<ReloadOutlined />} \n          onClick={fetchInvitations}\n          loading={loading}\n        >\n          刷新\n        </Button>\n      ]}\n    >\n      {/* 统计卡片 */}\n      <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n        <Card>\n          <Space size=\"large\">\n            <div>\n              <Text type=\"secondary\">待处理邀请</Text>\n              <br />\n              <Text style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>\n                {pendingCount}\n              </Text>\n            </div>\n            <div>\n              <Text type=\"secondary\">已接受</Text>\n              <br />\n              <Text style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>\n                {acceptedCount}\n              </Text>\n            </div>\n            <div>\n              <Text type=\"secondary\">已拒绝</Text>\n              <br />\n              <Text style={{ fontSize: '24px', fontWeight: 'bold', color: '#ff4d4f' }}>\n                {rejectedCount}\n              </Text>\n            </div>\n          </Space>\n        </Card>\n\n        {/* 邀请列表 */}\n        <Card title=\"邀请列表\">\n          <Table\n            columns={columns}\n            dataSource={invitations}\n            rowKey=\"id\"\n            loading={loading}\n            pagination={{\n              showSizeChanger: true,\n              showQuickJumper: true,\n              showTotal: (total) => `共 ${total} 条邀请记录`,\n              pageSize: 10,\n            }}\n          />\n        </Card>\n      </Space>\n\n      {/* 响应邀请弹窗 */}\n      <Modal\n        title={`${respondForm.getFieldValue('accept') ? '接受' : '拒绝'}邀请`}\n        open={respondModalVisible}\n        onCancel={() => {\n          setRespondModalVisible(false);\n          respondForm.resetFields();\n          setSelectedInvitation(null);\n        }}\n        footer={null}\n        width={500}\n      >\n        {selectedInvitation && (\n          <div style={{ marginBottom: 16 }}>\n            <Text strong>团队：</Text> {selectedInvitation.teamName}\n            <br />\n            <Text strong>邀请人：</Text> {selectedInvitation.inviterName}\n            <br />\n            {selectedInvitation.message && (\n              <>\n                <Text strong>邀请消息：</Text>\n                <div style={{ \n                  background: '#f5f5f5', \n                  padding: 8, \n                  borderRadius: 4, \n                  marginTop: 4 \n                }}>\n                  {selectedInvitation.message}\n                </div>\n              </>\n            )}\n          </div>\n        )}\n        \n        <Form\n          form={respondForm}\n          layout=\"vertical\"\n          onFinish={handleSubmitResponse}\n        >\n          <Form.Item name=\"accept\" hidden>\n            <Input />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"message\"\n            label=\"回复消息（可选）\"\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"您可以添加一些回复消息...\"\n              maxLength={200}\n            />\n          </Form.Item>\n          \n          <Form.Item>\n            <Space>\n              <Button \n                type=\"primary\" \n                htmlType=\"submit\"\n                icon={respondForm.getFieldValue('accept') ? <CheckOutlined /> : <CloseOutlined />}\n              >\n                确认{respondForm.getFieldValue('accept') ? '接受' : '拒绝'}\n              </Button>\n              <Button onClick={() => setRespondModalVisible(false)}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </PageContainer>\n  );\n};\n\nexport default UserInvitationsPage;\n"], "names": [], "mappings": "uQAuVA,+CAAA,8CA7U2C,oXAwBzB,iBAGgB,gBAED,oBACK,aAEtC,GAAM,CAAE,KAAA,CAAI,CAAE,MAAA,CAAK,CAAE,CAAG,SAAU,CAC5B,CAAE,SAAA,CAAQ,CAAE,CAAG,SAAK,KA4S1B,EA1SsC,KACpC,GAAM,CAAC,EAAa,EAAe,CAAG,GAAA,UAAQ,EAA2B,EAAE,EACrE,CAAC,EAAS,EAAW,CAAG,GAAA,UAAQ,EAAC,CAAA,GACjC,CAAC,EAAqB,EAAuB,CAAG,GAAA,UAAQ,EAAC,CAAA,GACzD,CAAC,EAAoB,EAAsB,CAAG,GAAA,UAAQ,EAAgC,MACtF,CAAC,EAAY,CAAG,SAAI,CAAC,OAAO,GAG5B,EAAmB,UACvB,GAAI,CACF,EAAW,CAAA,GACX,IAAM,EAAiB,MAAM,mBAAiB,CAAC,0BAA0B,GACzE,EAAe,GAAkB,EAAE,EACrC,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,oDAAa,GAC3B,SAAO,CAAC,KAAK,CAAC,oDACd,EAAe,EAAE,EACnB,QAAU,CACR,EAAW,CAAA,GACb,CACF,EAEA,GAAA,WAAS,EAAC,KACR,IACF,EAAG,EAAE,EAGL,IAAM,EAA0B,MAAO,EAAoC,KACzE,EAAsB,GACtB,EAAY,cAAc,CAAC,CAAE,OAAA,CAAO,GACpC,EAAuB,CAAA,GACzB,EAGM,EAAuB,MAAO,IAClC,GAAK,EAEL,GAAI,CACF,IAAM,EAAoC,CACxC,OAAQ,EAAO,MAAM,CACrB,QAAS,EAAO,OAAO,AACzB,EAEA,MAAM,mBAAiB,CAAC,mBAAmB,CAAC,EAAmB,EAAE,CAAE,GAEnE,IAAM,EAAS,EAAO,MAAM,CAAG,eAAO,eACtC,SAAO,CAAC,OAAO,CAAC,CAAC,gBAAE,EAAE,EAAO,gBAAE,CAAC,EAE/B,EAAuB,CAAA,GACvB,EAAY,WAAW,GACvB,EAAsB,MACtB,IACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wCAAW,GACzB,SAAO,CAAC,KAAK,CAAC,wCAChB,CACF,EAGM,EAA+C,CACnD,CACE,MAAO,2BACP,IAAK,OACL,OAAQ,CAAC,EAAG,IACV,WAAC,SAAK,YACJ,UAAC,SAAM,EAAC,KAAM,UAAC,SAAY,OAC3B,WAAC,iBACC,UAAC,GAAK,MAAM,aAAE,EAAO,QAAQ,GAC7B,UAAC,SACD,WAAC,GAAK,KAAK,YAAY,MAAO,CAAE,SAAU,MAAO,YAAG,mBAC3C,EAAO,MAAM,SAK9B,EACA,CACE,MAAO,qBACP,IAAK,UACL,OAAQ,CAAC,EAAG,IACV,WAAC,SAAK,YACJ,UAAC,SAAM,EAAC,KAAM,UAAC,SAAY,OAC3B,WAAC,iBACC,UAAC,GAAK,MAAM,aAAE,EAAO,WAAW,GAChC,UAAC,SACD,UAAC,GAAK,KAAK,YAAY,MAAO,CAAE,SAAU,MAAO,WAC9C,EAAO,YAAY,QAK9B,EACA,CACE,MAAO,2BACP,UAAW,SACX,IAAK,SACL,OAAQ,CAAC,EAAQ,IACf,UAAC,SAAyB,EACxB,OAAQ,EACR,UAAW,EAAO,SAAS,EAGjC,EACA,CACE,MAAO,2BACP,UAAW,YACX,IAAK,YACL,OAAQ,AAAC,GACP,UAAC,SAAO,EAAC,MAAO,GAAA,SAAK,EAAC,GAAM,MAAM,CAAC,gCAChC,GAAA,SAAK,EAAC,GAAM,MAAM,CAAC,iBAGxB,OAAQ,CAAC,EAAG,IAAM,GAAA,SAAK,EAAC,EAAE,SAAS,EAAE,IAAI,GAAK,GAAA,SAAK,EAAC,EAAE,SAAS,EAAE,IAAI,GACrE,iBAAkB,SACpB,EACA,CACE,MAAO,2BACP,UAAW,YACX,IAAK,YACL,OAAQ,CAAC,EAAM,KACb,IAAM,EAAY,EAAO,SAAS,CAClC,MACE,UAAC,SAAO,EAAC,MAAO,GAAA,SAAK,EAAC,GAAM,MAAM,CAAC,gCACjC,UAAC,GAAK,KAAM,EAAY,SAAW,qBAChC,GAAA,SAAK,EAAC,GAAM,MAAM,CAAC,mBAI5B,CACF,EACA,CACE,MAAO,eACP,IAAK,SACL,OAAQ,CAAC,EAAG,IACV,UAAC,SAAK,WACH,EAAO,cAAc,EACpB,iCACE,UAAC,SAAM,EACL,KAAK,UACL,KAAK,QACL,KAAM,UAAC,SAAa,KACpB,QAAS,IAAM,EAAwB,EAAQ,CAAA,YAChD,iBAGD,UAAC,SAAM,EACL,KAAK,QACL,KAAM,UAAC,SAAa,KACpB,QAAS,IAAM,EAAwB,EAAQ,CAAA,YAChD,qBAOX,EACD,CAGK,EAAe,EAAY,MAAM,CAAC,GAAO,EAAI,MAAM,GAAK,kBAAgB,CAAC,OAAO,EAAI,CAAC,EAAI,SAAS,EAAE,MAAM,CAC1G,EAAgB,EAAY,MAAM,CAAC,GAAO,EAAI,MAAM,GAAK,kBAAgB,CAAC,QAAQ,EAAE,MAAM,CAC1F,EAAgB,EAAY,MAAM,CAAC,GAAO,EAAI,MAAM,GAAK,kBAAgB,CAAC,QAAQ,EAAE,MAAM,CAEhG,MACE,WAAC,eAAa,EACZ,MAAM,2BACN,SAAS,+DACT,MAAO,CACL,UAAC,SAAM,EAEL,KAAM,UAAC,SAAc,KACrB,QAAS,EACT,QAAS,WACV,gBAJK,WAOP,WAGD,WAAC,SAAK,EAAC,UAAU,WAAW,KAAK,QAAQ,MAAO,CAAE,MAAO,MAAO,YAC9D,UAAC,SAAI,WACH,WAAC,SAAK,EAAC,KAAK,kBACV,WAAC,iBACC,UAAC,GAAK,KAAK,qBAAY,mCACvB,UAAC,SACD,UAAC,GAAK,MAAO,CAAE,SAAU,OAAQ,WAAY,OAAQ,MAAO,SAAU,WACnE,OAGL,WAAC,iBACC,UAAC,GAAK,KAAK,qBAAY,uBACvB,UAAC,SACD,UAAC,GAAK,MAAO,CAAE,SAAU,OAAQ,WAAY,OAAQ,MAAO,SAAU,WACnE,OAGL,WAAC,iBACC,UAAC,GAAK,KAAK,qBAAY,uBACvB,UAAC,SACD,UAAC,GAAK,MAAO,CAAE,SAAU,OAAQ,WAAY,OAAQ,MAAO,SAAU,WACnE,YAOT,UAAC,SAAI,EAAC,MAAM,oCACV,UAAC,SAAK,EACJ,QAAS,EACT,WAAY,EACZ,OAAO,KACP,QAAS,EACT,WAAY,CACV,gBAAiB,CAAA,EACjB,gBAAiB,CAAA,EACjB,UAAW,AAAC,GAAU,CAAC,SAAE,EAAE,EAAM,yCAAM,CAAC,CACxC,SAAU,EACZ,SAMN,WAAC,SAAK,EACJ,MAAO,CAAC,EAAE,EAAY,aAAa,CAAC,UAAY,eAAO,eAAK,gBAAE,CAAC,CAC/D,KAAM,EACN,SAAU,KACR,EAAuB,CAAA,GACvB,EAAY,WAAW,GACvB,EAAsB,MACxB,EACA,OAAQ,KACR,MAAO,cAEN,GACC,WAAC,OAAI,MAAO,CAAE,aAAc,EAAG,YAC7B,UAAC,GAAK,MAAM,aAAC,uBAAU,IAAE,EAAmB,QAAQ,CACpD,UAAC,SACD,UAAC,GAAK,MAAM,aAAC,6BAAW,IAAE,EAAmB,WAAW,CACxD,UAAC,SACA,EAAmB,OAAO,EACzB,iCACE,UAAC,GAAK,MAAM,aAAC,mCACb,UAAC,OAAI,MAAO,CACV,WAAY,UACZ,QAAS,EACT,aAAc,EACd,UAAW,CACb,WACG,EAAmB,OAAO,SAOrC,WAAC,SAAI,EACH,KAAM,EACN,OAAO,WACP,SAAU,YAEV,UAAC,SAAI,CAAC,IAAI,EAAC,KAAK,SAAS,MAAM,aAC7B,UAAC,SAAK,OAGR,UAAC,SAAI,CAAC,IAAI,EACR,KAAK,UACL,MAAM,4DAEN,UAAC,GACC,KAAM,EACN,YAAY,wEACZ,UAAW,QAIf,UAAC,SAAI,CAAC,IAAI,WACR,WAAC,SAAK,YACJ,WAAC,SAAM,EACL,KAAK,UACL,SAAS,SACT,KAAM,EAAY,aAAa,CAAC,UAAY,UAAC,SAAa,KAAM,UAAC,SAAa,eAC/E,eACI,EAAY,aAAa,CAAC,UAAY,eAAO,kBAElD,UAAC,SAAM,EAAC,QAAS,IAAM,EAAuB,CAAA,YAAQ,+BASpE"}